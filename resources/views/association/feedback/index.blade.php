@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Feedback
            </x-slot>
            <x-slot name="section">
                (Diese <PERSON>n sind nur in dieser Testinstallation sichtbar)
            </x-slot>
        </x:header>
        <div class="flex flex-wrap -m-2">
            <div class="w-full sm:w-1/2 p-2">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                <svg class="h-6 w-6 text-white"><use href="/icons.svg#megaphone"/></svg>
                            </div>

                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                        Allgemeines Feedback
                                    </dt>
                                    <dd class="flex items-baseline">
                                        <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                            <a href="{{ route('associationFeedback.feedback.show') }}" class="text-red-500">Feedback geben</a>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x:content>
@endsection
