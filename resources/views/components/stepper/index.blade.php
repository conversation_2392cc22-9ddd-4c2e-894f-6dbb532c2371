@props([
    'currentStep',
    'totalSteps',
])

<div class="flex space-x-4 items-center">
		<span class="text-gray-700 text-sm"><PERSON><PERSON><PERSON> {{ $currentStep }} von {{ $totalSteps }}</span>
		<div class="flex space-x-3">
				@foreach(range(1, $totalSteps) as $step)
						@if($step < $currentStep)
								<x-stepper.indicator state="complete"/>
						@elseif($step == $currentStep)
								<x-stepper.indicator state="current"/>
						@else
								<x-stepper.indicator state="incomplete"/>
						@endif
				@endforeach
		</div>
</div>