@props([
    'state' // complete, current, incomplete
])

<div
	@class([
    'bg-red-50 border-red-600' => $state === 'complete',
    'bg-red-50 border-red-600 ring-[4px] ring-red-200' => $state === 'current',
    'bg-gary-200 border-gray-200' => $state === 'incomplete',
		'border-[1.5px] rounded-full w-6 h-6'
	])
>
		<div class="flex justify-center items-center w-full h-full">
				@if($state === 'complete')
						<svg width="13" height="11" class="text-red-600" viewBox="0 0 13 11" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M11.096 0.390037L3.93602 7.30004L2.03602 5.27004C1.68602 4.94004 1.13602 4.92004 0.736015 5.20004C0.346015 5.49004 0.236015 6.00004 0.476015 6.41004L2.72602 10.07C2.94602 10.41 3.32601 10.62 3.75601 10.62C4.16601 10.62 4.55602 10.41 4.77602 10.07C5.13602 9.60004 12.006 1.41004 12.006 1.41004C12.906 0.490037 11.816 -0.319963 11.096 0.380037V0.390037Z" fill="currentColor"/>
						</svg>
				@else
						<div
							@class([
						    'bg-red-600' => $state === 'current',
						    'bg-gray-200' => $state === 'incomplete',
								'w-2 h-2 rounded-full'
							])
							class="w-2 h-2 bg-white rounded-full"
						></div>
				@endif
		</div>
</div>