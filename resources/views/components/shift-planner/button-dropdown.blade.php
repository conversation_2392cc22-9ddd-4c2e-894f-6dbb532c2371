@props([
    'options' => []
])

<div class="flex">
    <x-button class="rounded-none rounded-l-md" {{ $attributes }}>{{ $slot }}</x-button>

    <div
        x-data = "{
                            open: false,
                            toggle() {
                                if (this.open) {
                                    return this.close()
                                }

                                this.$refs.button.focus()

                                this.open = true
                            },
                            close(focusAfter) {
                                if (!this.open) return

                                this.open = false

                                focusAfter && focusAfter.focus()
                            }
                        }"
        x-on:keydown.escape.prevent.stop="close($refs.button)"
        x-on:focusin.window="! $refs.panel.contains($event.target) && close()"
        x-id="['dropdown-button']"
        class="relative -ml-px block">
        <x-button
            x-ref="button"
            x-on:click="toggle()"
            ::aria-expanded="open"
            ::aria-controls="$id('dropdown-button')"
            type="button" class="rounded-none relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 focus:z-10" id="option-menu-button" aria-expanded="true" aria-haspopup="true">
            <span class="sr-only">Öffne Optionen</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
            </svg>
        </x-button>

        <div
            x-ref="panel"
            x-show="open"
            x-transition.origin.top.left
            x-on:click.outside="close($refs.button)"
            ::id="$id('dropdown-button')"
            style="display: none;"
            class="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="option-menu-button" tabindex="-1">
            <div class="py-1" role="none">
                @foreach ($options as $option)
                    <a
                        @if(isset($option['action']))
                            x-on:click="Livewire.dispatch('{{ $option['action'] }}', {{ $option['action_data'] }});"
                        @endif
                        @if(isset($option['link']))
                            href="{{ $option['link'] }}"
                        @if(isset($option['target']))
                            target="{{ $option['target'] }}"
                        @endif
                        @else
                            href="javascript:void(0)"
                        @endif
                        class="cursor-pointer w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                        role="menuitem"
                        tabindex="-1"
                        >
                        {{ $option['name'] }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
