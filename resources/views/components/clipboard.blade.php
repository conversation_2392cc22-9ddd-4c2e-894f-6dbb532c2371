@props([
    'textToBeCopied' => '',
])

<button
    class="tooltip-copied"
    @click="$clipboard('{{ $textToBeCopied }}').then(() => {
                        $event.view.tippy('.tooltip-copied', { showOnCreate: true, content: 'Kopiert!', trigger: 'manual', onShow(instance) { setTimeout(() => { instance.hide(); instance.destroy() }, 2000) } });
                    })"
>
    @if ($slot->isEmpty())
        <x-svg-icon
            class="mt-1 inline-block h-4 w-4 align-top text-gray-400 hover:text-gray-500"
            iconId="square-2-stack"
        />
    @else
        {{ $slot }}
    @endif
</button>
