@props([
    'color' => 'primary',
    'size' => 'sm',
])

@php
    $class = 'inline-flex items-center rounded-full font-medium';

    switch ($color) {
        case 'primary':
            $class .= ' bg-primary-100 text-primary-800';
            break;
        case 'gray':
            $class .= ' bg-gray-100 text-gray-800';
            break;
        case 'red':
            $class .= ' bg-red-100 text-red-800';
            break;
        case 'yellow':
            $class .= ' bg-yellow-100 text-yellow-800';
            break;
        case 'green':
            $class .= ' bg-green-100 text-green-800';
            break;
        case 'blue':
            $class .= ' bg-blue-100 text-blue-800';
            break;
        case 'indigo':
            $class .= ' bg-indigo-100 text-indigo-800';
            break;
        case 'brand':
            $class .= ' bg-brand-25 text-brand-600';
            break;
    }

    switch ($size) {
        case 'xs':
            $class .= ' px-2.5 py-0 text-xs';
            break;
        case 'sm':
            $class .= ' px-2.5 py-0.5 text-xs';
            break;
        case 'lg':
            $class .= ' px-3 py-0.5 text-sm';
            break;
    }
@endphp

<span {{ $attributes->merge(['class' => $class]) }}>
    {{ $slot }}
</span>
