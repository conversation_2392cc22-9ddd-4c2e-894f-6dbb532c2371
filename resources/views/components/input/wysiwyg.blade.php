@props([
    'name' => 'editor',
    'label' => null,
    'error' => null,
    'required' => false,
])
<div x-data="setupEditor(
    $wire.entangle('{{ $attributes->wire('model')->value() }}'), '{{ $name }}'
)" x-init="() => init($refs.{{ $name }})" wire:ignore {{ $attributes->whereDoesntStartWith('wire:model') }}>
    @if ($label)
        <label
                class="block text-sm font-medium leading-5 text-gray-700"
                for="{{ $name }}"
        >{{ $label }} {{ $required ? '*' : '' }}</label>
    @endif
    <div class="mt-2 border border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm">
        <div class="border-b border-gray-300 flex items-center gap-2 p-1">
            <flux:button variant="ghost" size="sm" @click="toggleBold('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.bold variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleItalic('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.italic variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleStrikeThrough('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.strikethrough variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleUnderline('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.underline variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleH2('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.h1 variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleH3('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.h2 variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleH4('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.h3 variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:dropdown>
                <flux:button variant="ghost" size="sm" icon-trailing="chevron-down">
                    <flux:icon.swatch variant="solid" class="size-4"/>
                </flux:button>

                <flux:menu>
                    <div class="grid grid-cols-5">
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'black')">
                            <div class="size-4 rounded" style="background-color: black;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'darkgrey')">
                            <div class="size-4 rounded" style="background-color: darkgrey;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'grey')">
                            <div class="size-4 rounded" style="background-color: grey;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'slategrey')">
                            <div class="size-4 rounded" style="background-color: slategrey;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'lightgrey')">
                            <div class="size-4 rounded" style="background-color: lightgrey;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'red')">
                            <div class="size-4 rounded" style="background-color: red;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'orange')">
                            <div class="size-4 rounded" style="background-color: orange;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'yellow')">
                            <div class="size-4 rounded" style="background-color: yellow;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'yellowgreen')">
                            <div class="size-4 rounded" style="background-color: yellowgreen;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'green')">
                            <div class="size-4 rounded" style="background-color: green;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'mediumseagreen')">
                            <div class="size-4 rounded" style="background-color: mediumseagreen;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'deepskyblue')">
                            <div class="size-4 rounded" style="background-color: deepskyblue;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'blue')">
                            <div class="size-4 rounded" style="background-color: blue;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'darkslateblue')">
                            <div class="size-4 rounded" style="background-color: darkslateblue;">
                            </div>
                        </flux:button>
                        <flux:button variant="ghost" size="sm" @click="toggleColor('{{ $name }}', 'blueviolet')">
                            <div class="size-4 rounded" style="background-color: blueviolet;">
                            </div>
                        </flux:button>
                    </div>

                    <flux:menu.separator />

                    <flux:menu.item variant="danger" icon="trash" @click="toggleRemoveColor('{{ $name }}')">Farbe entfernen</flux:menu.item>
                </flux:menu>
            </flux:dropdown>
            <flux:dropdown>
                <flux:button variant="ghost" size="sm" icon-trailing="chevron-down">
                    <flux:icon.table-cells variant="solid" class="size-4"/>
                </flux:button>

                <flux:menu>
                    <flux:menu.item icon="plus-circle" @click="toggleAddTable('{{ $name }}')">Tabelle hinzufügen</flux:menu.item>

                    <flux:menu.separator />

                    <flux:menu.item icon="arrow-right-end-on-rectangle" @click="toggleAddColumnAfter('{{ $name }}')">Spalte hinzufügen</flux:menu.item>
                    <flux:menu.item icon="minus-circle" @click="toggleDeleteColumn('{{ $name }}')">Spalte entfernen</flux:menu.item>

                    <flux:menu.separator />

                    <flux:menu.item icon="arrow-down-on-square" @click="toggleAddRowAfter('{{ $name }}')">Zeile hinzufügen</flux:menu.item>
                    <flux:menu.item icon="minus-circle" @click="toggleDeleteRow('{{ $name }}')">Zeile entfernen</flux:menu.item>

                    <flux:menu.separator />

                    <flux:menu.item variant="danger" icon="trash" @click="toggleDeleteTable('{{ $name }}')">Tabelle entfernen</flux:menu.item>
                </flux:menu>
            </flux:dropdown>
            <flux:button variant="ghost" size="sm" @click="toggleOrderedList('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.numbered-list variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleBulletList('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.list-bullet variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleBlockquote('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.chat-bubble-bottom-center-text variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
            <flux:button variant="ghost" size="sm" @click="toggleCodeBlock('{{ $name }}')">
                <x-slot:icon>
                    <flux:icon.code-bracket variant="solid" class="size-4"/>
                </x-slot:icon>
            </flux:button>
        </div>
        <div x-ref="{{ $name }}"></div>
    </div>

    @if($error)
        <p class="mt-2 text-red-600 text-sm">{{ $error }}</p>
    @endif
</div>
