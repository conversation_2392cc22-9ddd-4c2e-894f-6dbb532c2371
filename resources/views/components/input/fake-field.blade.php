@props([
    'label' => false,
    'name' => false,
    'error' => false,
    'helpText' => false,
    'cornerHint' => false,
    'hidden' => false,
])

<div class="{{ $hidden ? 'invisible absolute' : '' }}">
    <div class="flex justify-between">
        @if ($label)
            <label for="{{ $name }}" class="block text-sm font-medium leading-5 text-gray-700">{{ $label }}</label>
        @endif
        @if ($cornerHint)
            <span class="text-sm leading-5 text-gray-500">{{ $cornerHint }}</span>
        @endif
    </div>
    <div class="mt-1 relative rounded-md shadow-sm">
        <span
            id="{{ $name }}"
            class="form-input block rounded-md border-gray-300 shadow-sm w-full sm:text-sm sm:leading-5 disabled:opacity-50 @if ($error) border-red-300 text-red-900 placeholder-red-300 focus:border-red-300 focus:shadow-outline-red @endif"
            {{ $attributes }}
        >{{ $slot }}</span>
    </div>
    @if ($helpText)
        <p class="mt-2 text-sm text-gray-500">{{ $helpText }}</p>
     @endif
    @if ($error)
        <p class="mt-2 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
