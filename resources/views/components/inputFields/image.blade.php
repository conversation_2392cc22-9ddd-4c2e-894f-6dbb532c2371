<dropzone
    input-name="{{ $inputName }}"
    messages-prop="{{ collect([
        'cancel' => trans('messages.cancel'),
        'select' => trans('messages.select'),
        'cropImage' => trans('messages.cropImage'),
        'uploadOrDragDrop' => trans('messages.uploadOrDragDrop'),
    ]) }}"
    initial-image-path="{{ $initialValue }}"

    @if(isset($config['rounded']))
        :rounded-image="{{ $config['rounded'] }}"
    @endif
    @if(isset($config['width']))
        :target-width="{{ $config['width'] }}"
    @endif
    @if(isset($config['height']))
        :target-height="{{ $config['height'] }}"
    @endif
    @if(isset($config['fileType']))
        file-type="{{ $config['fileType'] }}"
    @endif
    @if(isset($config['quality']))
        :quality="{{ $config['quality'] }}"
    @endif
    @if(isset($config['oldValue']))
        old-value="{{ $config['oldValue'] }}"
    @endif
    @if(isset($config['viewMode']))
        view-mode="{{ $config['viewMode'] }}"
    @endif
></dropzone>
