@props([
    'buttonTitle' => 'Jetzt CardLink bestellen',
])

<div>
    <x-headless.modal
            max-width="6xl"
            :initial-open="false"
            x-on:modal-closed="Livewire.dispatch('card-link-wizard-modal-closed'); Livewire.dispatch('wizard-closed')"
            {{ $attributes->merge() }}
    >
        <x-slot name="trigger" class="flex-1">
            <x-button type="button" wire:submit.stop>
                <svg class="-ml-1 mr-2 h-5 w-5">
                    <use href="/icons.svg#plus"/>
                </svg>
                {{ $buttonTitle }}
            </x-button>
        </x-slot>

        <livewire:card-link.order-card-link-wizard/>
    </x-headless.modal>
</div>