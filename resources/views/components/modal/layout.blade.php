@props([
    'title',
    'subtitle',
    'icon',
    'nextStep',
    'prevStep',
    'stepNumber',
    'totalSteps',
    'hasFatalError',
    'fatalErrorMessage',
])

<div
    class="relative flex h-[639px] flex-col"
    dusk="wizard-step-{{ $this::kebab() }}"
    {{ $attributes }}
>
    <x-modal.header
        :title="$this->title()"
        :subtitle="$this->subtitle()"
    >
        {!! $this->icon() !!}
    </x-modal.header>

    <div class="flex flex-grow overflow-x-hidden overflow-y-scroll bg-gray-25">
        <div {{ $attributes->class(['flex-grow']) }}>
            @if (!$hasFatalError)
                {{ $slot }}
            @else
                <x-modal.container class="py-8">
                    <x-alert
                        type="error"
                        :title="$fatalErrorMessage['title']"
                        :description="$fatalErrorMessage['message']"
                        :border="true"
                    />
                </x-modal.container>
            @endif
        </div>
    </div>

    @if (!$hasFatalError)
        <x-modal.footer>
            @if ($this->prevStep())
                <x:button
                    wire:click.prevent="prev()"
                    wire:loading.class="bg-gray"
                    wire:loading.attr="disabled"
                    appearance="secondary"
                >
                    <div
                        wire:loading.delay
                        wire:target="prev"
                    >laden...</div>
                    <div
                        wire:loading.delay.remove
                        wire:target="prev"
                    >{{ $this->prevStepLabel() }}</div>
                </x:button>
            @endif
            @if ($this->nextStep())
                <x:button
                    wire:click.prevent="submit()"
                    wire:loading.class="bg-gray"
                    wire:loading.attr="disabled"
                >
                    <div
                        wire:loading.delay
                        wire:target="submit"
                    >speichern...</div>
                    <div
                        wire:loading.delay.remove
                        wire:target="submit"
                    >{{ $this->nextStepLabel() }}</div>
                </x:button>
            @endif

            <x-slot:left>
                @if ($this->stepNumber())
                    <x-stepper.index
                        :current-step="$stepNumber"
                        :total-steps="$totalSteps"
                    />
                @endif
            </x-slot:left>
        </x-modal.footer>
    @endif
</div>
