<style>
    @page {
        margin: 0;
    }

    * {
        box-sizing: border-box;
    }

    body {
        margin-top: 27mm;
        margin-left: 25mm;
        margin-right: 20mm;
        margin-bottom: 26mm;
        font-family: "verdana", "sans-serif";
        font-size: 9pt;
    }

    /* Footer */
    footer {
        position: fixed;
        bottom: 20mm;
        left: 25mm;
        right: 20mm;
        font-size: 7pt;
        line-height: 1.2;
        padding: 2mm 0;
        border-top: 1px solid #E7E5E4;
        font-size: 7pt;
        line-height: 1.2;
    }

    .footer-left {
        float: left;
        width: 50%;
    }

    .footer-right {
        float: right;
        width: 50%;
        text-align: right;
    }

    .page-break {
        page-break-after: always;
    }

    .page-number:before {
        content: counter(page);
    }

    .clearfix:after {
        content: "";
        display: table;
        clear: both;
    }

    /* Headings */
    .headline {
        color: #111827;
    }

    h1 {
        font-size: 30pt;
        margin: 0;
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: -0.02em;
        color: #111827;
    }

    h2 {
        margin: 0;
        font-size: 16pt;
        font-weight: 700;
        letter-spacing: 0.03em;
        color: #111827;
    }

    h3 {
        margin: 0;
        font-size: 9pt;
        font-weight: bold;
        color: #111827;
    }

    h3 span {
        font-weight: 400;
        letter-spacing: -0.04em;
        padding-left: 1.5em;
    }

    /* Cover */
    .cover-logo {
        margin-bottom: 30mm;
    }

    .cover-logo img {
        height: 45px;
        width: auto;
        float: right;
    }

    .overtitle {
        display: block;
        font-size: 12pt;
        font-weight: 400;
        margin-bottom: 5pt;
    }

    .title {
        margin-bottom: 15pt;
    }

    .subtitle {
        display: block;
        font-size: 16pt;
        font-weight: bold;
        line-height: 1.4;
        /*color: #111827;*/
    }

    .cover-summary {
        margin-top: 90mm;
    }

    .cover-pharmacy {
        line-height: 1.4;
        margin-bottom: 20px;
    }

    .cover-table {
        position: relative;
    }

    /* Table */
    table {
        border-collapse: collapse;
        border-spacing: 0px;
        empty-cells: show;
        width: 100%;
        border: 1px solid #E7E5E4;
    }

    tbody {
        border-top: 1px solid #E7E5E4;
        border-bottom: 1px solid #E7E5E4;
    }

    tfoot {
        font-weight: bold;
    }

    table th,
    table td {
        padding: 4px 5px;
        border-bottom: 0.7pt dotted #E7E5E4;
        font-size: 6pt;
        white-space: nowrap;
    }

    thead th {
        font-weight: bold;
        text-transform: uppercase;
        font-size: 5pt;
        letter-spacing: 0.05em;
    }

    /* Text Align */
    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    /* Font Weight */
    .font-bold {
        font-weight: bold;
    }

    /* Width */
    .w-full {
        width: 100%;
    }

    .w-1 {
        width: 1%;
    }

    .w-2/12	{
        width: 16.666667%;
    }

    .w-4/12	{
        width: 33.333333%;
    }

    /* Text color */
    .text-black {
        color: #000000;
    }

    .text-white {
        color: #ffffff;
    }

    .text-gray-600 {
        color: #52525B;
    }

    /* Background */
    .bg-white {
        background-color: #ffffff;
    }

    .bg-gray-50 {
        background: #FAFAF9;
    }

    .bg-gray-100 {
        background-color: #F5F5F4;
    }

    .bg-gray-200 {
        background-color: #E7E5E4;
    }

    .bg-gray-300 {
        background-color: #D6D3D1;
    }

    .bg-gray-900 {
        background-color: #111827;
    }

    /* Text Transform */
    .uppercase {
        text-transform: uppercase;
    }

    /* Margin */
    .mt-12 {
        margin-top: 48px;
    }
</style>
