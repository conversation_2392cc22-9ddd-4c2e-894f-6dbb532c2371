<style>
    hr {
        background-color:#919eab;
        color:#919eab;
        height:1px;
        padding:0;
        margin:0;
        border-width:0;
    }
    table {
        width:100%;
        font-family:arial,helvetica,verdana,sans-serif;
    }
    .datagrid table .rounded-corner {
        border-collapse:collapse;
        text-align:left;
        width:100%;
    }
    .datagrid .rounded-corner {
        overflow:hidden;
        border:1px solid #919eab;
        -webkit-border-radius:8px;
        -moz-border-radius:8px;
        border-radius:8px;
    }
    .datagrid table .bordered {
        border-collapse:collapse;
        text-align:left;
        width:100%;
    }
    .datagrid .bordered {
        overflow:hidden;
        border:1px solid #919eab;
    }
    /*.datagrid table td {*/
    /*    padding:3px 10px;*/
    /*}*/
    .datagrid table .rounded-corner td {
        border-left:1px solid #919eab;
        border-bottom:1px solid #919eab;
    }
    .datagrid table .rounded-corner td:first-child {
        border-left:none;
    }
    .datagrid table .rounded-corner tr:last-child td {
        border-bottom:none;
    }
    .datagrid table .bordered td {
        border-left:1px solid #919eab;
        border-bottom:1px solid #919eab;
    }
    .datagrid table .bordered td:first-child {
        border-left:none;
    }
    .datagrid table .bordered tr:last-child td {
        border-bottom:none;
    }
    .border-left {
        border-left:1px solid #919eab;
    }
    .grey {
        color:#919eab;
    }
    .size-8 {
        font-size:8px;
    }
    .size-9 {
        font-size:9px;
    }
    .size-10 {
        font-size:10px;
    }
    .size-11 {
        font-size:11px;
    }
    .size-12 {
        font-size:12px;
    }
    .size-13 {
        font-size:13px;
    }
    .size-14 {
        font-size:14px;
    }
    .size-15 {
        font-size:15px;
    }
    .size-16 {
        font-size:16px;
    }
    .size-17 {
        font-size:17px;
    }
    .size-18 {
        font-size:18px;
    }
    .size-19 {
        font-size:19px;
    }
    .bold {
        font-weight:bold;
    }
    .cursive {
        font-style:italic;
    }
    .width-15px {
        width:15px;
    }
    .width-20px {
        width:20px;
    }
    .width-25px {
        width:25px;
    }
    .width-20 {
        width:20%;
    }
    .width-25 {
        width:25%;
    }
    .width-33 {
        width:33.3%;
    }
    .width-40 {
        width:40%;
    }
    .width-50 {
        width:50%;
    }
    .width-60 {
        width:60%;
    }
    .width-66 {
        width:66.6%;
    }
    .width-100 {
        width:100%;
    }
    .align-center {
        text-align:center;
    }
    .align-right {
        text-align:right;
    }
    .valign-top {
        vertical-align:top;
    }
    .valign-center {
        vertical-align:center;
    }
    .p-5 {
        padding:5px;
    }
    .p-10 {
        padding:10px;
    }
    .p-15 {
        padding:15px;
    }
    .px-15 {
        padding-left:15px;
        padding-right:15px;
    }
    .py-15 {
        padding-top:15px;
        padding-bottom:15px;
    }
    .m-bottom-30 {
        margin-bottom:30px;
    }
    .m-top-10 {
        margin-top:10px;
    }
    .m-top-20 {
        margin-top:20px;
    }
    .m-top-30 {
        margin-top:30px;
    }
    .m-top-60 {
        margin-top:60px;
    }
    .lh-15 {
        line-height:15px;
    }
    .lh-20 {
        line-height:20px;
    }
    .lh-30 {
        line-height:30px;
    }
    footer {
        position:fixed;
        bottom:0px;
        left:0px;
        right:0px;
        /*height:20px;*/
    }
</style>
