@section('subnavbar')
    <div class="bg-gray-900 z-40 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="relative flex justify-center items-center">
                @isset($backLink)
                    <div class="absolute left-0 top-3">
                        <x:button href="{{ $backLink }}" size="xs">
                            <svg class="-ml-0.5 mr-2 h-4 w-4"><use href="/icons.svg#arrow-long-left"/></svg>
                            Zurück
                        </x:button>
                    </div>
                @endisset

                <div class="py-4">
                    <x:stepper steps="{{ $vaccination->influenzaVaccination->is_model ? 10 : 9 }}" :vaccination="$vaccination" :current="$currentStep" :saved="$savedStep"/>
                </div>

                @if($savedStep > $currentStep && isset($forwardLink))
                    <div class="absolute right-0 top-3">
                        <x:button href="{{ $forwardLink }}" size="xs">
                            Weiter
                            <svg class="-mr-0.5 ml-2 h-4 w-4"><use href="/icons.svg#arrow-long-right"/></svg>
                        </x:button>
                    </div>
                @endisset
            </div>
        </div>
    </div>
@endsection
