@extends('layouts.app', ['novue' =>true])

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                COVID-19-Zertifikat
            </x-slot>
            <x-slot name="section">
                {{ $pharmacy->name }}
            </x-slot>
        </x:header>

        {{-- TODO: Remove when working on AP-1057 --}}
        @if (isCovidVaccinationCertificateCenterActive())
            <x:alert type="warning" class="mb-6">
                <x-slot name="title">WICHTIGE MELDUNG!</x-slot>
                <x-slot name="description">
                    <p>Zertifikatsdienst wird zum 31.12.2023 eingestellt.</p>
                </x-slot>
                <x-slot name="trailingLink">
                    <a href="https://www.mein-apothekenportal.de/blog/nach-fast-110-mio-covid-19-zertifikaten-ist-schluss" target="_blank">Mehr erfahren</a>
                </x-slot>
            </x:alert>
        @endif

        {{--        <x:alert type="warning" class="mb-6">--}}
        {{--            <x-slot name="description">--}}
        {{--                <p>An<PERSON><PERSON>ndigung: Am heutigen Mittwoch gibt es bei IBM ein außerplanmäßiges Wartungsfenster von 19 Uhr bis 20 Uhr. Innerhalb dieses Zeitraums werden aus technischen Gründen Netzwerktests durchgeführt und es kann sporadisch zu Aussetzern bei der Benutzung des Systems kommen.</p>--}}
        {{--                <p class="mt-2">Die Wahrscheinlichkeit von Einschränkungen ist gering.</p>--}}
        {{--            </x-slot>--}}
        {{--        </x:alert>--}}

        {{--        <x:alert type="warning" class="mb-6">--}}
        {{--            <x-slot name="description">--}}
        {{--                Auf Grund der jetzt geltenden neuen Regelungen im Hinblick auf die Anwendung von 3G und 2G sowie der äußerst hohen Nachfrage auf Grund der stetig wachsenden Zahl von Boosterimpfungen, kommt es bundesweit in den Apotheken aktuell zu einem stark erhöhten Bedarf nach entsprechenden Impfzertifikaten. Zeitweise werden mehrere 10.000 Impfzertifikate pro Stunde ausgestellt. Diese starke Nachfrage führt temporär zu Verzögerungen bei der Ausstellung der Zertifikate in den Apotheken. Es werden derzeit Maßnahmen umgesetzt, mehr Kapazitäten zur Verfügung zu stellen, um die Performance auf erhöhtem Niveau zu stabilisieren.--}}
        {{--            </x-slot>--}}
        {{--        </x:alert>--}}

        @if(! $pharmacy->isIbmRegistered())
            <x:alert type="warning" class="mb-8">
                <x-slot name="description">
                    <ul class="list-disc space-y-1">
                        Um die Überprüfung der digitalen Impfzertifikate durch den Dienstleister IBM und das RKI gewährleisten zu können, muss Ihre Apotheke dort registriert sein. Dieser Vorgang kann ein paar Tage in Anspruch nehmen.
                    </ul>
                </x-slot>
            </x:alert>
        @endif

        @if(! $pharmacy->canImportVaccinations() && $pharmacy->isIbmRegistered())
            <x:alert type="warning" class="mb-8">
                <x-slot name="description">
                    <ul class="list-disc space-y-1">
                        Um die Funktionen des COVID-19-Zertifikats nutzen zu können, müssen Sie an Ihrer Apotheke sowohl die zugehörige Telematik-ID hinterlegen, als auch die Funktion "COVID-19-Zertifikat" aktivieren.
                    </ul>
                </x-slot>
            </x:alert>

            <form
                    method="POST"
                    action="{{ route('pharmacies.importVaccination.updatePharmacy', [$pharmacy]) }}"
                    x-data="{
                    vaccination_import: {{ (old('vaccination_import') ? ( ! ! old('vaccination_import')) : $pharmacy->vaccination_import) }}
                    }"
            >
                @method('PUT')
                @csrf

                <input type="hidden" name="id" value="{{ $pharmacy->id }}">

                <x:card class="mb-8">
                    <x:row>
                        <x:col class="xl:w-1/2">
                            <x:input.text
                                    :label="__('validation.attributes.telematics_id')"
                                    name="telematics_id"
                                    :value="old('telematics_id', $pharmacy->telematicsId ? $pharmacy->telematicsId->fullId() : '')"
                                    :error="$errors->first('telematics_id')"
                            />
                        </x:col>
                        <x:col class="xl:w-1/2">
                            <x:input.toggle
                                    :label="__('validation.attributes.vaccination_import')"
                                    id="vaccination_import"
                                    name="vaccination_import"
                                    x-model="vaccination_import"
                                    :error="$errors->first('vaccination_import')"
                            />
                        </x:col>
                    </x:row>

                    <div class="pt-8 mt-8 text-right border-t border-gray-200">
                            <span class="ml-3 inline-flex rounded-md shadow-sm">
                            <x-button
                                    size="md"
                                    class="inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition duration-150 ease-in-out"
                            >
                                Speichern
                            </x-button>
                        </span>
                    </div>
                </x:card>
            </form>
        @endif

        @if(($pharmacy->canImportVaccinations() && $pharmacy->isIbmRegistered()) || user()->can('accounting', [App\VaccinationImport::class, $pharmacy]))
            <div class="flex flex-wrap -m-2">
                {{-- Link to the TI Part of the application --}}
                {{-- TODO: Remove when working on AP-1057 --}}
                @if($pharmacy->canImportVaccinations() && $pharmacy->isIbmRegistered() && isCovidVaccinationCertificateCenterActive())
                    <div class="w-full sm:w-1/2 p-2">
                        <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                            <div class="px-4 py-5 sm:p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                        <svg class="h-6 w-6 text-white">
                                            <use href="/icons.svg#identification"/>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                                COVID-19-Zertifizierung
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                    <a target="_blank" href="{{ route('pharmacies.importVaccination.redirect', [$pharmacy]) }}" class="text-red-500 flex items-center">
                                                        Zum Zertifizierungsportal
                                                        <svg class="h-6 w-6 ml-2">
                                                            <use href="/icons.svg#arrow-top-right-on-square"/>
                                                        </svg>
                                                    </a>
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                {{-- Accounting --}}
                @can('accounting', [App\VaccinationImport::class, $pharmacy])
                    <div class="w-full sm:w-1/2 p-2">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                        <svg class="h-6 w-6 text-white">
                                            <use href="/icons.svg#banknotes"/>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                                Abrechnung
                                            </dt>
                                            <dd class="flex items-baseline">
                                                <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                    <a href="{{ route('pharmacies.importVaccination.accounting', [$pharmacy]) }}" class="text-red-500">Zur Abrechnung</a>
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endcan

                {{-- Request Recovery certificate --}}
                @if($pharmacy->canImportVaccinations() && $pharmacy->isIbmRegistered() && isCovidVaccinationCertificateCenterActive())
                    @can('requestRecoveredCertificate', [App\VaccinationImport::class, $pharmacy])
                        <div class="w-full sm:w-1/2 p-2">
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                            <svg class="h-6 w-6 text-white">
                                                <use href="/icons.svg#identification"/>
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                                    Genesenen-Impfung zertifizieren
                                                </dt>
                                                <dd class="flex items-baseline">
                                                    <x:modal name="activate-recovered-certificate">
                                                        <x-slot name="activator">
                                                            <div class="text-2xl leading-8 font-semibold text-gray-900 cursor-pointer">
                                                                <div class="text-red-500">Jetzt beantragen</div>
                                                            </div>
                                                        </x-slot>
                                                        <div class="sm:flex sm:items-start">
                                                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-headline">
                                                                    Genesenen-Impfung zertifizieren beantragen?
                                                                </h3>
                                                                <div class="mt-2">
                                                                    <p class="text-sm text-gray-500">
                                                                        Ich möchte auch die Möglichkeit bekommen COVID-19-Zertifikate für Genesene auszustellen. Die Kosten hierfür belaufen sich auf einmalig 15,- Euro für die erste und 10,- Euro für jede weitere Apotheke.
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex sm:justify-end">
                                                            <button @click="$dispatch('change-modal-state', { name: 'activate-recovered-certificate', state: 'close' })" type="button"
                                                                    class="inline-flex justify-center w-full rounded-md border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                                                                Abbrechen
                                                            </button>
                                                            <form method="POST" action="{{ route('pharmacies.importVaccination.requestRecoveredCertificate', $pharmacy) }}">
                                                                @csrf
                                                                @method('PUT')
                                                                <button type="submit" class="mt-4 sm:mt-0 sm:ml-3 w-full inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:w-auto sm:text-sm">
                                                                    Kostenpflichtig bestellen
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </x:modal>
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endcan
                @endif
            </div>

            {{-- Statistics --}}
            <h3 class="mt-12 leading-6 font-medium text-gray-900 mr-2">
                Ausgestellte COVID-19-Zertifikate
            </h3>
            <div class="mt-2 flex flex-wrap -m-2">
                <div class="w-full lg:w-1/3 p-2">
                    <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                    <svg class="h-6 w-6 text-white">
                                        <use href="/icons.svg#identification"/>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                            Gesamt
                                        </dt>
                                        <dd class="flex items-baseline">
                                            <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                {{ number_format($pharmacy->pharmacyStatistic->vaccination_import_total, 0, '', '.') }}
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-1/3 p-2">
                    <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                    <svg class="h-6 w-6 text-white">
                                        <use href="/icons.svg#identification"/>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                            Ohne COVID-19-Impfung
                                        </dt>
                                        <dd class="flex items-baseline">
                                            <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                {{ number_format($pharmacy->pharmacyStatistic->vaccination_import_without_vaccination_id, 0, '', '.') }}
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-1/3 p-2">
                    <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                    <svg class="h-6 w-6 text-white">
                                        <use href="/icons.svg#identification"/>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                            Mit COVID-19-Impfung
                                        </dt>
                                        <dd class="flex items-baseline">
                                            <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                {{ number_format($pharmacy->pharmacyStatistic->vaccination_import_with_vaccination_id, 0, '', '.') }}
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-1/3 p-2">
                    <div class="bg-white overflow-hidden shadow rounded-lg h-full">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path fill="none" stroke="currentColor" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"/>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm leading-5 font-medium text-gray-500 truncate">
                                            Genesenenzertifikate
                                        </dt>
                                        <dd class="flex items-baseline">
                                            <div class="text-2xl leading-8 font-semibold text-gray-900 ">
                                                {{ number_format($pharmacy->pharmacyStatistic->vaccination_import_recovered, 0, '', '.') }}
                                            </div>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </x:content>
@endsection
