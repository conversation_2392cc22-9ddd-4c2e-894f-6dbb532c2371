@extends('layouts.app', ['novue' =>true])

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Übersicht
            </x-slot>
            <x-slot name="section">
                {{ $pharmacy->name }}
            </x-slot>
        </x:header>

        @if(user()->can('accounting', [\App\VaccinationImport::class, $pharmacy]) || user()->can('viewAny', [\App\CovidVaccinationInvoice::class, $pharmacy]) || user()->can('viewAny', [\App\PharmaceuticalService::class, $pharmacy]))
            @if($canReactivate)
                <x:alert type="warning" class="mb-6">
                    <x-slot name="description">
                        <p>
                            Ihre Mitgliedschaft wurde vor kurzem beendet.
                            <a class="underline" href="{{ route('pharmacies.subscription', $pharmacy) }}">
                                Mitgliedschaft reaktivieren
                            </a>
                        </p>
                    </x-slot>
                </x:alert>
            @endif

            <div class="flex flex-wrap -m-2">
                @can('accounting', [\App\VaccinationImport::class, $pharmacy])
                    <x:card-small
                        title="COVID-19-Zertifikate"
                        subtitle="Abrechnungen"
                        link="{{ route('pharmacies.importVaccination.accounting', ['pharmacy' => $pharmacy]) }}"
                        icon="certificate"
                    />
                @endcan
                @can('viewAny', [\App\CovidVaccinationInvoice::class, $pharmacy])
                    <x:card-small
                        title="COVID-19-Impfungen"
                        subtitle="Abrechnungen"
                        link="{{ route('pharmacies.vaccinate-covid.invoice.index', ['pharmacy' => $pharmacy]) }}"
                        icon="vaccination"
                    />
                @endcan
                @can('viewAny', [\App\PharmaceuticalService::class, $pharmacy])
                    <x:card-small
                        title="Pharmazeutische Dienstleistungen"
                        subtitle="Abrechnungen"
                        link="{{ route('pharmacies.pharmaceutical-services.accounting', ['pharmacy' => $pharmacy]) }}"
                        icon="services"
                    />
                @endcan
            </div>
        @else
            <x:alert type="info" class="mb-6" description="Es sind keine Daten für diese Apotheke verfügbar." />
        @endif
    </x:content>
@endsection
