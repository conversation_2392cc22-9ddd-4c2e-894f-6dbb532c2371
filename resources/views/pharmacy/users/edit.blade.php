@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Mi<PERSON><PERSON><PERSON>/in bearbeiten
            </x-slot>
            <x-slot name="action">
                <x:modal name="delete-user">
                    <x-slot name="activator">
                        <x:button>
                            <svg class="-ml-1 mr-2 h-5 w-5"><use href="/icons.svg#trash"/></svg>
                            Löschen
                        </x:button>
                    </x-slot>
                    <div>
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600"><use href="/icons.svg#trash"/></svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-5">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-headline">
                                Mitarbeiter/in löschen?
                            </h3>

                            <div class="mt-2">
                                <p class="text-sm leading-5 text-gray-500">
                                    Der
                                </p>
                            </div>

                            <div class="mt-2">
                                <p class="text-sm leading-5 text-gray-500">
                                    Mitarbeiter-Account wird unwiderruflich gelöscht.

                                </p>
                            </div>

                            <div class="mt-2">
                                <p class="text-sm leading-5 text-gray-500">
                                    Warnung: Diese Aktion kann nicht rückgängig gemacht werden.
                                    Alle zugehörigen Daten des Dienstplans, inkl. Schichten, werden ebenfalls unwiderruflich gelöscht.
                                    Pharmazeutische Dienstleistungen werden anonymisiert.
                                    Impfungen
                                </p>
                            </div>

                            <div class="mt-2">
                                @if(\App\ShiftPlanGroupUser::where('user_id', $user->id)->exists())
                                    <p class="text-sm leading-5 text-gray-500">
                                        Bitte beachten Sie: Der Mitarbeiter ist in einem Dienstplan vermerkt. Wenn Sie den Mitarbeiter-Account löschen, werden auch alle zugehörigen Daten im Dienstplan, inkl. Schichten, entfernt.
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6 flex flex-wrap">
                        <div class="w-1/2 px-1">
                            <x:button x-on:click="open = false" appearance="secondary" wrapperClass="w-full" class="w-full">
                                Abbrechen
                            </x:button>
                        </div>
                        <div class="w-1/2 px-1">
                            <form method="POST" action="{{ route('pharmacies.users.destroy', [$pharmacy, $user]) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <x:button type="submit" appearance="primary" wrapperClass="w-full" class="w-full">
                                    Löschen
                                </x:button>
                            </form>
                        </div>
                    </div>
                </x:modal>
            </x-slot>
        </x:header>

        @livewire('pharmacy.components.edit-user', ['pharmacy' => $pharmacy, 'user' => $user])
    </x:content>
@endsection
