@extends('layouts.app', ['novue' => false])

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                {{ $model->name }}
            </x-slot>
            <x-slot name="description">
                <x-pharmacies.digital-representation/>
            </x-slot>
            <x-slot name="action">
                @can('delete', $model)
                    <livewire:pharmacy.delete-pharmacy :pharmacy="$model" />
                @endcan
            </x-slot>
        </x:header>

        <div class="flex flex-wrap -m-4">
            <div class="w-full p-4">
                <form action="{{ route('pharmacies.update', $model) }}" method="POST">
                    @csrf
                    @method('PUT')

                    {{--Content--}}
                    <div>
                        <input type="hidden" name="id" value="{{ $model->id }}">
                        @include('components.pharmacies.pharmacy-tab-menu')
                        @include('components.pharmacyFormFields')
                    </div>

                    {{--Footer--}}
                    <div class="pt-8 pb-8 text-right">
                        <span class="inline-flex rounded-md shadow-sm">
                            <a href="{{ route('pharmacies') }}"
                               class="py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:border-red-300 focus:shadow-outline-red active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out cursor-pointer">
                                @lang('messages.cancel')
                            </a>
                        </span>
                        <span class="ml-3 inline-flex rounded-md shadow-sm">
                            <button type="submit"
                                    class="inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition duration-150 ease-in-out">
                                @lang('messages.save')
                            </button>
                        </span>
                    </div>
                </form>
            </div>
        </div>
    </x:content>
@endsection
