@extends('layouts.app')

@section('content')
    <x:content>
        @include('pharmacy.inhalationTechnique.partials.subnavbar', [
            'currentStep' => 4,
            'savedStep' => $pharmaceuticalService['step'],
            'backLink' => route('pharmacies.pharmaceutical-services.inhalation-techniques.checklist', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]),
        ])

        <x:inhalationTechnique.headline>
            Überblick über die Inhalationstechnik
        </x:inhalationTechnique.headline>

        <x:layout.container size="max-w-2xl">
            <x:card>
                <x:row>
                    <x:col>
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Angaben zum Patienten/zur Patientin</h3>
                            <x:button href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.personal-data', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]) }}" appearance="light" size="xs">
                                <svg class="h-4 w-4"><use href="/icons.svg#pencil"/></svg>
                            </x:button>
                        </div>
                    </x:col>
                </x:row>
                <x:row>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Person
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            {{ $pharmaceuticalService->pharmaceuticalServicePatient->first_name }} {{ $pharmaceuticalService->pharmaceuticalServicePatient->last_name }}
                        </p>
                    </x:col>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Adresse
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @if ($pharmaceuticalService->pharmaceuticalServicePatient->optional_address_line )
                                {{ $pharmaceuticalService->pharmaceuticalServicePatient->optional_address_line }}<br>
                            @endif
                            {{ $pharmaceuticalService->pharmaceuticalServicePatient->street }} {{ $pharmaceuticalService->pharmaceuticalServicePatient->house_number }} <br>
                            {{ $pharmaceuticalService->pharmaceuticalServicePatient->postcode }} {{ $pharmaceuticalService->pharmaceuticalServicePatient->city }}
                        </p>
                    </x:col>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Geburtsdatum
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            {{ \Carbon\Carbon::parse($pharmaceuticalService->pharmaceuticalServicePatient->birthdate)->format('d.m.Y') }}
                        </p>
                    </x:col>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Kostenträgerkennung
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @if ($pharmaceuticalService->pharmaceuticalServicePatient->cost_unit_identification )
                                {{ $pharmaceuticalService->pharmaceuticalServicePatient->cost_unit_identification }}
                            @endif
                        </p>
                    </x:col>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Krankenkasse
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @if ($pharmaceuticalService->pharmaceuticalServicePatient->health_insurance )
                                {{ $pharmaceuticalService->pharmaceuticalServicePatient->health_insurance }}
                            @endif
                        </p>
                    </x:col>
                    <x:col class="xl:w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Versichertennummer
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @if ($pharmaceuticalService->pharmaceuticalServicePatient->insurance_number )
                                {{ $pharmaceuticalService->pharmaceuticalServicePatient->insurance_number }}
                            @endif
                        </p>
                    </x:col>
                </x:row>

                <hr class="my-8">

                <x:row>
                    <x:col>
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Checkliste</h3>
                            <x:button href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.checklist', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]) }}" appearance="light" size="xs">
                                <svg class="h-4 w-4"><use href="/icons.svg#pencil"/></svg>
                            </x:button>
                        </div>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Arzneimittel&reg; (Wirkstoff(e))
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            {{ $pharmaceuticalService->inhalationTechniquePatient->pharmaceutical }}
                        </p>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Inhalationssystem
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @if($pharmaceuticalService->inhalationTechniquePatient->inhalation_system === \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::Other->name)
                                {{ $pharmaceuticalService->inhalationTechniquePatient->inhalation_system_other_text }}
                            @else
                                {{ $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ? \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::value($pharmaceuticalService->inhalationTechniquePatient->inhalation_system) : 'Kein Eintrag' }}
                            @endif
                        </p>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Zustand des Gerätes
                        </p>
                        <ul class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @forelse(\App\Enums\InhalationTechnique\ChecklistDeviceConditionEnum::arrayOnly($pharmaceuticalService->inhalationTechniquePatient->device_condition ?? []) as $text)
                                <li>&middot; {{ $text }}</li>
                            @empty
                                <li>Keine Einträge</li>
                            @endforelse
                        </ul>

                        @if (!empty($pharmaceuticalService->inhalationTechniquePatient->device_condition_optional_text))
                            <x:alert type="discrete"
                                     withoutIcon="true"
                                     title="Freitext zum Zustand des Gerätes"
                                     description="{{ $pharmaceuticalService->inhalationTechniquePatient->device_condition_optional_text }}"
                                     class="mb-4"
                            />
                        @endif
                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Vorbereitung
                        </p>
                        <ul class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @forelse(\App\Enums\InhalationTechnique\ChecklistPreparationEnum::arrayOnly($pharmaceuticalService->inhalationTechniquePatient->preparation ?? []) as $text)
                                <li>&middot; {{ $text }}</li>
                            @empty
                                <li>Keine Einträge</li>
                            @endforelse
                        </ul>

                        @if (!empty($pharmaceuticalService->inhalationTechniquePatient->preparation_optional_text))
                            <x:alert type="discrete"
                                     withoutIcon="true"
                                     title="Freitext zur Vorbereitung"
                                     description="{{ $pharmaceuticalService->inhalationTechniquePatient->preparation_optional_text }}"
                                     class="mb-4"
                            />
                        @endif

                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Inhalation
                        </p>
                        <ul class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @forelse(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::arrayOnly($pharmaceuticalService->inhalationTechniquePatient->inhalation ?? []) as $text)
                                <li>&middot; {{ $text }}</li>
                            @empty
                                <li>Keine Einträge</li>
                            @endforelse
                        </ul>

                        @if (!empty($pharmaceuticalService->inhalationTechniquePatient->inhalation_optional_text))
                            <x:alert type="discrete"
                                     withoutIcon="true"
                                     title="Freitext zur Inhalation"
                                     description="{{ $pharmaceuticalService->inhalationTechniquePatient->inhalation_optional_text }}"
                                     class="mb-4"
                            />
                        @endif

                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <p class="text-sm font-medium leading-5 text-gray-800">
                            Beenden
                        </p>
                        <ul class="text-sm font-medium leading-5 text-gray-500 mb-1">
                            @forelse(\App\Enums\InhalationTechnique\ChecklistEndingEnum::arrayOnly($pharmaceuticalService->inhalationTechniquePatient->ending ?? []) as $text)
                                <li>&middot; {{ $text }}</li>
                            @empty
                                <li>Keine Einträge</li>
                            @endforelse
                        </ul>

                        @if (!empty($pharmaceuticalService->inhalationTechniquePatient->ending_optional_text))
                            <x:alert type="discrete"
                                     withoutIcon="true"
                                     title="Freitext zum Beenden"
                                     description="{{ $pharmaceuticalService->inhalationTechniquePatient->ending_optional_text }}"
                                     class="mb-4"
                            />
                        @endif

                    </x:col>
                </x:row>

                @if (!empty($pharmaceuticalService->inhalationTechniquePatient->others))
                    <x:row>
                        <x:col>
                            <x:alert type="discrete"
                                     withoutIcon="true"
                                     title="Sonstiges"
                                     description="{{ $pharmaceuticalService->inhalationTechniquePatient->others }}"
                                     class="mb-4"
                            />
                        </x:col>
                    </x:row>
                @endif

                <hr class="my-8">

                <x:row>
                    <x:col>
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Benötigt der Patient das Ergebnis in ausgedruckter Form?</h3>
                        </div>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col>
                        <x:alert type="warning" title="Wichtig">
                            <x-slot name="description">
                                Sobald dieser Vorgang abgeschlossen worden ist, kann das Dokument nicht mehr heruntergeladen werden!
                            </x-slot>
                        </x:alert>

                        <div class="mt-4 text-center">
                            <x-button :href="route('pharmacies.pharmaceutical-services.inhalation-techniques.download-result', [$pharmacy, $pharmaceuticalService])" target="_blank" size="lg" class="w-72">
                                PDF Dokument herunterladen
                            </x-button>
                        </div>
                    </x:col>
                </x:row>
            </x:card>

            <form method="POST" action="">
                @csrf
                <div class="mt-12 w-full flex justify-center">
                    <x-button
                        type="submit"
                        size="lg" class="w-72">
                        Abschließen
                    </x-button>
                </div>
            </form>

            <div class="mt-3 w-full flex justify-center">
                <x-button
                    href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.abort', [
                        'pharmacy' => $pharmacy,
                        'pharmaceuticalService' => $pharmaceuticalService,
                        'reasons' => \App\Enums\InhalationTechnique\ReasonToAbortEnum::TEST,
                    ]) }}"
                    appearance="secondary"
                    size="lg" class="w-72"
                >
                    Als Test abschließen
                </x-button>
            </div>

            <div class="mt-3 w-full flex justify-center">
                <x:button
                    href="{{ route('pharmacies.pharmaceutical-services.inhalation-techniques.abort', [
                            'pharmacy' => $pharmacy,
                            'pharmaceuticalService' => $pharmaceuticalService,
                            'reasons' => \App\Enums\InhalationTechnique\ReasonToAbortEnum::ABORT,
                        ]) }}"
                    appearance="secondary"
                    size="lg" class="w-72"
                >
                    Abbrechen
                </x:button>
            </div>
        </x:layout.container>
    </x:content>
@endsection
