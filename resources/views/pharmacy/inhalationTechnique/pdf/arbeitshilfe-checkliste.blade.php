<html class="m-45">

@include('pharmacy.inhalationTechnique.pdf.partials.head', ['doctitle' => 'Arbeitshilfe Vereinbarung bei Inhalationstechnik'])

<!-- *** start content *** -->

<body class="pt-7 pr-30 pl-25 ">

<div class="page-height page-1 page-break">
    <header class="arrow height-header border-red-bold">
        <img src="{{ resource_path('/views/pharmacy/inhalationTechnique/pdf/img/pDL-logo-2023.png') }}" width="100px" class="pdl-logo">
        <img src="{{ resource_path('/views/pharmacy/inhalationTechnique/pdf/img/bak-logo.png') }}" width="130px" class="bak-logo">
    </header>

    <div class="ml-25 mt-235">
        <h1 class="text-gray text-4md">
            Arbeitsmaterialien für die pharmazeutischen Dienstleistungen
        </h1>
        <h2 class="text-red text-lg mt-22">
            Erweiterte Einweisung in die korrekte<br>Arzneimittelanwendung mit Üben der Inhalationstechnik
        </h2>
        <ul class="text-lg mt-45 text-black list-style-square">
            <li><b>Checkliste zur korrekten Anwendung inhalativer Arzneimittel</b></li>
        </ul>

        <p class="text-4sm"><b>Stand: 16.06.2023</b></p>
    </div>
</div>

<div class="page-height page-2 page-break mt--15 ml-2">
    <header class="arrow height-header border-red-bold border-radius bg-red text-white">
        <ul class="header-list">
            <li><b>Arbeitsmaterialien für die pharmazeutischen Dienstleistungen</b></li>
        </ul>
        <span class="ml-12 text-4sm">Erweiterte Einweisung in die korrekte Arzneimittelanwendung mit Üben der Inhalationstechnik</span>
    </header>

    <div class="mt-45 ml-22 mr-20 text-4sm lh-15 justify height-content">
        Mit Hilfe dieser Arbeitshilfe kann das pharmazeutische Personal die korrekte Anwendung inhalativer Arzneimittel überprüfen.
        Der/die Patient*in demonstriert die Anwendung des inhalativen Arzneimittels. Das Vorgehen wird mittels der Checkliste geprüft.
        Sollte ein Beratungsbedarf festgestellt werden, wird dieser in der Checkliste dokumentiert. Anwendungsfehler werden
        ausgiebig besprochen. Außerdem erhält der/die Patient*in individuelle Hilfestellungen, z. B. in
        Form von Demonstrationsmaterial oder praktischen Übungen.
    </div>

    @include('pharmacy.inhalationTechnique.pdf.partials.footer-bak')
</div>

<div class="page-height page-3 mt--15 ml-2">
    <header class="arrow arrow-bold header-bold border-red-bolder border-radius-bolder">
        <ul class="header-list header-list-bold">
            <li class="black"><b>Arbeitsmaterialien für die pharmazeutischen Dienstleistungen</b></li>
        </ul>
        <span class="ml-9 text-5sm nowrap z-20">Erweiterte Einweisung in die korrekte Arzneimittelanwendung mit Üben der Inhalationstechnik</span>
    </header>

    <div class="mt-33 mr--5 ml-6 height-content">
        <table class="table-width table-border text-3sm">
            <tbody>
            <tr>
                <th class="lightgray" colspan="4">Name Patient*in:</th>
                <td class="lightblue" colspan="7">{{ $pharmaceuticalService->pharmaceuticalServicePatient->first_name }} {{ $pharmaceuticalService->pharmaceuticalServicePatient->last_name }}</td>
                <th class="lightgray" colspan="2">Datum:</th>
                <td class="lightblue" colspan="5">{{ $pharmaceuticalService->pharmaceuticalServicePatient->datetime->format('d.m.Y') }}</td>
            </tr>
            <tr>
                <th class="lightgray" colspan="7">Arzneimittel&reg; <span class="text-normal">(Wirkstoff(e))</span>:</th>
                <td class="lightblue" colspan="11">{{ $pharmaceuticalService->inhalationTechniquePatient->pharmaceutical }}</td>
            </tr>
            <tr>
                <th class="lightgray" colspan="18">Inhalationssystem</th>
            </tr>
            <tr>
                <td colspan="9">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::DA->name === $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ?? null])
                    Dosieraerosol (DA)
                </td>
                <td colspan="9">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::PI->name === $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ?? null])
                    Pulverinhalator (PI)
                </td>
            </tr>
            <tr>
                <td colspan="9">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::DA_breath->name === $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ?? null])
                    Atemzuginduziertes Dosieraerosol (DA-atem)
                </td>
                <td colspan="9">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::SV->name === $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ?? null])
                    Respimat&reg; (Sprühvernebler, SV)
                </td>
            </tr>
            <tr>
                <td colspan="9">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::DA_S->name === $pharmaceuticalService->inhalationTechniquePatient->inhalation_system ?? null])
                    Dosieraerosol mit Spacer (DA+S)
                </td>
                <td class="blue" colspan="9">
                    <span class="white">Sonstiges:</span>
                    @if($pharmaceuticalService->inhalationTechniquePatient->inhalation_system === \App\Enums\InhalationTechnique\ChecklistInhalationSystemEnum::Other->name)
                        {{ $pharmaceuticalService->inhalationTechniquePatient->inhalation_system_other_text }}
                    @endif
                </td>
            </tr>
            <tr>
                <th class="lightgray" colspan="14">Durchführung der Inhalation durch den/die Patienten/Patientin</th>
                <th class="lightgray" colspan="4">Beratungsbedarf</th>
            </tr>
            <tr>
                <th class="lightgray" colspan="14">Zustand des Gerätes</th>
                <th class="lightgray" colspan="4"></th>
            </tr>
            <tr>
                <td class="text-center">1</td>
                <td colspan="13">Gerät technisch funktionsfähig und Gerätekomponenten passen zusammen</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistDeviceConditionEnum::Device_functional_components_match->name, $pharmaceuticalService->inhalationTechniquePatient->device_condition ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">2</td>
                <td colspan="13">Sauberkeit ausreichend</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistDeviceConditionEnum::Device_clean->name, $pharmaceuticalService->inhalationTechniquePatient->device_condition ?? [])])
                </td>
            </tr>
            <tr>
                <th class="lightgray" colspan="14">Vorbereitung</th>
                <th class="lightgray" colspan="4"></th>
            </tr>
            <tr>
                <td class="text-center">3</td>
                <td colspan="13">Schütteln (i. d. R. DA, DA-atem, DA+S)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistPreparationEnum::Shaking->name, $pharmaceuticalService->inhalationTechniquePatient->preparation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">4</td>
                <td colspan="13">Abnehmen der Schutzkappe</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistPreparationEnum::Removing_cap->name, $pharmaceuticalService->inhalationTechniquePatient->preparation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">5</td>
                <td colspan="13">Korrekte Bedienung bis zur Gerätebereitschaft (ggf. Patrone/Kapsel einlegen,
                    Laden/Spannen, Spacer aufsetzen, Freisetzen des Wirkstoffs)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistPreparationEnum::Correct_operation_until_device_ready->name, $pharmaceuticalService->inhalationTechniquePatient->preparation ?? [])])
                </td>
            </tr>
            <tr>
                <th class="lightgray" colspan="14">Inhalation</th>
                <th class="lightgray" colspan="4"></th>
            </tr>
            <tr>
                <td class="text-center">6</td>
                <td colspan="13">Korrekte Gerätehaltung (DA, DA+S: Mundstück nach unten; DA-atem: senkrecht;
                    PI: senkrecht oder waagerecht, nicht schütteln; SV: waagerecht)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Correct_device_posture->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">7</td>
                <td colspan="13">Vollständig ausatmen (nicht in das Gerät)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Exhale_completely->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">8</td>
                <td colspan="13">Mundstück in den Mund nehmen und mit den Lippen vollständig umschließen</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Lip_closure->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">9</td>
                <td colspan="13">Kopf aufrecht halten</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Keep_head_upright->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center" rowspan="5">10</td>
                <td colspan="13">DA: Sprühstoß auslösen und gleichzeitig tief, langsam und lang einatmen</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::DA_Release_spray_and_simultaneously_take_deep_slow_and_long_breath->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td colspan="13">DA-atem: tief, langsam und lange einatmen (bis zur maximalen Inspiration), Lufteinlassöffnungen nicht abdecken</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::DA_breath_Inhale_deeply_slowly_and_for_long_time_do_not_cover_the_air_intake_openings->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])]){{-- todo --}}
                </td>
            </tr>
            <tr>
                <td colspan="13">DA+S: Auslösen in Spacer und sofort langsam und möglichst tief einatmen (&lt; 3 – 5 Sekunden)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::DA_S_Release_into_spacer_and_immediately_breathe_in_slowly_and_as_deeply_as_possible->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])]){{-- todo --}}
                </td>
            </tr>
            <tr>
                <td colspan="13">PI: tief und zügig einatmen (bis zur maximalen Inspiration)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::PI_Inhale_deeply_and_quickly->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])]){{-- todo --}}
                </td>
            </tr>
            <tr>
                <td colspan="13">SV: Sprühstoß auslösen und gleichzeitig tief, langsam und lang einatmen, Lufteinlassöffnungen nicht abdecken</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::SV_Release_spray_burst_and_at_the_same_time_inhale_deeply_slowly_and_long_do_not_cover_air_inlet_openings->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])]){{-- todo --}}
                </td>
            </tr>
            <tr>
                <td class="text-center">11</td>
                <td colspan="13">Atem für 5 – 10 Sekunden anhalten</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Hold_your_breath->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">12</td>
                <td colspan="13">Ausatmen über Lippenbremse oder Nase (nicht in das Gerät)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Exhale_over_lip_brake_or_nose->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">13</td>
                <td colspan="13">Erfolgskontrolle (ggf. Geschmack, Geräusch, Zählwerk, leere Kapselhülle)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistInhalationEnum::Success_control->name, $pharmaceuticalService->inhalationTechniquePatient->inhalation ?? [])]){{-- todo --}}
                </td>
            </tr>
            <tr>
                <th class="lightgray" colspan="14">Beenden</th>
                <th class="lightgray" colspan="4"></th>
            </tr>
            <tr>
                <td class="text-center">14</td>
                <td colspan="13">Mundstück säubern</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistEndingEnum::Clean_mouthpiece->name, $pharmaceuticalService->inhalationTechniquePatient->ending ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">15</td>
                <td colspan="13">Gerät zurücksetzen (DA-atem: Entspannen, PI: ggf. Kapselhülle entfernen)</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistEndingEnum::Reset_device->name, $pharmaceuticalService->inhalationTechniquePatient->ending ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">16</td>
                <td colspan="13">Aufstecken der Schutzkappe</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistEndingEnum::Putting_on_protective_cap->name, $pharmaceuticalService->inhalationTechniquePatient->ending ?? [])])
                </td>
            </tr>
            <tr>
                <td class="text-center">17</td>
                <td colspan="13">Mund ausspülen oder etwas Essen nach Anwendung eines Glucocorticoids</td>
                <td colspan="4" class="text-center">
                    @include('pharmacy.measureBloodPressure.pdf.partials.checkbox', ['checked' => in_array(\App\Enums\InhalationTechnique\ChecklistEndingEnum::Rinse_your_mouth_or_eat_something_after_using_Glucocorticoid->name, $pharmaceuticalService->inhalationTechniquePatient->ending ?? [])])
                </td>
            </tr>
            <tr>
                <td class="lightgray" colspan="18"><b>Sonstiges</b> (andere Fehler, Kommentare)</td>
            </tr>
            </tbody>
            <tfoot>
            <tr rowspan="2">
                <td class="lightblue h-35" colspan="18">
                    {{ $pharmaceuticalService->inhalationTechniquePatient->others }}
                </td>
            </tr>
            </tfoot>
        </table>
    </div>

    @include('pharmacy.inhalationTechnique.pdf.partials.footer-bak')
</div>

</body>
</html>
