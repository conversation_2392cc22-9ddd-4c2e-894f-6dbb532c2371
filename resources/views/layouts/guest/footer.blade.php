<footer class="bg-white mt-20">
    @include('layouts.guest.apo-portal-advertising')

    <div class="w-full bg-transparent bg-gradient-to-b from-gray-300 to-white border-t border-gray-300 h-8">&nbsp;</div>

    <div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">

        <div class="sm:grid sm:grid-cols-2 sm:gap-8">

            <div class="space-y-8 xl:col-span-1">
                <a href="{{ route('home') }}">
                    <x:logo class="block h-10 w-auto"></x:logo>
                </a>
                <p class="text-gray-500 text-base leading-6">
                    GEDISA - Gesellschaft für digitale Services der Apotheken mbH<br>
                    Yorckstraße 22- 24<br>
                    14467 Potsdam<br><br>
                    Telefon: +49 (0)30 62 93 77 55<br>
                    E-Mail: <a class="underline" href="mailto:<EMAIL>"><EMAIL></a>
                </p>
            </div>

            <div class="mt-12 sm:mt-0">
                <div class="grid grid-cols-2 gap-4 md:gap-8">
                    <div>
                        <h4 class="text-sm leading-5 font-semibold text-gray-400 tracking-wider uppercase">
                            {{ Str::upper('Allgemein') }}
                        </h4>
                        <ul class="mt-4 space-y-4">
                            <li>
                                <a href="{{ route('home') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('messages.home')</a>
                            </li>
                            <li>
                                <a href="{{ route('support') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('navigation.support')</a>
                            </li>
                            <li>
                                @if (Auth::guest())
                                    <a href="{{ route('news.index', ['type' => 'news']) }}"
                                       class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('messages.news')</a>
                                @else
                                    <a href="{{ route('news.index', ['type' => 'news']) }}"
                                       class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('messages.blog')</a>
                                @endif
                            </li>
                            <li>
                                <a href="{{ route('register-form') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('auth.register')</a>
                            </li>
                        </ul>
                    </div>
                    <div class="">
                        <h4 class="text-sm leading-5 font-semibold text-gray-400 tracking-wider uppercase">
                            {{ Str::upper('Rechtliches') }}
                        </h4>
                        <ul class="mt-4 space-y-4">
                            <li>
                                <a href="{{ route('imprint') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('navigation.imprint')</a>
                            </li>
                            <li>
                                <a href="{{ route('privacy') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('navigation.privacy')</a>
                            </li>
                            <li>
                                <a href="{{ route('terms-of-use') }}"
                                   class="text-base leading-6 text-gray-500 hover:text-gray-900">@lang('navigation.termsOfUse')</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
        <div class="mt-12 border-t border-gray-600 pt-8">
            <p class="text-base leading-6 text-gray-500 xl:text-center">
                &copy; {{ date('Y') }} GEDISA - Gesellschaft für digitale Services der Apotheken mbH
            </p>
        </div>
    </div>
</footer>

@if (request()->routeIs(['dashboard', 'support']) && app(\App\Settings\AppSettings::class)->show_disruptor && \App\Banner::query()->active()->published()->bottom()->exists())
    @include('partials.disruptor', ['content' => \App\Banner::query()->active()->published()->bottom()->latest('starts_at')->first()->content])
@endif
