{{-- General --}}
<div class="-ml-2 mt-3 lg:hidden">
    <h3 class="mb-3 px-3 text-xs font-semibold uppercase leading-4 tracking-wider text-gray-500">
        @lang('navigation.general')
    </h3>
    <div
        class="mt-1 space-y-1"
        role="group"
        aria-labelledby="projects-headline"
    >
        <a
            class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
            href="{{ route('users.edit') }}"
        >
            <span class="truncate">
                @lang('navigation.account')
            </span>
        </a>
        @if (user()->isOwner())
            @can('viewAny', \App\BillingAddress::class)
                <a
                    class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
                    href="{{ route('users.billing-addresses') }}"
                >
                    <span class="truncate">Rechnungsadressen</span>
                </a>
            @endcan
        @endif
        <a
            class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
            href="{{ route('support') }}"
        >
            <span class="truncate">
                @lang('navigation.support')
            </span>
        </a>
        @can('betaTestShow', user())
            <a
                class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
                href="{{ route('beta-test.show') }}"
            >
                <span class="truncate">
                    Friendly User
                </span>
            </a>
        @endcan
        <a
            class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
            href="{{ route('imprint') }}"
        >
            <span class="truncate">
                Impressum
            </span>
        </a>
        <a
            class="group flex items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
            href="{{ route('privacy') }}"
        >
            <span class="truncate">
                Datenschutz
            </span>
        </a>
        <form
            class="w-full"
            action="{{ route('logout') }}"
            method="POST"
        >
            @csrf

            <button id="logout-button"
                class="group flex w-full items-center rounded-md px-3 py-2 text-sm font-medium leading-5 text-gray-600 transition duration-150 ease-in-out hover:bg-gray-50 hover:text-gray-900 focus:bg-gray-50 focus:text-gray-900 focus:outline-none"
                type="submit"
                role="menuitem"
            >
                @lang('auth.logout')
            </button>
        </form>
    </div>
</div>
