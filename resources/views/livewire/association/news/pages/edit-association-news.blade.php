<x-content>
    <x-header>
        <x-slot name="main">
            Verbandsnews bearbeiten
        </x-slot>
    </x-header>

    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <x-panel class="p-4 sm:p-6 lg:p-8 sm:col-span-2">
            <livewire:association.news.components.form :$news/>
        </x-panel>

        <x-panel class="self-start p-4 sm:p-6 lg:p-8">
            <dl class="text-sm">
                <dt class="font-semibold">Veröffentlicht am</dt>
                <dt class="text-gray-600">{{ $news->created_at->timezone('Europe/Berlin')->format('d.m.Y H:i:s') }}</dt>

                <dt class="mt-4 font-semibold">Letzte Aktualisierung am</dt>
                <dt class="text-gray-600">{{ $news->updated_at->timezone('Europe/Berlin')->format('d.m.Y H:i:s') }}</dt>

                <dt class="mt-4 font-semibold">Slug</dt>
                <dt class="text-gray-600">{{ $news->slug }}</dt>
            </dl>

            @can('delete', $news)
                <hr class="mt-4 sm:mt-6 lg:mt-8"/>

                <x-button wire:click="delete" class="mt-4 sm:mt-6 lg:mt-8" appearance="primary">Löschen</x-button>
            @endcan
        </x-panel>
    </div>

    @can('delete', $news)
        <flux:modal name="confirm-delete" class="min-w-[22rem] space-y-6">
            <div>
                <flux:heading size="lg">Verbandsnews löschen?</flux:heading>

                <flux:subheading>
                    <p>Sie sind dabei die Verbandsnews <span class="font-semibold">"{{ $news->title }}"</span> zu
                        löschen.
                    </p>
                    <p>Diese Aktion kann nicht widerrufen werden.</p>
                </flux:subheading>
            </div>

            <div class="flex gap-2">
                <flux:spacer/>

                <flux:modal.close>
                    <x-button appearance="ghost">Abbrechen</x-button:button>
                </flux:modal.close>

                <x-button appearance="primary" wire:click="confirm">Verbandsnews löschen</x-button:button>
            </div>
        </flux:modal>
    @endcan
</x-content>