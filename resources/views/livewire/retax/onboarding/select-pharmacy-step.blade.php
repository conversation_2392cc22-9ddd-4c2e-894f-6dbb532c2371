<x-modal.layout
    :title="$this->title()"
    :subtitle="$this->subtitle()"
    :icon="$this->icon()"
    :next-step="$this->nextStep()"
    :prev-step="$this->prevStep()"
    :step-number="$this->stepNumber()"
    :total-steps="$totalSteps"
    :has-fatal-error="$this->hasFatalError()"
    :fatal-error-message="$this->fatalErrorMessage()"
>
    <x-modal.container class="h-full px-12 py-6">
        <h2 class="font-semibold">Hauptapotheke auswählen</h2>
        <p class="mt-1 text-sm text-gray-600 mb-3">
            Diese Information ist für das Anlegen der Retax-Fälle und für die Kommunikation mit Ihrem Verband notwendig.
        </p>

        @if ($this->pharmacies->count() > 0)
        <div>
            <flux:radio.group wire:model="selectedPharmacy" variant="cards" class="flex-col">
                @foreach($this->pharmacies as $pharmacy)
                    <flux:radio :value="$pharmacy->id">
                        <flux:radio.indicator />

                        <div class="flex-1">
                            <flux:heading class="leading-4">{{ $pharmacy->name }}</flux:heading>
                            <flux:text size="sm" class="mt-2">
                                {{ sprintf('%s %s, %s %s', $pharmacy->homeAddress->street, $pharmacy->homeAddress->house_number, $pharmacy->homeAddress->postcode, $pharmacy->homeAddress->city) }}
                            </flux:text>
                        </div>
                    </flux:radio>
                @endforeach
            </flux:radio.group>
        </div>
        @else
            <x-alert type="warning" class="rounded-md">
                <x-slot:description>
                    Es wurden keine Apotheken gefunden, die Sie verwalten können.
                </x-slot:description>
            </x-alert>
        @endif

        @error('selectedPharmacy')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </x-modal.container>
</x-modal.layout>
