<x-modal.layout
    :title="$this->title()"
    :subtitle="$this->subtitle()"
    :icon="$this->icon()"
    :next-step="$this->nextStep()"
    :prev-step="$this->prevStep()"
    :step-number="$this->stepNumber()"
    :total-steps="$totalSteps"
    :has-fatal-error="$this->hasFatalError()"
    :fatal-error-message="$this->fatalErrorMessage()"
>
    <x-modal.container class="h-full px-12 py-6">
        <h2 class="font-semibold">SDR für Retax aktivieren (Sicherer Datenraum)</h2>
        <p class="mt-1 text-sm text-gray-600 mb-3">
            Bitte wählen Sie den Sicheren Datenraum, in dem Ihre Dokumente aus dem Retax-Portal automatisiert hinterlegt werden sollen.
        </p>

        @if ($this->docSpaces->count() > 0)
            <div>
                <flux:radio.group wire:model="selectedDocSpace" variant="cards" class="flex-col">
                    @foreach($this->docSpaces as $docSpace)
                        <flux:radio :value="$docSpace->id">
                            <flux:radio.indicator />

                            <div class="flex-1">
                                <flux:heading class="leading-4">{{ $docSpace->name }}</flux:heading>
                                <flux:text size="sm" class="mt-2">
                                    {{ $docSpace->description }}
                                </flux:text>
                            </div>
                        </flux:radio>
                    @endforeach
                </flux:radio.group>
            </div>
        @else
            <x-alert type="warning" class="rounded-md">
                <x-slot:description>
                    Es wurden keine sicheren Dateräume gefunden.
                </x-slot:description>
            </x-alert>
        @endif

        @error('selectedDocSpace')
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </x-modal.container>
</x-modal.layout>
