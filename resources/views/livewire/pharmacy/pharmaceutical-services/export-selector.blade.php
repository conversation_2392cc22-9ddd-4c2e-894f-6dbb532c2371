<div>
    <div>
        <div>
            <x:modal name="download-start-modal" close-button="false" modal-model-name="open">
                <div>
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600"><use href="/icons.svg#check"/></svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-5">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            Abrechnungshilfe wird erstellt
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Wir erstellen Ihre Abrechnungshilfe. Dieser Prozess kann einige Zeit beanspruchen. Sobald die Abrechnungshilfe zum Download bereit ist benachrichtigen wir Sie per E-Mail.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-6">
                    <button x-on:click="$dispatch('close-modal')" type="button" class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-500 text-base font-medium text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                        Zurück
                    </button>
                </div>
            </x:modal>
        </div>
        <x:card>
            <p>Bitte wählen Sie den Abrechnungsmonat, für den die Abrechnungshilfe erstellt werden soll. Bitte legen Sie die bereitgestellte Unterlage als Abrechnungsunterlage auf Ihrem Computer ab, das Dokument wird auf dem Portal nicht dauerhaft gespeichert.</p>

            <div class="mt-4">
                <x:input.radio-group
                    wire:model.live="timeRangeRadio"
                    orientation="horizontal"
                    :labels="[1 => 'Pro Monat', 2 => 'Zeitraum']"
                />
            </div>

            @if($timeRangeRadio == 1)
                <form action="" wire:submit="downloadExportsMonthly" wire:key="timeRangeRadio-1">
                    <div class="flex flex-col mt-4 space-y-4 md:space-y-0 md:space-x-2 md:flex-row md:items-end">
                        <div class="w-full md:w-24">
                            <x:input.select
                                wire:model.live="year"
                                label="Jahr"
                                name="example_select"
                                :disabled="count($yearRange) === 1"
                            >
                                @foreach($yearRange as $range)
                                    <option value="{{ $range }}">{{ $range }}</option>
                                @endforeach
                            </x:input.select>
                        </div>

                        <div class="w-full md:w-32">
                            <x:input.select
                                wire:model.live="month"
                                label="Monat"
                                name="example_select"
                                :disabled="count($monthRange) === 1"
                            >
                                @foreach($monthRange as $range)
                                    <option value="{{ $range }}">{{ __('months.' . $range) }}</option>
                                @endforeach
                            </x:input.select>
                        </div>

                        <div>
                            <x:button type="submit" appearance="primary">Abrechnungshilfe erstellen</x:button>
                        </div>
                    </div>
                </form>
            @else
                <form action="" wire:submit="downloadExportsCustomTime" wire:key="timeRangeRadio-2">
                    <div class="flex flex-col mt-4 space-y-4 md:space-y-0 md:space-x-2 md:flex-row md:items-start">
                        <div>
                            <x:input.text
                                wire:model.live="export_start"
                                label="Start"
                                type="date"
                                name="export_start"
                                :error="$errors->first('export_start')"
                            />
                        </div>

                        <div>
                            <x:input.text
                                wire:model.live="export_end"
                                label="Ende"
                                type="date"
                                name="export_end"
                                :error="$errors->first('export_end')"
                            />
                        </div>

                        <div class="md:pt-6">
                            <x:button type="submit" appearance="primary">Abrechnungshilfe erstellen</x:button>
                        </div>
                    </div>
                </form>
            @endif
        </x:card>
    </div>
    <div class="mt-6">
        <div class="flex flex-col">
            <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Zeitraum
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ersteller
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Betrag
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                    <span class="sr-only">Herunterladen</span>
                                </th>
                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($downloads as $download)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $download->start_date->format('d.m.Y') }} - {{ $download->end_date->format('d.m.Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ optional($download->user)->name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $download->isFinished() ? \Illuminate\Support\Number::currency($download->price / 100, in: 'EUR', locale: 'de') : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        @if($download->isFinished())
                                            <a href="{{ route('pharmacies.pharmaceutical-services.download', [$pharmacy, $download]) }}" class="text-red-600 hover:text-red-900">Herunterladen</a>
                                        @else
                                            Wird generiert...
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center" colspan="4">
                                        Noch keine Abrechnungshilfe angelegt
                                    </td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-6">
                        {{ $downloads->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($this->currentlyExporting && $this->getPage() == 1)
        <div wire:poll.5000ms.keep-alive="pollForQueueEntries()"></div>
    @endif
</div>
