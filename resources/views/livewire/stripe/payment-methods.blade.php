<div class="space-y-4">
    @if ($this->paymentMethods->count() > 1)
        <div class="flex items-center justify-center" wire:poll.2s="$refresh">
            <flux:icon.loading class="size-12 animate-spin text-gray-500" />
        </div>
    @else
        @foreach ($this->paymentMethods as $paymentMethod)
            <div class="flex items-center justify-between gap-12">
                <div>
                    <div class="flex items-center gap-2">
                        <p class="font-black text-lg">{{ \App\Domains\Payment\Domain\Enums\PaymentMethodEnum::from($paymentMethod->type)->label() }}</p>

                        @if($pharmacy->defaultPaymentMethod()?->asStripePaymentMethod()->id === $paymentMethod->id)
                            <x-badge color="green">Standardzahlungsmethode</x-badge>
                        @endif
                    </div>

                    @if ($paymentMethod->type === \App\Domains\Payment\Domain\Enums\PaymentMethodEnum::SEPA_DEBIT->value)
                        <div class="mt-1">
                            <p class="text-sm text-gray-600 font-mono tracking-widest">
                                {{ str($paymentMethod->sepa_debit->country)->append(str('*')->repeat(16))->append($paymentMethod->sepa_debit->last4)->split(4)->join(' ') }}
                            </p>
                        </div>
                    @endif
                </div>

                <x-button appearance="outline" size="sm" wire:click="change">Zahlungsmethode ändern</x-button>
            </div>
        @endforeach
    @endif
</div>
