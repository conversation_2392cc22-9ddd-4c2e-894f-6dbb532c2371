<div>
    <x-card class="mb-8">
        <div class="md:-m-5 md:-mt-4">
            <div>
                <h2 class="text-xl font-bold text-gray-900">
                    {{ __('apomail.tos_confirmation_title') }}
                </h2>

                <p class="mt-4"> {{ __('apomail.tos_confirmation_description') }}</p>

                <ul class="mt-4 list-disc list-inside">
                    @foreach($pharmacies as $pharmacy)
                        <li>{{ $pharmacy->name }}</li>
                    @endforeach
                </ul>
            </div>

            <div class="mt-4">
                <x-tos.terms-of-service-links />
            </div>

            <div class="mt-4">
                <p>
                    Den aktuellen Auftragsverarbeitungsvertrag finden Sie hier:<br>
                </p>
                <p class="mt-1">
                    <a href="{{ asset('/downloads/apothekenportal_auftragsverarbeitungsvertrag.pdf') }}" target="_blank"
                       class="font-medium underline flex items-center hover:no-underline">
                        Auftragsverarbeitungsvertrag
                        <svg class="w-4 h-4 ml-1"><use href="/icons.svg#arrow-long-top-right-on-square"/></svg>
                    </a>
                </p>
            </div>

            <div class="mt-6">
                <form wire:submit="submit">
                    <div>
                        <x-input.checkbox id="tos" wire:model.live="tos" :error="$errors->first('tos')">
                            {{ __('apomail.tos_confirmation_checkbox_tos') }}
                        </x-input.checkbox>
                    </div>

                    <div class="mt-1">
                        <x-input.checkbox id="avv" wire:model.live="avv" :error="$errors->first('avv')">
                            {{ __('apomail.tos_confirmation_checkbox_avv') }}
                        </x-input.checkbox>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <x-button type="submit">{{ __('apomail.tos_confirmation_submit') }}</x-button>
                    </div>
                </form>
            </div>
        </div>
    </x-card>
</div>
