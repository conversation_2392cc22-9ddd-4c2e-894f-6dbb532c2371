<x-content>
    <x-header>
        <x-slot name="main">
            Bestellzentrale
        </x-slot>

        <x-slot name="section">
            Online-Shop bestellen
        </x-slot>

        <x-slot name="description">
            <p>
                Bitte wählen Sie hier die Apotheke(n) aus, für die Sie den Online-Shop bestellen wollen.
            </p>
            <div class="pt-6">
                <x-link href="{{ asset('downloads/flyer_ia.pdf') }}" target="_blank">
                    Informationen zum Onlineshop
                </x-link>
            </div>
        </x-slot>
    </x-header>

    <div
            class="mt-6 space-y-3"
            x-data="{
                pharmacies: @entangle('pharmacies').live,
                termsAccepted: @entangle('termsAccepted').live,
                privacyPolicyAccepted: @entangle('privacyPolicyAccepted').live,
            }"
    >
        <x-simple-card>
            <p
                class="mb-2 cursor-pointer text-sm text-red-600"
                @click="pharmacies = pharmacies.map(function (pharmacy) { pharmacy.selected = true; return pharmacy; })"
            >alle auswählen</p>
            @foreach ($pharmacies as $key => $pharmacy)
                <x-input.checkbox
                    id="{{ 'pharmacy-' . $key }}"
                    name="{{ 'pharmacy-' . $key }}"
                    label="{{ $pharmacy['name'] }}"
                    x-model="pharmacies[{{ $key }}].selected"
                    subheadline="{{ $pharmacy['address'] }}"
                    autocomplete="off"
                >
                    {{ $pharmacy['name'] }}
                </x-input.checkbox>
            @endforeach

            <p class="mt-6 text-sm">
                Den Online-Shop stellen wir bis zum 31.12.2024 kostenfrei zur Verfügung. Ab dem 01.01.2025 fallen
                hierfür monatlich 29,- Euro (netto) je Apotheke an. Weitere Details, auch bzgl. der Vertragslaufzeiten,
                entnehmen Sie bitte den Nutzungsbedingungen des Online-Shops.
            </p>

            @error('pharmacies')
                <p class="mt-2 text-sm text-red-600">{{ $errors->first('pharmacies') }}</p>
            @enderror
        </x-simple-card>

        <x-simple-card>
            <x-link
                class="mb-4 text-red-600"
                href="{{ asset('/downloads/nutzungsbedingungen_ia_neukunden.pdf') }}"
                target="_blank"
            >Ergänzende Nutzungsbedingungen herunterladen</x-link>
            <x-input.checkbox
                id="termsAccepted"
                name="termsAccepted"
                label="Ergänzende Nutzungsbedingungen akzeptieren"
                x-model="termsAccepted"
                subheadline="Ich bestätige, dass ich die ergänzenden Nutzungsbedingungen gelesen und verstanden habe. Ich stimme diesen Bedingungen zu."
                :error="$errors->first('termsAccepted')"
                autocomplete="off"
            >
                Ergänzende Nutzungsbedingungen akzeptieren
            </x-input.checkbox>
        </x-simple-card>

        <x-simple-card>
            <x-link
                class="mb-4 text-red-600"
                href="{{ asset('/downloads/datenschutzerklaerung_ia.pdf') }}"
                target="_blank"
            >Ergänzende Datenschutzbestimmungen herunterladen</x-link>
            <x-input.checkbox
                id="privacyPolicyAccepted"
                name="privacyPolicyAccepted"
                label="Ergänzende Datenschutzbestimmungen akzeptieren"
                x-model="privacyPolicyAccepted"
                subheadline="Ich stimme den ergänzenden Datenschutzbestimmungen zu und bin mit der Verwendung personenbezogener Daten einverstanden."
                :error="$errors->first('privacyPolicyAccepted')"
                autocomplete="off"
            >
                Ergänzende Datenschutzbestimmungen akzeptieren
            </x-input.checkbox>
        </x-simple-card>

        <x-button
            wire:loading.attr="disabled"
            wire:click="submit"
        >Jetzt kostenpflichtig bestellen</x-button>
    </div>
</x-content>
