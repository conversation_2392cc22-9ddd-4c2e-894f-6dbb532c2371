<x-mail::message>
{{ $customerName }},

vielen Dank für die verbindliche Bestellung Ihrer KIM-Adresse bei der GEDISA. Wir freuen uns sehr, dass Sie uns Ihr Vertrauen schenken.

Wir haben folgende Details Ihrer Bestellung aufgenommen:

KIM-Adresse: <a href="mailto:{{ $kimAddressEmail }}" target="_blank">{{ $kimAddressEmail }}</a>

Vertragsbeginn und -dauer: Ab erfolgter Installation und Aktivierung, initiale Laufzeit 3 Jahre

Kosten: {!! $priceText !!}

Alle hier angegebenen Preise verstehen sich zzgl. Mehrwertsteuer.

<br><br>
**Wie geht es weiter?**

Vereinbaren Sie jetzt einen Installationstermin für Ihre KIM-Adresse unter der Telefonnummer <a href="tel:03062937755">030 / 62 93 77 55</a> (Mo-Fr 08:00-17:00 Uh<PERSON> und Sa 09:00-13:00 Uhr).

Für eine reibungslose Remote-Installation nutzt unser Servicepartner die Software „TeamViewer“. Falls Sie diese nicht bereits installiert haben, können Sie sie unter diesem Link herunterladen und installieren: <a href="https://get.teamviewer.com/64d7zy9" target="_blank">TeamViewer</a>

Voraussetzung für die Installation des KIM Client-Moduls ist ein aktuelles Betriebssystem, d. h. mindestens Windows 10, Windows Server 2016 oder MacOS 10.13 (High Sierra).

Für die erfolgreiche Einrichtung des KIM-Dienstes ist es notwendig, dass die folgenden Angaben bereits im Vorfeld ermittelt werden. Dabei reicht es aus, wenn die Informationen zum Installationstermin bei Ihnen vorhanden sind.

- Verfügbarkeit einer funktionsfähigen TI-Anbindung:
- Sie betreiben einen E-Health-Konnektor (TI-Konnektor der Produkttypversion 3 oder aktueller).
- Ihre TI-Kartenterminals sind funktionsfähig und mit Ihrem Konnektor verbunden.
- Ihre SMC-B und ggf. eHBA (optional) steckt nutzungsbereit und freigeschaltet in einem TI-Kartenterminal.
- Die Netzwerkadresse (IP-Adresse) Ihres TI-Konnektors ist Ihnen bekannt.
- Der TI-Konnektor-Aufrufkontext für den Zugriff auf die Karte(n) ist Ihnen bekannt. Der Aufrufkontext besteht aus den Parametern Mandant-ID/Arbeitsplatz-ID/Clientsystem-ID.
- Die TLS-Authentifizierungsmethode des Konnektors (keine/Benutzername und Passwort/Zertifikat) muss bekannt sein und es muss, falls die TLS-Authentifizierungsmethode Passwort/Zertifikat ist, die p12-Datei inkl. Passwort vorhanden sein.

Bei Fragen hierzu wenden Sie sich an Ihren TI-Dienstleister.

Sollten Sie allgemeine Fragen zum GEDISA ApothekenPortal oder zu KIM haben, wenden Sie sich gerne an unseren Support unter <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>.

Bei Rückfragen sind wir immer gerne für Sie da.
</x-mail::message>
