import {get} from "scriptjs";

export default function init() {
    indexedDB.databases()
         .then((databases) => {

             const loggedInServerSide = document.querySelector("#logged-in").getAttribute("content") === "1";
             const canAccessChat = document.querySelector("#can-access-chat").getAttribute("content") === "1";

             if (! loggedInServerSide) {
                 let databases = [
                     "firebase-heartbeat-database",
                     "firebase-installations-database",
                     "firebase-messaging-database",
                     "firebaseLocalStorageDb",
                     "firebase_remote_config",
                     "hive_web_worker_database_versions",
                 ];

                 databases.forEach((databaseName) => {
                     try {
                         indexedDB.deleteDatabase(databaseName);
                     } catch (e) {
                         console.error(e)
                     }
                 });
             }


             let webchatDatabaseName = "apoconnect";

             if (! loggedInServerSide && undefined !== databases.find((database) => database.name === webchatDatabaseName)) {
                 const WebchatOpenRequest = indexedDB.open(webchatDatabaseName);

                 WebchatOpenRequest.onsuccess = function (e) {
                     const database = e.target.result;
                     const transaction = database.transaction(["box_client"], "readonly");
                     const store = transaction.objectStore("box_client");

                     const count = store.count();

                     count.onsuccess = function (e) {
                         const result = e.target.result;

                         /**
                          * If we have no user, we need to check if the famedly webchat is loaded. If it is, we need
                          * to trigger the famedly logout.
                          */
                         if (result >= 1 && !loggedInServerSide) {
                             get("/messenger/app/logout.js", async () => {
                                 try {
                                    await window.logOutApoPortalWebChat();
                                 } catch (e) {
                                     console.error(e);
                                 }
                             });
                         }
                     }
                 }
             }
         });
}
