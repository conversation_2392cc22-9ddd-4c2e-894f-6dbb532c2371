<?php

namespace App\Actions\Kim;

use App\Enums\Kim\KimClientKonManEnum;
use App\Enums\Kim\KimClientMailSystemEnum;
use App\Enums\Kim\KimClientOSEnum;
use App\Enums\Kim\KimClientRemoteHelpSoftwareEnum;
use App\Enums\KimAddressStatus;
use App\Events\Kim\KimAddressOrdered;
use App\Helper\Kim\KimRiseAddressApi;
use App\KimAddress;
use App\Mail\KimAddressOrderConfirmationMail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CreateNewKimAddressAtRise
{
    private KimRiseAddressApi $kimAddressRiseApi;

    public function __construct(KimRiseAddressApi $kimAddressRiseApi)
    {
        $this->kimAddressRiseApi = $kimAddressRiseApi;
    }

    public function execute(array $input, KimAddress $kimAddress): KimAddress
    {
        Gate::authorize('create', [KimAddress::class, $kimAddress->pharmacy]);

        $validated = Validator::validate($input, [
            'tosCheckboxAccepted' => ['required', 'accepted'],
            'kimClientOS' => ['required', Rule::in(KimClientOSEnum::names())],
            'kimClientKonMan' => ['required', Rule::in(KimClientKonManEnum::names())],
            'kimClientMailSystem' => ['required', Rule::in(KimClientMailSystemEnum::names())],
            'kimClientRemoteHelpSoftware' => ['required', Rule::in(KimClientRemoteHelpSoftwareEnum::names())],
        ]);

        DB::transaction(function () use ($validated, &$kimAddress) {
            $kimAddress->update([
                'additional' => $validated,
            ]);

            $orderData =
                $this->kimAddressRiseApi->createOrder($kimAddress);

            //            if ($orderData->isEmpty()) {
            //                throw new ApiResponseException('Response has no data');
            //            }

            $kimAddress->update([
                'order_number' => $orderData->get('orderNumber'),
                'status' => KimAddressStatus::ORDERED->value,
                'ordered_at' => now(),
            ]);

            Mail::to($kimAddress->owner()->email)
                ->send(new KimAddressOrderConfirmationMail(
                    $kimAddress->owner(),
                    $kimAddress->email,
                    $kimAddress->pharmacy->activeSubscriptions()->first()?->plan ?? '',
                    $kimAddress->owner()->belongsToAnyAssociation()
                ));
        });

        event(new KimAddressOrdered($kimAddress));

        return $kimAddress;
    }
}
