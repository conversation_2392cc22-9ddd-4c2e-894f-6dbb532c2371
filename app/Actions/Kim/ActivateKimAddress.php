<?php

namespace App\Actions\Kim;

use App\Enums\Kim\FailedKimReportStatus;
use App\Enums\KimAddressStatus;
use App\Events\Kim\KimAddressActivated;
use App\FailedKimReport;
use App\KimAddress;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class ActivateKimAddress
{
    public function execute(KimAddress $kimAddress, string $changeDate, string $usedVoucherCode): KimAddress
    {
        DB::transaction(function () use ($usedVoucherCode, $kimAddress, $changeDate) {

            if (! $kimAddress->additional) {
                throw new RuntimeException('Kim address has no additional data');
            }

            if ($kimAddress->additional['voucherCode'] !== $usedVoucherCode) {
                $otherKimAddress = KimAddress::where('additional', 'LIKE', '%'.$usedVoucherCode.'%')->first();

                if ($otherKimAddress && $otherKimAddress->additional) {
                    if ($otherKimAddress->pharmacy_id !== $kimAddress->pharmacy_id) {
                        FailedKimReport::report($kimAddress, ['code' => $usedVoucherCode], FailedKimReportStatus::CODE_FOUND_IN_OTHER_PHARMACY);
                        throw new RuntimeException('Voucher code: '.$usedVoucherCode.' found in an address of another pharmacy: '.$otherKimAddress->pharmacy_id);
                    }

                    $otherKimAddress->update([
                        'additional' => array_merge($otherKimAddress->additional, ['voucherCode' => $kimAddress->additional['voucherCode']]),
                    ]);
                }
            }

            $kimAddress->update([
                'status' => KimAddressStatus::ACTIVATED->value,
                'activated_at' => Carbon::parse($changeDate),
                'additional' => array_merge($kimAddress->additional, ['voucherCode' => $usedVoucherCode]),
            ]);

            event(new KimAddressActivated($kimAddress));
        });

        return $kimAddress;
    }
}
