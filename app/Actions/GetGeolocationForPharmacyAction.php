<?php

namespace App\Actions;

use App\Enums\StateEnum;
use App\Exceptions\LocationNotFoundException;
use App\PharmacyAddress;
use App\State;

class GetGeolocationForPharmacyAction
{
    public function execute(PharmacyAddress $address)
    {
        if (config('geocoder.enabled') == true) {
            $result = app('geocoder')->geocode($address->string_address)->get()->first();

            throw_unless($result, LocationNotFoundException::class);

            $coordinates = $result->getCoordinates();
            $address->latitude = $coordinates->getLatitude();
            $address->longitude = $coordinates->getLongitude();

            if ($state = $result->getAdminLevels()->first()) {
                $stateName = StateEnum::getShortName($state->getName());

                $state = State::query()->where('short_name', $stateName)->first();

                if ($state) {
                    $address->state_id = $state->id;
                }
            }
        }

        return $address;
    }
}
