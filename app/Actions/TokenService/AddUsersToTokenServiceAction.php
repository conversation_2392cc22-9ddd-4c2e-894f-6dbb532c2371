<?php

namespace App\Actions\TokenService;

use App\Jobs\TokenService\CreateTokenServiceUserJob;
use App\User;
use Illuminate\Support\Collection;

class AddUsersToTokenServiceAction
{
    /**
     * @param  Collection<User>|User  $users
     */
    public function execute(Collection|User $users): void
    {
        if ($users instanceof User) {
            $users = collect([$users]);
        }

        foreach ($users as $user) {
            CreateTokenServiceUserJob::dispatch($user)->afterCommit();
        }
    }
}
