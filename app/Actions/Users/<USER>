<?php

namespace App\Actions\Users;

use App\Association;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Support\Str;
use RuntimeException;

class CreateDatabaseUserAction
{
    private ?User $user = null;

    public function create(array $data, ?string $uuid = null): static
    {
        $this->user = User::make($data);

        $this->user->uuid = $uuid ?? Str::uuid()->toString();

        $this->user->save();

        return $this;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function asPharmacyUser(array $data = []): static
    {
        $this->user->pharmacyProfile()->create($data);

        return $this;
    }

    public function asAssociationUser(array $data = []): static
    {
        $this->user->associationProfile()->create($data);

        return $this;
    }

    public function assignToPharmacy(Pharmacy $pharmacy, string $role, array $permissions): static
    {
        if (is_null($this->user)) {
            return $this;
        }

        $pharmacy->assignUser($this->user, $role, $permissions);

        return $this;
    }

    public function assignToAssociation(Association $association, string $role, array $permissions): static
    {
        $association->assignUser(
            $this->user,
            $role,
            $permissions
        );

        return $this;
    }

    /**
     * @param  array<int>  $pharmacyIds
     */
    public function syncCompanyUserPharmacies(
        User $actingUser,
        array $pharmacyIds = [],
        string $role = PharmacyRoleEnum::SUB_OWNER
    ): self {
        if (! ($this->user instanceof User)) {
            throw new RuntimeException('User not found');
        }

        $pharmacies = $this->user->pharmacies;
        $selectedPharmacies = Pharmacy::whereIn('id', $pharmacyIds)->get();

        foreach ($pharmacies as $pharmacy) {
            if (! in_array($pharmacy->id, $pharmacyIds)) {
                $pharmacy->unassignUser($this->user);
            }
        }

        foreach ($selectedPharmacies as $pharmacy) {
            if ($pharmacies->doesntContain($pharmacy)
                && $actingUser->can('administratePharmacySubOwners', $pharmacy)) {
                $pharmacy->assignUser($this->user, $role);
            }
        }

        return $this;
    }

    public function get(): ?User
    {
        return $this->user;
    }
}
