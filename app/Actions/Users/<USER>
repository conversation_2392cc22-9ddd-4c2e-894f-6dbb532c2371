<?php

namespace App\Actions\Users;

use App\Actions\AnonymizePharmaceuticalServiceAction;
use App\Actions\AnonymizeVaccinationAction;
use App\CardLinkOrder;
use App\CompanyUser;
use App\PharmaceuticalService;
use App\Shift;
use App\ShiftPlanBetaUser;
use App\ShiftPlanGroupUser;
use App\User;
use App\UserPharmacyProfile;
use App\Vaccination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeletePharmacyEmployee
{
    public function execute(User $user): void
    {
        DB::beginTransaction();

        try {
            // 1. Delete shift plan related data
            $this->deleteShiftPlanData($user);

            // 2. Anonymize pharmaceutical services
            $this->anonymizePharmaceuticalServices($user);

            // 3. Anonymize vaccinations
            $this->anonymizeVaccinations($user);

            // 4. Handle CardLinkOrders
            $this->handleCardLinkOrders($user);

            // 5. Clean up company user data
            $this->cleanupCompanyUserData($user);

            // 6. Clean up user profile data
            $this->cleanupUserProfileData($user);

            // 7. Clean up user data (existing functionality)
            $this->cleanupUserData($user);

            DB::commit();

            Log::info('Successfully deleted pharmacy employee', [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete pharmacy employee', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    private function deleteShiftPlanData(User $user): void
    {
        // Delete all shifts assigned to this user through ShiftPlanGroupUser
        $shiftPlanGroupUsers = ShiftPlanGroupUser::where('user_id', $user->id)->get();

        foreach ($shiftPlanGroupUsers as $shiftPlanGroupUser) {
            // Delete all shifts for this user
            Shift::where('shift_plan_group_user_id', $shiftPlanGroupUser->id)->delete();
        }

        // Delete ShiftPlanGroupUser entries
        ShiftPlanGroupUser::where('user_id', $user->id)->delete();

        // Delete ShiftPlanBetaUser entries
        ShiftPlanBetaUser::where('user_id', $user->id)->delete();
    }

    private function anonymizePharmaceuticalServices(User $user): void
    {
        $pharmaceuticalServices = PharmaceuticalService::where('user_id', $user->id)->get();

        foreach ($pharmaceuticalServices as $service) {
            // Anonymize patient data
            app(AnonymizePharmaceuticalServiceAction::class)->execute($service);

            // Remove user reference
            $service->update(['user_id' => null]);
        }
    }

    private function anonymizeVaccinations(User $user): void
    {
        $vaccinations = Vaccination::where('user_id', $user->id)->get();

        foreach ($vaccinations as $vaccination) {
            // Anonymize patient data
            app(AnonymizeVaccinationAction::class)->execute($vaccination, true);

            // Remove user reference
            $vaccination->update(['user_id' => null]);
        }
    }

    private function handleCardLinkOrders(User $user): void
    {
        // Transfer CardLinkOrders to pharmacy owner (as done in PharmacyRoleUserObserver)
        $cardLinkOrders = CardLinkOrder::where('user_id', $user->id)->get();

        foreach ($cardLinkOrders as $order) {
            $pharmacy = $order->pharmacy;
            $owner = $pharmacy?->owner();

            if ($owner) {
                $order->update(['user_id' => $owner->id]);
            } else {
                // If no owner found, we could either delete the order or set user_id to null
                // Based on the existing logic in PharmacyRoleUserObserver, we expect an owner
                Log::warning('No owner found for CardLinkOrder when deleting employee', [
                    'order_id' => $order->id,
                    'pharmacy_id' => $pharmacy?->id,
                    'user_id' => $user->id,
                ]);
                $order->update(['user_id' => null]);
            }
        }
    }

    private function cleanupCompanyUserData(User $user): void
    {
        // Delete CompanyUser record if exists
        CompanyUser::where('user_id', $user->id)->delete();

        // Update any UserPharmacyProfile records that reference this user as company_user_id
        UserPharmacyProfile::where('company_user_id', $user->id)
            ->update(['company_user_id' => null]);
    }

    private function cleanupUserProfileData(User $user): void
    {
        // Delete UserPharmacyProfile
        UserPharmacyProfile::where('user_id', $user->id)->delete();
    }

    private function cleanupUserData(User $user): void
    {
        // Reset user login in order to make the email address and username available for registration
        $user->update([
            'email' => null,
            'username' => null,
        ]);
    }
}
