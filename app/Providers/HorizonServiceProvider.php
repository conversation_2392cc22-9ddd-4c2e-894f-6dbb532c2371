<?php

namespace App\Providers;

use App\Enums\StaffRoleEnum;
use Illuminate\Support\Facades\Gate;
use Laravel\Horizon\Horizon;
use Laravel\Horizon\HorizonApplicationServiceProvider;

class HorizonServiceProvider extends HorizonApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        if (config('horizon.notifications_email')) {
            Horizon::routeMailNotificationsTo(config('horizon.notifications_email'));
        }
    }

    /**
     * Register the Horizon gate.
     *
     * This gate determines who can access Horizon in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewHorizon', function ($user = null) {
            if (config('services.horizon.token') && request()->bearerToken() && request()->bearerToken() === config('services.horizon.token')) {
                return true;
            }

            return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN);
        });
    }
}
