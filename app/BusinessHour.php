<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class BusinessHour
 *
 * @mixin IdeHelperBusinessHour
 */
class BusinessHour extends Model
{
    use HasFactory;

    protected $touches = ['pharmacy'];

    protected $fillable = [
        'day_of_week',
        'opens',
        'closes',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
