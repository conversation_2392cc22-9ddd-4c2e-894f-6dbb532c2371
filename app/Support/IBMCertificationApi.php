<?php

namespace App\Support;

use Illuminate\Support\Facades\Http;

class IBMCertificationApi
{
    public function issueCertificate(array $data, string $type = 'pdf')
    {
        $accept = 'application/pdf';

        if ($type == 'code') {
            $accept = 'application/cbor+base45';
        }

        $url = config('services.ibm.vaccination-center.url');
        $response = Http::withOptions([
            'cert' => [storage_path('ibm-key.pem'), config('services.ibm.vaccination-center.private-key-password')],
        ])
            ->timeout(config('services.ibm.vaccination-center.timeout', 5))
            ->withHeaders([
                'Accept' => $accept,
                'Content-Type' => 'application/json',
            ])->post($url, [
                'nam' => [
                    'fn' => $data['last_name'],
                    'gn' => $data['first_name'],
                ],
                'dob' => $data['birthdate'],
                'v' => [
                    [
                        'id' => $data['vaccination_center_uuid'],
                        'tg' => $data['disease_id'],
                        'vp' => $data['prophylaxis_id'],
                        'mp' => $data['medicinal_product_id'],
                        'ma' => $data['auth_holder_id'],
                        'dn' => $data['shot_one_number'],
                        'sd' => $data['shot_two_number'],
                        'dt' => $data['date_of_shot'],
                    ],
                ],
            ]);

        if ($response->failed()) {
            try {
                $response->throw();
            } catch (\Exception $e) {
                report($e);
            }
        }

        return $response;
    }
}
