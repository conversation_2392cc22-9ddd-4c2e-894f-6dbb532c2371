<?php

namespace App\Support;

use Guz<PERSON><PERSON>ttp\Client;
use Guzzle<PERSON>ttp\ClientInterface as GuzzleClientInterface;
use Laminas\Feed\Reader\Http\ClientInterface as FeedReaderHttpClientInterface;
use Lam<PERSON>\Feed\Reader\Http\Psr7ResponseDecorator;

class GuzzleClient implements FeedReaderHttpClientInterface
{
    /**
     * @var GuzzleClientInterface
     */
    private $client;

    public function __construct(?GuzzleClientInterface $client = null)
    {
        $this->client = $client ?: new Client;
    }

    /**
     * {@inheritdoc}
     */
    public function get($uri)
    {
        return new Psr7ResponseDecorator(
            $this->client->request('GET', $uri)
        );
    }
}
