<?php

namespace App\Support\MoveKimAddress;

use App\Data\Akquinet\AkquinetSubCustomerData;
use App\Enums\Kim\MoveKimAddressCaseEnum;

class MoveKimAddressSpecialCase1 extends AbstractMoveKimAddress
{
    protected function processCase(): void
    {
        $this->updateCanceledKimAddress();
        $this->targetSubCustomer = $this->targetSubCustomer();
    }

    protected function resolveCase(): MoveKimAddressCaseEnum
    {
        return MoveKimAddressCaseEnum::SPECIAL_CASE_1;
    }

    protected function targetSubCustomer(): AkquinetSubCustomerData
    {
        $subCustomerData = $this->targetPharmacy->kimAddresses()->first()?->additional;

        return AkquinetSubCustomerData::from($subCustomerData);
    }
}
