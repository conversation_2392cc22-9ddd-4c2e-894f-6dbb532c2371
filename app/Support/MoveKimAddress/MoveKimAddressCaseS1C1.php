<?php

namespace App\Support\MoveKimAddress;

use App\Data\Akquinet\AkquinetSubCustomerData;
use App\Enums\Kim\MoveKimAddressCaseEnum;

class MoveKimAddressCaseS1C1 extends AbstractMoveKimAddress
{
    protected function processCase(): void
    {
        $this->targetSubCustomer = $this->targetSubCustomer();
    }

    protected function resolveCase(): MoveKimAddressCaseEnum
    {
        return MoveKimAddressCaseEnum::S1C1;
    }

    protected function targetSubCustomer(): AkquinetSubCustomerData
    {
        if (! $this->kimAddress->additional) {
            throw new \RuntimeException('KimAddress additional data is missing');
        }

        return AkquinetSubCustomerData::from([
            'email' => $this->targetPharmacy->uuid.'@gedisa.de',
            'organisation' => $this->targetPharmacy->name,
            'referenceId' => $this->targetPharmacy->uuid,
            'customerNumber' => $this->kimAddress->additional['customerNumber'],
            'supportPin' => $this->kimAddress->additional['supportPin'],
        ]);
    }
}
