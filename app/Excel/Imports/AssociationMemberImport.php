<?php

namespace App\Excel\Imports;

use App\Enums\SalutationEnum;
use App\Jobs\InstantRegistrationJob;
use App\RuleSets\RegistrationRuleSet;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class AssociationMemberImport extends DefaultValueBinder implements SkipsEmptyRows, ToCollection, WithCustomValueBinder, WithHeadingRow, WithValidation
{
    use Importable;

    public function __construct(
        private readonly int $associationId
    ) {}

    public const COLUMNS = [
        'anrede_auswahlfeld' => 'Anrede (Auswahlfeld)',
        'akadem_grad' => 'Akadem. Grad',
        'vorname_inhaber' => 'Vorname Inhaber',
        'nachname_inhaber' => 'Nachname Inhaber',
        'e_mail_adresse_inhaber' => 'E-Mail-Adresse Inhaber',
        'name_der_apotheke' => 'Name der Apotheke',
        'adresszusatz' => 'Adresszusatz',
        'strasse_hausnr' => 'Straße + Hausnr.',
        'postleitzahl' => 'Postleitzahl',
        'ort' => 'Ort',
        'datum_format_ttmmyyyy' => 'Datum (Format: TT.MM.YYYY)',
    ];

    private const DATE_FORMAT = 'd.m.Y';

    public function headingRow()
    {
        return 2;
    }

    public function bindValue(Cell $cell, $value)
    {
        $value = $this->formatDate($cell, $value);

        return parent::bindValue($cell, $value);
    }

    public function formatDate(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'L' && $cell->getRow() >= 3 && ! is_null($value)) {
            $cell->getStyle()->getNumberFormat()->setFormatCode('dd.mm.yyyy');

            $value = Date::excelToDateTimeObject($value)->format(self::DATE_FORMAT);
        }

        return $value;
    }

    public function rules(): array
    {
        return RegistrationRuleSet::make()
            ->rename('salutation', 'anrede_auswahlfeld')
            ->rename('title', 'akadem_grad')
            ->rename('first_name', 'vorname_inhaber')
            ->rename('last_name', 'nachname_inhaber')
            ->rename('email', 'e_mail_adresse_inhaber')
            ->rename('company_name', 'ohg_name')
            ->rename('pharmacy_name', 'name_der_apotheke')
            ->rename('street', 'strasse_hausnr')
            ->rename('postcode', 'postleitzahl')
            ->rename('city', 'ort')
            ->put('house_number', ['nullable'])
            ->put('phone', ['nullable'])
            ->put('telematics_id', ['nullable'])
            ->put('anrede_auswahlfeld', ['required', 'string', Rule::in(SalutationEnum::getLabels())])
            ->put('e_mail_adresse_inhaber', [
                'required',
                'email:rfc,dns',
                'not_regex:/^.+@apomail\.de/i',
                'max:255',
            ])
            ->put('adresszusatz', ['nullable', 'string', 'max:255'])
            ->put('datum_format_ttmmyyyy', ['required', 'date', 'date_format:'.self::DATE_FORMAT])
            ->get();
    }

    public function collection(Collection $rows): void
    {
        $rows->each(function ($row) {
            $mappedRow = [
                'association_id' => $this->associationId,
                'salutation' => array_flip(SalutationEnum::getLabels())[$row->get('anrede_auswahlfeld')],
                'title' => $row->get('akadem_grad'),
                'first_name' => $row->get('vorname_inhaber'),
                'last_name' => $row->get('nachname_inhaber'),
                'email' => $row->get('e_mail_adresse_inhaber'),
                'is_company' => ! empty($row->get('ohg_name')),
                'company_name' => $row->get('ohg_name'),
                'pharmacy_name' => $row->get('name_der_apotheke'),
                'optional_address_line' => $row->get('adresszusatz'),
                'street' => $row->get('strasse_hausnr'),
                'postcode' => $row->get('postleitzahl'),
                'city' => $row->get('ort'),
            ];
            dispatch(new InstantRegistrationJob($mappedRow, true));
            Log::channel('association-member-import')->info(
                'Asssociation Member Import Registration Job dispatched',
                [
                    'user' => user()?->uuid ?? user()?->id ?? 'N/A',
                    'associationId' => $this->associationId,
                    'row' => implode(':', $mappedRow),
                ]
            );
        });
    }
}
