<?php

namespace App\Enums\Vaccinate;

use Illuminate\Support\Collection;

class InformationPlacesEnum
{
    //const DID_BEFORE = 0; Erst ab zweitem Jahr des Modellprojekts
    const PHARMACY_STAFF = 1;

    const INFORMATION_MATERIAL_OF_PHARMACY = 2;

    const MEDICAL_OFFICE = 3;

    const HEALTH_INSURANCE_COMPANY = 4;

    const ADVERTISEMENT = 5;

    const RECOMMENDATION = 6;

    const INTERNET = 7;

    const OTHER = 8;

    public static function getAll(): Collection
    {
        return collect([
            //self::DID_BEFORE,
            self::PHARMACY_STAFF,
            self::INFORMATION_MATERIAL_OF_PHARMACY,
            self::MEDICAL_OFFICE,
            self::HEALTH_INSURANCE_COMPANY,
            self::ADVERTISEMENT,
            self::RECOMMENDATION,
            self::INTERNET,
            self::OTHER,
        ]);
    }

    public static function getForDropdown()
    {
        return [
            //self::DID_BEFORE => self::DID_BEFORE,
            self::PHARMACY_STAFF => self::PHARMACY_STAFF,
            self::INFORMATION_MATERIAL_OF_PHARMACY => self::INFORMATION_MATERIAL_OF_PHARMACY,
            self::MEDICAL_OFFICE => self::MEDICAL_OFFICE,
            self::HEALTH_INSURANCE_COMPANY => self::HEALTH_INSURANCE_COMPANY,
            self::ADVERTISEMENT => self::ADVERTISEMENT,
            self::RECOMMENDATION => self::RECOMMENDATION,
            self::INTERNET => self::INTERNET,
            self::OTHER => self::OTHER,
        ];
    }

    public static function getShortLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            //case self::DID_BEFORE:
            //    return 'Schon mal gemacht';
            case self::PHARMACY_STAFF:
                return 'Apothekenpersonal';
            case self::INFORMATION_MATERIAL_OF_PHARMACY:
                return 'Informationsmaterial von Apotheke';
            case self::MEDICAL_OFFICE:
                return 'Arztpraxis';
            case self::HEALTH_INSURANCE_COMPANY:
                return 'Krankenversicherung';
            case self::ADVERTISEMENT:
                return 'Anzeige';
            case self::RECOMMENDATION:
                return 'Mundpropaganda';
            case self::INTERNET:
                return 'Internet';
            case self::OTHER:
                return 'Sonstiges';
        }

        return '';
    }

    public static function getShortLabels($values): array
    {
        if (! $values) {
            return [];
        }

        $labels = [];
        foreach ($values as $value) {
            array_push($labels, self::getShortLabel($value));
        }

        return $labels;
    }
}
