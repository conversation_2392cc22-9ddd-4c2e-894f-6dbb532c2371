<?php

namespace App\Enums;

enum UploadVisibility: string
{
    case Public = 'Public';

    case Authenticated = 'Protected';

    public function label(): string
    {
        return self::labels()[$this->value];
    }

    /**
     * @return array<string>
     */
    public static function labels(): array
    {
        return [
            self::Public->value => 'öffentlich',
            self::Authenticated->value => 'nur für eingeloggte Nutzer',
        ];
    }
}
