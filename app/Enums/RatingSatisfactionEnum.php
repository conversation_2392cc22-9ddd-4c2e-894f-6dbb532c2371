<?php

namespace App\Enums;

use Illuminate\Support\Collection;

class RatingSatisfactionEnum
{
    const VERY_SATISFIED = 0;

    const SATISFIED = 1;

    const LITTLE_SATISFIED = 2;

    const NOT_SATISFIED = 3;

    const UNKNOWN = 4;

    public static function getAll(): Collection
    {
        return collect([
            self::VERY_SATISFIED,
            self::SATISFIED,
            self::LITTLE_SATISFIED,
            self::NOT_SATISFIED,
            self::UNKNOWN,
        ]);
    }

    public static function getLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            case self::VERY_SATISFIED:
                return 'Sehr zufrieden';
            case self::SATISFIED:
                return 'Ziemlich zufrieden';
            case self::LITTLE_SATISFIED:
                return '<PERSON><PERSON> zufrieden';
            case self::NOT_SATISFIED:
                return 'Gar nicht zufrieden';
            case self::UNKNOWN:
                return 'Weiß nicht';
        }

        return '';
    }

    public static function getForDropdownWithTranslation()
    {
        return collect([
            self::VERY_SATISFIED => __('vaccination.poll.satisfaction.'.self::VERY_SATISFIED),
            self::SATISFIED => __('vaccination.poll.satisfaction.'.self::SATISFIED),
            self::LITTLE_SATISFIED => __('vaccination.poll.satisfaction.'.self::LITTLE_SATISFIED),
            self::NOT_SATISFIED => __('vaccination.poll.satisfaction.'.self::NOT_SATISFIED),
            self::UNKNOWN => __('vaccination.poll.satisfaction.'.self::UNKNOWN),
        ]);
    }
}
