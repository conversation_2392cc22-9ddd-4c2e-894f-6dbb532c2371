<?php

namespace App\Enums\Retax;

use App\Enums\AssociationRoleEnum;
use App\Enums\PharmacyRoleEnum;

enum RetaxPermissionEnum: string
{
    case RETAX_EMPLOYEE = 'retax_employee';

    case RETAX_OWNER = 'retax_owner';

    case RETAX_KEY_USER = 'retax_key_user';

    case RETAX_LEADER = 'retax_leader';

    public static function byPharmacyRoleName(string $roleName): ?self
    {
        return match ($roleName) {
            PharmacyRoleEnum::OWNER => self::RETAX_OWNER,
            PharmacyRoleEnum::EMPLOYEE => self::RETAX_EMPLOYEE,
            default => null,
        };
    }

    public static function byAssociationRoleName(string $roleName): ?self
    {
        return match ($roleName) {
            AssociationRoleEnum::ADMIN => self::RETAX_LEADER,
            AssociationRoleEnum::EMPLOYEE => self::RETAX_EMPLOYEE,
            default => null,
        };
    }
}
