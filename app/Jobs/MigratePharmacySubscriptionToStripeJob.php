<?php

namespace App\Jobs;

use App\Domains\Subscription\Application\Discounts\StripeDiscount;
use App\Domains\Subscription\Application\Helper\DiscountApplicationHelper;
use App\Domains\Subscription\Application\StripeProducts\AddOns\CardLinkStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\AddOns\IAStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\CardLinkPacketMPlusStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\CardLinkPacketMStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Enums\PaymentMethod;
use App\Integrations\IntegrationTypeEnum;
use App\Pharmacy;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Laravel\Cashier\Cashier;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Laravel\Cashier\Subscription;

class MigratePharmacySubscriptionToStripeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Pharmacy $pharmacy) {}

    public function handle()
    {
        require_once storage_path('cardlink-addons-pre-2025.php');

        $this->migrate($this->pharmacy);
    }

    public function migrate(Pharmacy $pharmacy): void
    {
        //        if ($pharmacy->subscribed()) {
        //            return;
        //        }

        $cardLinkOrderedAt = null;
        $iAOrderedAt = null;
        $cardLinkOrderedBeforeStartOfYear = false;
        $iAOrderedBeforeStartOfYear = false;

        if ($pharmacy->cardLinkOrder && $pharmacy->cardLinkOrder->activated_at) {
            if ($pharmacy->cardLinkOrder->activated_at->lt(now()->startOfYear())) {
                $cardLinkOrderedBeforeStartOfYear = true;
                $cardLinkOrderedAt = now('europe/berlin')->startOfYear();
            } else {
                $cardLinkOrderedAt = $pharmacy->cardLinkOrder->activated_at->timezone('Europe/Berlin');
            }
        }

        $ia = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken);

        if ($ia && ! $ia->settings->isZpaCustomer) {

            if ($ia->created_at->lt(now()->startOfYear())) {
                $iAOrderedBeforeStartOfYear = true;
                $iAOrderedAt = now('europe/berlin')->startOfYear();
            } else {
                $iAOrderedAt = $ia->created_at->timezone('Europe/Berlin');
            }
        }

        if (! $cardLinkOrderedAt && ! $iAOrderedAt) {
            return;
        }

        $pharmacy->update([
            'selected_pm_type' => PaymentMethod::Invoice,
        ]);

        if ($cardLinkOrderedBeforeStartOfYear && $iAOrderedBeforeStartOfYear) {
            $this->startSubscriptionForIaAndCardLinkFullQuarter($pharmacy);

            return;
        }

        if ($cardLinkOrderedBeforeStartOfYear && ! $iAOrderedAt) {
            $this->startSubscriptionForCardLinkOnlyFullQuarter($pharmacy);

            return;
        }

        if ($cardLinkOrderedAt && ! $iAOrderedAt) {
            $this->startSubscriptionForCardLinkOnlyPartialQuarter($pharmacy, $cardLinkOrderedAt);

            return;
        }

        if (! $cardLinkOrderedAt && $iAOrderedBeforeStartOfYear) {
            $this->startSubscriptionForIaOnlyFullQuarter($pharmacy);

            return;
        }

        if (! $cardLinkOrderedAt && $iAOrderedAt) {
            $this->startSubscriptionForIaOnlyPartialQuarter($pharmacy, $iAOrderedAt);

            return;
        }

        if ($cardLinkOrderedAt->lte($iAOrderedAt)) {
            $this->cardLinkOrderedBeforeIa($pharmacy, $cardLinkOrderedAt, $iAOrderedAt);

            return;
        }

        if ($cardLinkOrderedAt->gt($iAOrderedAt)) {
            $this->cardLinkOrderedAfterIa($pharmacy, $cardLinkOrderedAt, $iAOrderedAt);

            return;
        }

        throw new \Exception('This should not happen');
    }

    private function startSubscriptionForIaAndCardLinkFullQuarter(Pharmacy $pharmacy)
    {
        return $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), IAStripeProduct::make(), CardLinkStripeProduct::make()]);
    }

    private function startSubscriptionForCardLinkOnlyFullQuarter(Pharmacy $pharmacy)
    {
        return $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), CardLinkStripeProduct::make()]);
    }

    private function startSubscriptionForIaOnlyFullQuarter(Pharmacy $pharmacy)
    {
        return $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), IAStripeProduct::make()]);
    }

    /**
     * @param  StripeProduct[]  $products
     *
     * @throws IncompletePayment
     */
    private function startSubscriptionFromDate(Pharmacy $pharmacy, array $products, $date = null, ?array $productsWithAmount = null): ?Subscription
    {
        if (! $date) {
            $date = now('Europe/Berlin')->startOfYear();
        }

        $pharmacy->updateOrCreateStripeCustomer();

        if ($pharmacy->subscribed()) {
            $subscription = $pharmacy->subscription();
        } else {

            $subscription = $pharmacy->newSubscription('default', collect($products)->map(fn ($product) => $product->getRecurringStripePrice($pharmacy)->stripePriceId)->toArray())
                // pharmacy cant have a payment method. We send invoice with sepa mandate payment
                ->create(null, [], array_merge(
                    [
                        'backdate_start_date' => $date->timestamp,
                        'collection_method' => 'send_invoice',
                        'proration_behavior' => $date->eq(now('Europe/Berlin')->startOfYear()) ? 'none' : 'create_prorations',
                        'days_until_due' => 14,
                        'billing_cycle_anchor' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                        'discounts' => collect(app(DiscountApplicationHelper::class)->getDiscountsForOrder($pharmacy, $products))->map(fn (StripeDiscount $discount) => ['coupon' => $discount->getSettings()->discount_id])->toArray(),
                        'payment_settings' => [
                            'payment_method_types' => ['customer_balance'],
                        ],
                        //                    'add_invoice_items' => $this->getOneTimeAddons($pharmacy),
                    ],
                ));
        }

        if ($date->eq(now('Europe/Berlin')->startOfYear())) {
            $lines = [];

            $invoices = Cashier::stripe()->invoices->all(['subscription' => $subscription->stripe_id]);

            $invoice = $invoices->first() ?? Cashier::stripe()->invoices->create([
                'subscription' => $subscription->stripe_id,
                'customer' => $pharmacy->stripe_id,
                'automatically_finalizes_at' => now('Europe/Berlin')->addHours(2)->timestamp,
                'auto_advance' => true,
                'collection_method' => 'send_invoice',
                'days_until_due' => 14,
                'payment_settings' => [
                    'payment_method_types' => ['customer_balance'],
                ],
            ]);

            if ($invoice->amount_due !== 0) {
                return $subscription;
            }

            if ($productsWithAmount) {
                foreach ($productsWithAmount as $product) {
                    $lines[] = [
                        'description' => $product['class']->getPublicRepresentationData($pharmacy)->text->name,
                        'period' => [
                            'end' => $product['start'],
                            'start' => $product['end'],
                        ],
                        'quantity' => $product['amount'],
                        'price' => $product['class']->getOneTimeStripePrice($pharmacy)->stripePriceId,
                    ];
                }
            } else {
                foreach ($products as $product) {
                    $lines[] = [
                        'description' => $product->getPublicRepresentationData($pharmacy)->text->name,
                        'period' => [
                            'end' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                            'start' => now('Europe/Berlin')->startOfQuarter()->timestamp,
                        ],
                        'quantity' => 3,
                        'price' => $product->getOneTimeStripePrice($pharmacy)->stripePriceId,
                    ];
                }
            }

            Cashier::stripe()->invoices->addLines(
                $invoice->id,
                [
                    'lines' => [
                        ...$this->getOneTimeAddons($pharmacy),
                        ...$lines,
                    ],
                ]
            );
        }

        return $subscription;
    }

    private function startSubscriptionForCardLinkOnlyPartialQuarter(Pharmacy $pharmacy, $cardLinkOrderedAt, array $products = [], $subscriptionStartDate = null): Subscription
    {

        $products = array_merge([BaseStripeProduct::make()], $products);

        if (! $pharmacy->subscribed()) {
            $subscription = $pharmacy->newSubscription('default', collect($products)->map(fn ($p) => $p->getRecurringStripePrice($pharmacy)->stripePriceId)->toArray())
                // pharmacy cant have a payment method. We send invoice with sepa mandate payment
                ->create(null, [], array_merge(
                    [
                        'backdate_start_date' => $subscriptionStartDate ? $subscriptionStartDate->timestamp : $cardLinkOrderedAt->timestamp,
                        'collection_method' => 'send_invoice',
                        'days_until_due' => 14,
                        'billing_cycle_anchor' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                        'discounts' => collect(app(DiscountApplicationHelper::class)->getDiscountsForOrder($pharmacy, $products))->map(fn (StripeDiscount $discount) => ['coupon' => $discount->getSettings()->discount_id])->toArray(),
                        'payment_settings' => [
                            'payment_method_types' => ['customer_balance'],
                        ],
                        //                        'add_invoice_items' => [],
                    ],
                ));

            $invoice = Cashier::stripe()->invoices->all(['limit' => 1, 'subscription' => $subscription->stripe_id]);

            Cashier::stripe()->invoices->addLines(
                $invoice->first()->id,
                [
                    'lines' => [
                        [
                            'price' => CardLinkStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                            'quantity' => ceil($cardLinkOrderedAt->diffInMonths(now('Europe/Berlin')->endOfQuarter())) + 1,
                            'period' => [
                                'end' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                                'start' => $cardLinkOrderedAt->timestamp,
                            ],
                        ],
                    ],
                ]
            );

        } else {
            $subscription = $pharmacy->subscription();
        }

        if ($subscription != null && $subscription->active() && ! $subscription->hasProduct(app(CardLinkStripeProduct::class)->getStripeProductId())) {
            $subscription->noProrate()->addPrice(CardLinkStripeProduct::make()->getRecurringStripePrice($pharmacy)->stripePriceId, 1, [
                'discounts' => collect(app(DiscountApplicationHelper::class)->getDiscountsForOrder($pharmacy, [CardLinkStripeProduct::make()]))->map(fn (StripeDiscount $discount) => ['coupon' => $discount->getSettings()->discount_id])->toArray(),
            ]);
        }

        return $subscription;
    }

    private function startSubscriptionForIaOnlyPartialQuarter(Pharmacy $pharmacy, $iaOrderedAt)
    {
        $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), IAStripeProduct::make()], $iaOrderedAt);
    }

    private function cardLinkOrderedBeforeIa(Pharmacy $pharmacy, Carbon $cardLinkOrderedAt, Carbon $iAOrderedAt)
    {
        if ($cardLinkOrderedAt->lte(now('Europe/Berlin')->startOfYear())) {
            $subscription = $this->startSubscriptionForCardLinkOnlyFullQuarter($pharmacy);
        } else {
            $subscription = $this->startSubscriptionForCardLinkOnlyPartialQuarter($pharmacy, $cardLinkOrderedAt);
        }

        if (! $subscription->hasProduct(app(IAStripeProduct::class)->getStripeProductId())) {
            $subscription->prorate()->addPrice(IAStripeProduct::make()->getRecurringStripePrice($pharmacy)->stripePriceId, 1, [
                'proration_date' => $iAOrderedAt->timestamp,
            ]);
        }
    }

    private function cardLinkOrderedAfterIa(Pharmacy $pharmacy, Carbon $cardLinkOrderedAt, Carbon $iAOrderedAt)
    {
        if ($iAOrderedAt->lte(now('Europe/Berlin')->startOfYear()) && $cardLinkOrderedAt->lte(now('Europe/Berlin')->startOfYear()->endOfMonth())) {
            $subscription = $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), CardLinkStripeProduct::make(), IAStripeProduct::make()]);

            return;
        }

        if ($iAOrderedAt->gt(now('Europe/Berlin')->startOfYear())) {
            $this->startSubscriptionForCardLinkOnlyPartialQuarter($pharmacy, $cardLinkOrderedAt, [BaseStripeProduct::make(), IAStripeProduct::make()], $iAOrderedAt);

            return;
        }

        $this->startSubscriptionFromDate($pharmacy, [BaseStripeProduct::make(), CardLinkStripeProduct::make(), IAStripeProduct::make()], $iAOrderedAt, [
            [
                'class' => IAStripeProduct::make(),
                'start' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'end' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'amount' => 3,
            ],
            [
                'class' => BaseStripeProduct::make(),
                'start' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'end' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'amount' => 3,
            ],
            [
                'class' => CardLinkStripeProduct::make(),
                'start' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'end' => now('Europe/Berlin')->addQuarter()->startOfQuarter()->timestamp,
                'amount' => ceil($cardLinkOrderedAt->diffInMonths(now('Europe/Berlin')->endOfQuarter())) + 1,
            ],
        ]);

        //        $subscription->prorate()->addPrice(IAStripeProduct::make()->getRecurringStripePrice($pharmacy)->stripePriceId, 1, [
        //            'proration_date' => $iAOrderedAt->timestamp,
        //        ]);
    }

    private function getOneTimeAddons($pharmacy)
    {
        $addOns = [
            'M' => [
                10 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M (150 weitere Transaktionen) (Oktober)',
                    'price' => CardLinkPacketMStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
                11 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M (150 weitere Transaktionen) (November)',
                    'price' => CardLinkPacketMStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
                12 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M (150 weitere Transaktionen) (Dezember)',
                    'price' => CardLinkPacketMStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
            ],
            'M+' => [
                10 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M+ (250 weitere Transaktionen) (Oktober)',
                    'price' => CardLinkPacketMPlusStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
                11 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M+ (250 weitere Transaktionen) (November)',
                    'price' => CardLinkPacketMPlusStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
                12 => [
                    'quantity' => 0,
                    'description' => 'CardLink Paket M+ (250 weitere Transaktionen) (Dezember)',
                    'price' => CardLinkPacketMPlusStripeProduct::make()->getOneTimeStripePrice($pharmacy)->stripePriceId,
                ],
            ],
        ];

        foreach (cardLinkAddOns() as $month => $cardLinkAddOn) {
            if (in_array($pharmacy->uuid, $cardLinkAddOn['M'])) {
                $addOns['M'][$month]['quantity']++;
            }
            if (isset($cardLinkAddOn['M+'][$pharmacy->uuid])) {
                $addOns['M+'][$month]['quantity'] += $cardLinkAddOn['M+'][$pharmacy->uuid];
            }
        }

        $result = [];
        foreach ($addOns as $addOn) {
            $result = array_merge($result, array_values(array_filter($addOn, fn ($addOn) => $addOn['quantity'] > 0)));
        }

        return array_values($result);
    }
}
