<?php

namespace App\Jobs;

use App\Enums\PharmaceuticalService\PharmaceuticalServicePrice;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use App\PharmaceuticalServiceInvoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GeneratePharmaceuticalServiceInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private PharmaceuticalServiceInvoice $pharmaceuticalServiceInvoice;

    private bool $notify;

    public function __construct(PharmaceuticalServiceInvoice $pharmaceuticalServiceInvoice, bool $notify = true)
    {
        $this->pharmaceuticalServiceInvoice = $pharmaceuticalServiceInvoice;
        $this->notify = $notify;
    }

    public function handle()
    {
        $pdf = app('dompdf.wrapper');
        $path = Storage::disk('local-temp')->path('/pdf').Str::uuid().'.pdf';

        $query = $this->getQuery();

        $highBloodPressureCount = $this->calcHighBloodPressureCount($query);
        $highBloodPressureSum = $highBloodPressureCount * PharmaceuticalServicePrice::MEASURE_BLOOD_PRESSURE->value;
        $inhalationCount = $this->calcInhalationCount($query);
        $inhalationSum = $inhalationCount * PharmaceuticalServicePrice::INHALATION_TECHNIQUE->value;
        $sum = $inhalationSum + $highBloodPressureSum;

        // Update total price on invoice
        $this->pharmaceuticalServiceInvoice->update([
            'price' => $sum,
        ]);

        // Generate PDF
        $pdf->loadHTML(view('pharmacy.pharmaceuticalServices.invoice.pdf.export', [
            'pharmacy' => $this->pharmaceuticalServiceInvoice->pharmacy,
            'query' => $query,
            'start_date' => $this->pharmaceuticalServiceInvoice->start_date,
            'end_date' => $this->pharmaceuticalServiceInvoice->end_date,
            'highBloodPressureCount' => $highBloodPressureCount,
            'highBloodPressureSum' => $highBloodPressureSum,
            'inhalationCount' => $inhalationCount,
            'inhalationSum' => $inhalationSum,
            'sum' => $sum,
        ]))->save($path);

        $this->pharmaceuticalServiceInvoice->addMedia($path)
            ->toMediaCollection('invoice');

        $this->pharmaceuticalServiceInvoice->finished($this->notify);
    }

    public function calcPriceSummary($query)
    {
        return $query->count() * 10 * 100;
    }

    public function calcHighBloodPressureCount($query)
    {
        $q = clone $query;

        return $q->where('type', PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE)->count();
    }

    public function calcInhalationCount($query)
    {
        $q = clone $query;

        return $q->where('type', PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE)->count();
    }

    /**
     * @return Builder
     */
    public function getQuery()
    {
        return $this
            ->pharmaceuticalServiceInvoice
            ->pharmacy
            ->pharmaceuticalServices()
            ->where('status', PharmaceuticalServiceStatus::FINISHED)
            ->whereNull('reasons_to_abort')
            ->where('date', '>=', $this->pharmaceuticalServiceInvoice->start_date)
            ->where('date', '<=', $this->pharmaceuticalServiceInvoice->end_date);
    }
}
