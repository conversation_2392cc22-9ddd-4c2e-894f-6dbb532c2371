<?php

namespace App\Jobs;

use App\Actions\DocSpaces\GetRiseSDRDocSpaceStatsAction;
use App\DocSpace;
use App\Exceptions\DocSpace\GetDocSpaceStatsRequestException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RefreshDocSpaceUsageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public DocSpace $docSpace;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(DocSpace $docSpace)
    {
        $this->docSpace = $docSpace;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $docSpace = $this->docSpace;

            $action = app()->make(GetRiseSDRDocSpaceStatsAction::class, ['docSpace' => $docSpace])
                ->execute();

            $docSpace->update([
                'current_usage' => $action->getLastUsageInGB() * 1024,
            ]);
        } catch (GetDocSpaceStatsRequestException $e) {
            report($e);
        }
    }
}
