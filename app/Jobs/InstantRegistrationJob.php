<?php

namespace App\Jobs;

use App\Actions\Auth\RegistrationAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstantRegistrationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $data;

    private bool $skipValidation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $data, bool $skipValidation = false)
    {
        $this->data = $data;
        $this->skipValidation = $skipValidation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        (new RegistrationAction)->createInstantRegistration($this->data, $this->skipValidation);
    }
}
