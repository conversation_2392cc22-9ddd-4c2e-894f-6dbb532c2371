<?php

namespace App\Nova\Actions;

use App\CardLinkOrder;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class ChangeCardLinkOrderStatus extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Status ändern';

    /**
     * @param  Collection<CardLinkOrder>  $models
     */
    public function handle(ActionFields $fields, Collection $models): void
    {
        foreach ($models as $model) {
            $model->update([
                'status' => $fields->status, // @phpstan-ignore-line: Access to an undefined property
            ]);
        }
    }

    /**
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Status', 'status')
                ->options(CardLinkOrderStatusEnum::labels())
                ->displayUsingLabels()
                ->required(),
        ];
    }
}
