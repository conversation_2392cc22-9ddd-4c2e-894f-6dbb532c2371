<?php

namespace App\Nova\Actions;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

class DownloadPharmacies extends DownloadExcel implements ShouldAutoSize, WithEvents
{
    public $name = 'Excel Download';

    public function headings(): array
    {
        return [
            'Name der Apotheke',
            'Verifizierungsstatus',
            'Aponummer der N-Ident Registrierung',
            'Adresse',
            'Institutionskennzeichen',
            'Apotheken-id',
            'Telefonnummer',
            'Fax',
            'E-Mail-Adresse',
            'Website',
            'Botendienst',
            'Botendienst Radius (in km)',
            'Parkmöglichkeit an der Apotheke',
            'Aktiv',
            'Hauptapotheke',
            'Logo',
            'Bild',
            'Erstellt am',
            'Warenwirtschaftssystem',
            'Abrechnungszentrum',
            'Besondere Lage',
            'Gesprochene Sprachen',
            'Schwerpunk<PERSON>',
            'Öffungszeiten',
        ];
    }

    public function map($pharmacy): array
    {
        return [
            $pharmacy->name,
            trans('entities.pharmacyStatus.'.$pharmacy->verification_status),
            $pharmacy->n_id,
            $pharmacy->address,
            $pharmacy->institute_id,
            $pharmacy->pharmacy_id,
            $pharmacy->phone,
            $pharmacy->fax,
            $pharmacy->email,
            $pharmacy->website,
            $pharmacy->courier_service ? 'Ja' : 'Nein',
            $pharmacy->courier_service_radius,
            $pharmacy->has_near_parking_space ? 'Ja' : 'Nein',
            $pharmacy->active ? 'Ja' : 'Nein',
            $pharmacy->is_main ? 'Ja' : 'Nein',
            $pharmacy->logo() ? 'Ja' : 'Nein',
            $pharmacy->images()->first() ? 'Ja' : 'Nein',
            $pharmacy->created_at,
            $pharmacy->goodsManagementSystem ? $pharmacy->goodsManagementSystem->system_name : 'Keine Angabe',
            $pharmacy->accountingCenter ? $pharmacy->accountingCenter->company : 'Keine Angabe',
            $pharmacy->pharmacyType ? trans('entities.pharmacyTypes.'.$pharmacy->pharmacyType->name) : 'Keine Angabe',
            $pharmacy->languages->map(function ($lan) {
                return trans('languages.'.$lan->code);
            })->implode(', '),
            $pharmacy->focusAreas->map(function ($area) {
                return trans('entities.focusAreas.'.$area->name);
            })->implode(', '),
            $pharmacy->businessHours->map(function ($hour) {
                return trans('entities.weekdays.'.(string) $hour->day_of_week).': '.$hour->opens.' - '.$hour->closes;
            })->implode(', '),
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event
                    ->sheet
                    ->getDelegate()
                    ->getStyle(
                        'A1:Y'.$event->sheet->getDelegate()->getHighestRow()
                    )
                    ->applyFromArray([
                        'alignment' => [
                            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT,
                        ],
                    ]);
            },
        ];
    }
}
