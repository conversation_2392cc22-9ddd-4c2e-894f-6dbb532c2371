<?php

namespace App\Nova\Actions;

use App\Exceptions\LocationNotFoundException;
use App\Nova\ApprovableChange;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ApprovePharmacyAttributeChanges extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Attribute übernehmen';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models)
    {
        $exceptions = [];
        /** @var ApprovableChange $model */
        foreach ($models as $model) {
            try {
                $model->pharmacy->approveAttributeChange($model->attribute);
            } catch (LocationNotFoundException $e) {
                $exceptions[] = $model->pharmacy->name.': '.$model->attribute;
            }
        }

        if (count($exceptions) > 0) {
            return Action::danger('Folgende Attribute konnten nicht gespeichert werden: '.implode(', ', $exceptions));
        }
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
