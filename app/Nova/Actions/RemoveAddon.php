<?php

namespace App\Nova\Actions;

use App\StripeMigrationOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON><PERSON>\Nova\Actions\DestructiveAction;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class RemoveAddon extends DestructiveAction
{
    use InteractsWithQueue, Queueable;

    public function name(): string
    {
        return 'Addon stornieren';
    }

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $model = $models->sole();
        assert($model instanceof StripeMigrationOrder);
        $addons = $model->addons;
        unset($addons[$fields->addon_to_remove]); /* @phpstan-ignore-line */
        $model->addons = $addons;
        $model->save();

        return Action::message('Addon erfolgreich storniert.');
    }

    /** @return array<Field> */
    public function fields(NovaRequest $request): array
    {
        return [
            Heading::make(
                'Hinweis: Einige Addons wie z.B. Cardlink, KIM & iA können hierüber nicht storniert werden. Diese werden dann zwar aus der Liste entfernt, das hat aber keinen Effekt.'
            )->asHtml(),
            Select::make('Zu stornierendes Addon', 'addon_to_remove')
                ->options(function () use ($request) {
                    return $request->findModelQuery()->sole()->addons; /* @phpstan-ignore-line */
                })
                ->rules('required'),
        ];
    }
}
