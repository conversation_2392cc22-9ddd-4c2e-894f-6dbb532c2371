<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class DispprovePharmacyAttributeChanges extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Attribute ablehnen';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->delete();
        }
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
