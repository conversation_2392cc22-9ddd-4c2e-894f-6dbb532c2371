<?php

namespace App\Nova\Metrics;

use App\Domains\Subscription\Application\FeatureAccess\DigitalRepresentationFeatureAccess;
use App\Pharmacy;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class DoesCovidVaccinationPerAssociation extends Partition
{
    private $association;

    public function __construct($association = null)
    {
        parent::__construct();
        $this->association = $association;
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Bietet COVID-19-Impfung an';
    }

    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $query = Pharmacy::query();

        if ($this->association) {
            $query = $query
                ->withFeatureAccess(DigitalRepresentationFeatureAccess::class);

            if ($this->association === 'all_association_member') {
                $query = $query->whereNotNull('association_id');
            } elseif ($this->association === 'not_in_association') {
                $query = $query->whereNull('association_id');
            } elseif ($this->association !== 'all') {
                $query = $query->where('association_id', $this->association);
            }
        }

        $query2 = clone $query;

        return $this->result([
            'Ja' => $query->where('does_covid_vaccination', true)->count(),
            'Nein' => $query2->where('does_covid_vaccination', false)->count(),
        ])->colors([
            'Ja' => 'green',
            'Nein' => 'red',
        ]);
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            10 => '10 Tage',
            30 => '30 Tage',
            60 => '60 Tage',
            'ALL' => 'Seit Beginn',
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'does-covid-vaccination-'.($this->association ?: '');
    }
}
