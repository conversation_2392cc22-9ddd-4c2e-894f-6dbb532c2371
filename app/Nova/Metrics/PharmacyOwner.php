<?php

namespace App\Nova\Metrics;

use App\Enums\PharmacyRoleEnum;
use App\User;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;

class PharmacyOwner extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $ownerWithUsedInvite = User::query()
            ->whereHas('brochureCode')
            ->count();

        $ownerWithoutUsedInvite = User::query()
            ->whereHas('pharmacies', function ($query) {
                return $query->where('role_name', PharmacyRoleEnum::OWNER);
            })
            ->whereDoesntHave('brochureCode')
            ->count();

        return $this->result([
            'Code eingelöst' => $ownerWithUsedInvite,
            'Code nicht eingelöst' => $ownerWithoutUsedInvite,
        ])->colors([
            'Code eingelöst' => 'green',
            'Code nicht eingelöst' => 'red',
        ]);
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Inhaber';
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'pharmacy-owner';
    }
}
