<?php

namespace App\Nova\Metrics;

use App\Integration;
use App\Integrations\IntegrationTypeEnum;
use Flowframe\Trend\TrendValue;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Trend;
use <PERSON>vel\Nova\Metrics\TrendResult;

class IaIntegrationsOverTime extends Trend
{
    public $name = 'Neue IA Integrationen';

    /**
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $trend = \Flowframe\Trend\Trend::query(
            Integration::query()
                ->where('integration_type', IntegrationTypeEnum::IhreApotheken)
                ->withTrashed()
        );

        $trend = match ($request->range) {
            '30D' => $trend->between(now()->subDays(30), now())->perDay(),
            '90D' => $trend->between(now()->subDays(90), now())->perDay(),
            '12M' => $trend->between(now()->subYear(), now())->perMonth(),
            '3Y' => $trend->between(now()->subYears(3), now())->perYear(),
            default => $trend->between(now()->subYear(), now())->perMonth(),
        };

        $trend = $trend->dateColumn('created_at')->count();

        $result = collect($trend)->mapWithKeys(function (TrendValue $value) { // @phpstan-ignore-line: This is a known issue with PHPStan
            return [$value->date => $value->aggregate];
        })->toArray();

        return (new TrendResult)->trend($result);
    }

    /**
     * @return array<string, string>
     */
    public function ranges(): array
    {
        return [
            '30D' => '30 Tage',
            '90D' => '90 Tage',
            '12M' => '12 Monate',
            '3Y' => '3 Jahre',
        ];
    }

    /**
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * @return string
     */
    public function uriKey()
    {
        return 'ia-integrations-over-time';
    }
}
