<?php

namespace App\Nova;

use Lara<PERSON>\Nova\Fields\Field;
use Laravel\Nova\Fields\ID;
use Lara<PERSON>\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class BillingAddress extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\BillingAddress>
     */
    public static $model = \App\BillingAddress::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'Rechnungsadressen';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('E-Mail-Adresse', 'email'),
            Text::make('Firmenname', 'company'),
            Text::make('Vorname', 'first_name'),
            Text::make('Nachname', 'last_name'),
            Text::make('Straße', 'street'),
            Text::make('Hausnummer', 'house_number'),
            Text::make('Adresszusatz', 'optional_address_line'),
            Text::make('Postleitzahl', 'postal_code'),
            Text::make('Telefonnummer', 'phone'),

        ];
    }
}
