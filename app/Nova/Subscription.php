<?php

namespace App\Nova;

use <PERSON><PERSON>\Cashier\Subscription as CashierSubscription;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\Field;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\HasOne;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Subscription extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<CashierSubscription>
     */
    public static $model = CashierSubscription::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'id', 'stripe_id',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label(): string
    {
        return 'Abonnements (Stripe)';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('stripe_status'),
            Date::make('ends_at'),
            Date::make('created_at'),
            Date::make('updated_at'),
            HasMany::make('Im Abo enthalten', 'items', SubscriptionItem::class),
            Text::make('Stripe Link', function () {
                return '<a target="_blank" class="font-bold" href="https://dashboard.stripe.com/subscriptions/'.$this->model()?->stripe_id.'">Abo in Stripe öffnen</a>';
            })->asHtml(),
            HasOne::make('Apotheke', 'owner', Pharmacy::class),
        ];
    }
}
