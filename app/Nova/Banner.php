<?php

namespace App\Nova;

use App\Enums\BannerPosition;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\Field;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Banner extends Resource
{
    /** @var class-string */
    public static $model = \App\Banner::class;

    public static $title = 'title';

    /** @var array<string> */
    public static $search = [
        'id',
        'title',
        'content',
    ];

    /** @var string */
    public static $group = 'News/Blog';

    public static function label(): string
    {
        return 'Banner';
    }

    /** @return array<Field> */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Titel', 'title')->required(),
            Textarea::make('Inhalt', 'content')->required(),
            Select::make('Position', 'position')
                ->options(BannerPosition::dropdown())
                ->displayUsing(fn (string $position) => BannerPosition::tryFrom($position)?->forNova())
                ->required(),

            DateTime::make('Beginn', 'starts_at')->nullable(),
            DateTime::make('Ende', 'ends_at')->nullable(),

            Boolean::make('veröffentlicht', 'active'),

        ];
    }
}
