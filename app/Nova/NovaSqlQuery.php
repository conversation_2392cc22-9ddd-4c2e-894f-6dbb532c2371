<?php

namespace App\Nova;

use App\Nova\Actions\ExecuteNovaSqlQuery;
use App\Rules\NovaSqlQueryRule;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Field;
use <PERSON><PERSON>\Nova\Fields\Heading;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\KeyValue;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class NovaSqlQuery extends Resource
{
    public static string $model = \App\NovaSqlQuery::class;

    public static $title = 'name';

    /** @var array<int, string> */
    public static $search = [
        'id', 'name', 'query',
    ];

    public static function label(): string
    {
        return 'Nova SQL Query';
    }

    /** @return array<int, Field> */
    public function fields(NovaRequest $request): array
    {
        assert(($staffUser = auth()->user()) instanceof \App\Staff);

        return [
            ID::make()->sortable(),

            Text::make('Name', 'name')
                ->rules(['required', 'string'])
                ->sortable(),

            Textarea::make('Beschreibung', 'description')
                ->rules(['required', 'string'])
                ->alwaysShow()
                ->hideFromIndex(),

            BelongsTo::make('Staff')
                ->default($staffUser->id)
                ->displayUsing(fn (Staff $staff) => $staff->name)
                ->readonly()
                ->sortable(),

            Heading::make('Es muss eine <b>Prepared Statement</b>-gültige Abfrage sein! Falls nötig, für den WHERE-Teil bitte die Werte als <b>"?"</b> angeben.', 'h2')
                ->asHtml()
                ->onlyOnForms(),

            Textarea::make('Abfrage', 'query')
                ->rules(['required', 'string', new NovaSqlQueryRule])
                ->onlyOnForms(),

            Heading::make('Die zu betrachtende Spalte, zu ersetzende Werte aus dem WHERE-Teil ("?"-Parameter)', 'h2')
                ->asHtml()
                ->onlyOnForms(),

            KeyValue::make('Parameter (WHERE)', 'params')
                ->keyLabel('Column')
                ->valueLabel('Datentyp (SQL)')
                ->rules(['json', 'nullable'])
                ->help('Erlaubte Datentypen: boolean, date, dateTime, float, integer, string')
                ->onlyOnForms(),
        ];
    }

    /** @return array<Action> */
    public function actions(NovaRequest $request): array
    {
        return [
            (new ExecuteNovaSqlQuery)->onlyOnDetail(),
        ];
    }
}
