<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class KimAddressReportStatus extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('report_status', $value);
    }

    /**
     * @return array<string, string>
     */
    public function options(Request $request)
    {
        return \App\Enums\KimAddressReportStatus::forFilter();
    }
}
