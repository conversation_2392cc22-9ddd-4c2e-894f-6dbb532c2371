<?php

namespace App\Nova;

use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class AssociationFrameworkContractHistory extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Domains\Association\Models\AssociationFrameworkContractHistory>
     */
    public static $model = \App\Domains\Association\Models\AssociationFrameworkContractHistory::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'association.name';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'id',
        'association.name',
    ];

    public static function label(): string
    {
        return 'Gültigkeitshistorie Rahmenverträge';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Verband', 'association', Association::class),

            Text::make('Rahmenvertrag', 'contract')
                ->displayUsing(fn (?string $contract) => $contract ? AssociationFrameworkContractEnum::from($contract)->label() : AssociationFrameworkContractEnum::NoAssociationFrameworkContract->label()), // @phpstan-ignore-line

            Date::make('Beginn', 'starts_at'),

            Date::make('Ende', 'ends_at'),
        ];
    }
}
