<?php

namespace App\Nova;

use App\Enums\PharmaceuticalsTypeEnum;
use App\Enums\Vaccinate\AgeGroupEnum;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Pharmaceutical extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Pharmaceutical::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
    ];

    public static $group = 'Standings';

    public static function label()
    {
        return 'Arzneimittel';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required'),

            Text::make('Anzeigename', 'display_name')
                ->sortable()
                ->rules('required'),

            Number::make('PZN', 'pzn')
                ->creationRules(['unique:pharmaceuticals,pzn', 'nullable']),

            Select::make('Typ', 'type')
                ->options(PharmaceuticalsTypeEnum::getForNova())
                ->displayUsingLabels()
                ->sortable(),

            Select::make('min. Alter', 'min_agegroup')
                ->options(AgeGroupEnum::getForNova())
                ->displayUsingLabels()
                ->sortable(),

            Select::make('max. Alter', 'max_agegroup')
                ->options(AgeGroupEnum::getForNova())
                ->displayUsingLabels()
                ->sortable(),

            Boolean::make('Aktiv', 'active')->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
