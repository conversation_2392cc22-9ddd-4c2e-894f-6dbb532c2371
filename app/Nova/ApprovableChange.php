<?php

namespace App\Nova;

use App\Nova\Actions\ApprovePharmacyAttributeChanges;
use App\Nova\Actions\DispprovePharmacyAttributeChanges;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Image;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class ApprovableChange extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\ApprovableChange::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static $displayInNavigation = false;

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        $fields = [
            ID::make('id'),
            Text::make('attribute'),
        ];

        $fields = array_merge($fields, $this->getChangePreview($this->resource));

        $fields[] = BelongsTo::make('pharmacy');

        return $fields;
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [
            (new ApprovePharmacyAttributeChanges)->canRun(function () {
                return true;
            }),
            (new DispprovePharmacyAttributeChanges)->canRun(function () {
                return true;
            }),
        ];
    }

    private function getChangePreview($resource)
    {
        if ($resource->type == 'image') {
            return [
                $this->getOldImagePreview($resource),

                Image::make('Neuer Wert', 'changeads')->thumbnail(function () use ($resource) {
                    return url(route('nova.approvals.image.show', ['approvableChange' => $resource]));
                })->textAlign('left'),
            ];
        }

        if ($resource->type == 'address') {
            return [
                Text::make('Alter Wert')->displayUsing(function () use ($resource) {
                    return $resource->pharmacy->address;
                }),

                Text::make('Neuer Wert', 'change')->displayUsing(function ($val) {
                    return (isset($val['optional_address_line']) ? $val['optional_address_line'].', ' : null).$val['street'].' '.$val['house_number'].', '.$val['postcode'].' '.$val['city'];
                }),
            ];
        }

        return [
            Text::make('Alter Wert')->displayUsing(function () use ($resource) {
                return $resource->pharmacy->{$resource->attribute};
            }),

            Text::make('Neuer Wert', 'change')->displayUsing(function ($val) {
                return $val['newValue'];
            }),
        ];
    }

    private function getOldImagePreview($resource)
    {
        if ($resource->attribute == 'logo' && $logo = $resource->pharmacy->logo()) {
            return Image::make('Alter Wert', 'adsasdasd')
                ->thumbnail(function ($val) use ($resource) {
                    return url(route('nova.pharmacies.logo', ['pharmacy' => $resource->pharmacy]));
                })->textAlign('left');
        }

        if ($resource->attribute == 'image' && $resource->pharmacy->images()->count() > 0) {
            return Image::make('Alter Wert', 'adsasdasd')
                ->thumbnail(function ($val) use ($resource) {
                    return url(route('nova.pharmacies.image', ['pharmacy' => $resource->pharmacy]));
                })->textAlign('left');
        }

        return Text::make('Alter Wert')
            ->displayUsing(function () {
                return null;
            });
    }
}
