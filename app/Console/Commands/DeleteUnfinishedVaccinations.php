<?php

namespace App\Console\Commands;

use App\Jobs\DeleteUnfinishedVaccinations as JobsDeleteUnfinishedVaccinations;
use Illuminate\Console\Command;

class DeleteUnfinishedVaccinations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vaccination:unfinished-delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lösche unerledigte Impfungen älter als 24 Stunden';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        JobsDeleteUnfinishedVaccinations::dispatch();
    }
}
