<?php

namespace App\Console\Commands;

use App\Pharmacy;
use App\PharmacySetting;
use Illuminate\Console\Command;

class PharmacyCreateSettings extends Command
{
    /**
     * @var string
     */
    protected $signature = 'pharmacies:create-settings';

    /**
     * @var string
     */
    protected $description = 'Create pharmacy settings table entries';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $pharmacies = Pharmacy::all();
        foreach ($pharmacies as $pharmacy) {
            PharmacySetting::firstOrCreate([
                'pharmacy_id' => $pharmacy->id,
            ]);
        }
    }
}
