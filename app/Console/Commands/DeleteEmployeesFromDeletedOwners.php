<?php

namespace App\Console\Commands;

use App\Actions\Users\DeleteIDPUserAction;
use App\User;
use Illuminate\Console\Command;

class DeleteEmployeesFromDeletedOwners extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:delete-employees-from-deleted-owners {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = 0;
        User::query()
            ->onlyTrashed()
            ->whereHas('brochureCode')
            ->eachById(function (User $owner) use (&$count) {
                foreach ($owner->pharmacies as $pharmacy) {
                    $pharmacy->users()->each(function (User $user) use (&$count) {
                        if (! $this->option('dry-run')) {
                            \DB::transaction(function () use ($user) {
                                $user->forceFill([
                                    'deleted_at' => now(),
                                ])->delete();

                                if ($user->is_at_idp) {
                                    app(DeleteIDPUserAction::class)->deleteUser($user->uuid);
                                }
                            });
                        }
                        $this->info('deleted user: '.$user->id);
                        $count += 1;
                    });
                }
            });

        $count2 = 0;

        // Apothekennutzer (keine Owner oder Subowner) ohne Apotheke
        User::query()
            ->whereHas('pharmacyProfile')
            ->whereDoesntHave('brochureCode')
            ->whereDoesntHave('pharmacies')
            ->whereDoesntHave('pharmacyProfile.companyHeadUser')
            ->eachById(function (User $user) use (&$count2) {
                if (! $this->option('dry-run')) {
                    \DB::transaction(function () use ($user) {
                        $user->forceFill([
                            'deleted_at' => now(),
                        ])->delete();

                        if ($user->is_at_idp) {
                            app(DeleteIDPUserAction::class)->deleteUser($user->uuid);
                        }
                    });
                }
                $this->info('deleted user: '.$user->id);
                $count2 += 1;
            });

        $this->info('deleted users from owner '.$count);
        $this->info('deleted users without pharmacy: '.$count2);
    }
}
