<?php

namespace App\Console\Commands;

use App\Association;
use App\AssociationSetting;
use Illuminate\Console\Command;

class AssociationsCreateSettings extends Command
{
    /**
     * @var string
     */
    protected $signature = 'associations:create-settings';

    /**
     * @var string
     */
    protected $description = 'Create association settings table entries';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $associations = Association::all();
        foreach ($associations as $association) {
            AssociationSetting::firstOrCreate([
                'association_id' => $association->id,
            ]);
        }
    }
}
