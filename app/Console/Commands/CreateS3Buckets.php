<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreateS3Buckets extends Command
{
    /**
     * @var string
     */
    protected $signature = 's3:create-buckets {--buckets=}';

    public function handle()
    {
        $disks = $this->option('buckets') ? explode(',', $this->option('buckets')) : config('filesystems.own-application-s3-disks');

        foreach ($disks as $disk) {
            $client = new \Aws\S3\S3Client([
                'endpoint' => config('filesystems.disks.'.$disk.'.endpoint'),
                'version' => 'latest',
                'use_path_style_endpoint' => true,
                'region' => config('filesystems.disks.'.$disk.'.region'),
                'credentials' => [
                    'key' => config('filesystems.disks.'.$disk.'.key'),
                    'secret' => config('filesystems.disks.'.$disk.'.secret'),
                ],
            ]);

            if (! $client->doesBucketExist(config('filesystems.disks.'.$disk.'.bucket'))) {
                $client->createBucket([
                    'Bucket' => config('filesystems.disks.'.$disk.'.bucket'),
                ]);
            }
        }
    }
}
