<?php

namespace App\Console\Commands;

use App\Enums\AkuqinetKimStatus;
use App\Enums\KimAddressStatus;
use App\Http\Integrations\Akquinet\AkquinetConnector;
use App\Http\Integrations\Akquinet\Requests\GetSubCodesByQueryRequest;
use App\KimAddress;
use Illuminate\Console\Command;

class FixFalseDeactivatedKimAdresses extends Command
{
    protected $signature = 'kim:fix-false-activated';

    protected $description = 'Set status of all false deactivated kim addresses to activated again';

    public function handle(): void
    {
        //next we have to check all other kim addresses, by getting the status from AKQ
        KimAddress::where('log->deactivated_retrospectively_at', 'LIKE', '%2024-10-13T23%')
            ->eachById(function (KimAddress $kim) {
                //1. Check if has registration code
                if ($kim->additional) {
                    $connector = new AkquinetConnector;

                    $request = new GetSubCodesByQueryRequest($kim->additional['voucherCode']);

                    $response = $connector->send($request);

                    if ($response->ok() === false) {
                        throw new \RuntimeException('Could not get status from AKQ');
                    }

                    /** @phpstan-ignore-next-line */
                    $result = collect($response->json('registrationCodes'));

                    if ($result->count() > 1) {
                        $this->error('More than one registration code found for: '.$kim->email);

                        //Skip this one
                        return true;
                    }

                    /** @phpstan-ignore-next-line */
                    $status = match ($result->first()['state']) {
                        AkuqinetKimStatus::ACTIVATED->value => KimAddressStatus::ACTIVATED->value,
                        AkuqinetKimStatus::ORDERED->value => KimAddressStatus::ORDERED->value,
                        default => KimAddressStatus::CANCELED->value,
                    };

                    if ($status === KimAddressStatus::CANCELED->value) {
                        return true;
                    }

                    $kim->update([
                        'status' => $status,
                        'log->deactivated_retrospectively_at' => null,
                        'deactivated_at' => null,
                    ]);
                }
            });
    }
}
