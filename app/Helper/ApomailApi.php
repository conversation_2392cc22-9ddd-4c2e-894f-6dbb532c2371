<?php

namespace App\Helper;

use App\Exceptions\Apomail\NotAuthenticatedInApomailException;
use App\Settings\ApoMailSettings;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use RuntimeException;
use Throwable;

class ApomailApi
{
    private string $url;

    private string $certificate;

    public function __construct()
    {
        $this->url = $this->getUrl();
        $this->certificate = $this->getCertFile();
    }

    public function createUser(string $email, string $name, string $password, bool $retry = true): bool
    {
        if (! $this->isApomailActive()) {
            return true;
        }

        try {
            $response = $this->getAuthenticatedBaseRequest(! $retry)
                ->post($this->url.'/api/user/'.$email, [
                    'name' => $name,
                    'password_hash' => $this->getEncryptedPassword($password),
                    'language' => 'de_DE',
                ])->throw();

            if ($this->getStatusFromJsonResponse($response)) {
                return true;
            }
        } catch (NotAuthenticatedInApomailException $exception) {
            if ($retry) {
                return $this->createUser($email, $name, $password, false);
            }

            report($exception);

            return false;
        } catch (Throwable $exception) {
            report($exception);
        }

        return false;
    }

    public function updateUser(
        string $email,
        string $name,
        ?string $givenName = null,
        ?string $surname = null,
        ?string $password = null,
        bool $retry = true
    ): bool {
        if (! $this->isApomailActive()) {
            return true;
        }

        try {
            $response = $this->getAuthenticatedBaseRequest(! $retry)
                ->put($this->url.'/api/user/'.$email, [
                    'name' => $name,
                    'gn' => $givenName,
                    'sn' => $surname,
                    'password_hash' => empty($password) ? null : $this->getEncryptedPassword($password),
                ])->throw();

            if ($this->getStatusFromJsonResponse($response)) {
                return true;
            }
        } catch (NotAuthenticatedInApomailException $exception) {
            if ($retry) {
                return $this->updateUser($email, $name, $givenName, $surname, $password, false);
            }

            report($exception);

            return false;
        } catch (Throwable $exception) {
            report($exception);
        }

        return false;
    }

    public function loginAsAdmin(bool $newCookie = false)
    {
        try {
            $cacheKey = $this->getCacheKey();

            if ($newCookie || ! Cache::has($cacheKey)) {
                [$username, $password] = $this->getAdminCredentials();

                $response = $this->getBaseRequest()
                    ->post($this->url.'/api/login', [
                        'username' => $username,
                        'password' => $password,
                    ])->throw();

                if (! $response->ok()) {
                    throw new RuntimeException('Request failed!');
                }
                Cache::put($cacheKey, $response->cookies());
            }

            return Cache::get($cacheKey);
        } catch (Throwable $exception) {
            report($exception);
        }

        return false;
    }

    public function getBaseRequest(array $additionalOptions = []): PendingRequest
    {
        return Http::withOptions([...$additionalOptions, 'verify' => $this->certificate])
            ->asForm()
            ->timeout(5);
    }

    public function getAuthenticatedBaseRequest(bool $newCookie = false): PendingRequest
    {
        $cookieFromResponse = $this->loginAsAdmin($newCookie);

        return $this->getBaseRequest(['cookies' => $cookieFromResponse]);
    }

    public function getCacheKey(): string
    {
        return config('app.server_node_name').'_apomail-auth';
    }

    /**
     * @throws RuntimeException
     */
    public function getUrl(): string
    {
        if (! $this->isApomailActive()) {
            return '';
        }

        if (empty($url = config('services.apomail.url'))) {
            throw new RuntimeException('Missing config value for services.apomail.url');
        }

        return $url;
    }

    private function isApomailActive(): bool
    {
        return (bool) config('services.apomail.active');
    }

    /**
     * @throws RuntimeException
     */
    public function getCertFile(): string
    {

        if (! $this->isApomailActive()) {
            return '';
        }

        return ApoMailSettings::certificate();
    }

    /**
     * @throws RuntimeException
     */
    public function getCookieExpiringDate(): float|int
    {
        $expiringDate = config(
            'services.apomail.admin_login_expiring_minutes',
            5
        );

        if (empty($expiringDate)) {
            throw new RuntimeException('Error in config value for services.apomail.admin_login_expiring_date');
        }

        return Carbon::now()->addMinutes($expiringDate)->diffInSeconds();
    }

    /**
     * @throws RuntimeException
     */
    private function getAdminCredentials(): array
    {
        if (empty($username = config('services.apomail.admin.username'))) {
            throw new RuntimeException('Missing config value for services.apomail.admin.username');
        }
        if (empty($password = config('services.apomail.admin.password'))) {
            throw new RuntimeException('Missing config value for services.apomail.admin.password');
        }

        return [$username, $password];
    }

    /**
     * @throws RuntimeException
     */
    public function getEncryptedPassword($password): string
    {
        $salt = openssl_random_pseudo_bytes(4, $strongResult);
        $count = 0;

        while ($strongResult === false || $salt === false) {
            openssl_random_pseudo_bytes(4, $strongResult);

            if ($count >= 3) {
                throw new RuntimeException('IV generation failed');
            }
            $count++;
        }

        $encodedPassword = base64_encode(sha1($password.$salt, true).$salt);

        return '{SSHA}'.$encodedPassword;
    }

    /**
     * @throws RuntimeException|NotAuthenticatedInApomailException
     */
    public function getStatusFromJsonResponse(Response $response): bool
    {
        if (! $response->ok()) {
            $this->throwExceptionWithMessageAndResponse('Request failed', $response);
        }

        try {
            $array = json_decode($response->body(), true, 512, JSON_THROW_ON_ERROR);
        } catch (Exception $exception) {
            $this->throwExceptionWithMessageAndResponse('Json could not be decoded from response'
                                                        .' - exception message: '.$exception->getMessage(), $response);

        }

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->throwExceptionWithMessageAndResponse(json_last_error_msg(), $response);
        }

        if (! Arr::has($array, '_success')) {
            $this->throwExceptionWithMessageAndResponse('Response error: No key "_success" in Response!', $response);
        }

        if ((bool) Arr::get($array, '_success') === false && ! Arr::has($array, '_msg')) {
            $this->throwExceptionWithMessageAndResponse('Response error: No key "_msg" in Response!', $response);
        }

        if (Arr::get($array, '_msg') === 'LOGIN_REQUIRED') {
            throw new NotAuthenticatedInApomailException(Arr::get($array, '_msg'));
        }

        if ((bool) Arr::get($array, '_success') === false && Arr::has($array, '_msg')) {
            $this->throwExceptionWithMessageAndResponse(Arr::get($array, '_msg'), $response);
        }

        return filter_var($array['_success'], FILTER_VALIDATE_BOOLEAN);
    }

    private function throwExceptionWithMessageAndResponse(string $message, Response $response): void
    {
        throw new RuntimeException(
            $message.' - response body: '.$response->body()
                                   .' - response code: '.$response->status()
                                   .' - response headers: '.(json_encode($response->headers()) ?: 'response headers could not be json encoded')
        );
    }
}
