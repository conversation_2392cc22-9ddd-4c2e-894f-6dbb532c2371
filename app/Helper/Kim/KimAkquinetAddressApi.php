<?php

namespace App\Helper\Kim;

use App\Enums\KimAddressStatus;
use App\Enums\KimVendorEnum;
use App\Exceptions\Kim\ApiException;
use App\KimAddress;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Sentry\Breadcrumb;

use function Sentry\addBreadcrumb;

class KimAkquinetAddressApi
{
    public const KIM_AKQUINET_ADMIN_CACHE_KEY = 'kim_aquinet_admin_access_token';

    public function createSubCustomer(KimAddress $kimAddress): Response
    {
        $owner = $kimAddress->pharmacy->owner();

        return $this->makeRequest(method: 'post', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/customers/subCustomer', data: [
            'role' => 'Inaktiver Subkunde',
            'title' => $owner->title,
            'name' => $owner->last_name,
            'firstName' => $owner->first_name,
            'email' => $kimAddress->pharmacy->uuid.'@gedisa.de',
            'phoneNumber' => $owner->phone,
            'organisation' => $kimAddress->pharmacy->name,
            'referenceId' => $kimAddress->pharmacy->uuid,
            'note' => $kimAddress->email,
        ]);
    }

    public function createKimOrder(KimAddress $kimAddress): Response
    {
        $owner = $kimAddress->pharmacy->owner();

        $alreadyFiledKims = $kimAddress->pharmacy
            ->kimAddresses()
            ->whereNotIn('status', [KimAddressStatus::CANCELED->value])
            ->where('vendor', KimVendorEnum::AKQUINET)
            ->whereNotNull('additional->customerNumber')
            ->get();

        $additional = [];

        if (count($alreadyFiledKims) > 0) {
            $additional = [
                'customerNumber' => $alreadyFiledKims->first()->additional['customerNumber'],
            ];
        }

        $kimString = $alreadyFiledKims->pluck('email')->add($kimAddress->email)->implode(', ').', Kontakt: '.$kimAddress->pharmacy->email;

        return $this->makeRequest(method: 'post', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/gedisa/subCustomer', data: array_merge($additional, [
            'role' => 'Inaktiver Subkunde',
            'title' => $owner->title,
            'name' => $owner->last_name,
            'firstName' => $owner->first_name,
            'email' => $kimAddress->pharmacy->uuid.'@gedisa.de',
            'phoneNumber' => $owner->phone ?? $kimAddress->pharmacy->phone,
            'organisation' => $kimAddress->pharmacy->name,
            'referenceId' => $kimAddress->pharmacy->uuid,
            'note' => $kimString,
            'domainId' => config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.product_uuid'),
        ]));
    }

    public function getAllSubCustomers()
    {
        return $this->makeRequest(method: 'get', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/customers');
    }

    public function getAllSubCodes($page = 1)
    {
        return $this->makeRequest(method: 'get', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/codes/contingent', data: [
            'pageNumber' => $page,
            'pageSize' => 100,
            'sortColumn' => 'CODE',
            'sortDirection' => 'ASC',
        ]);
    }

    public function increaseAddressLimit(KimAddress $kimAddress): Response
    {
        return $this->makeRequest(method: 'post', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/orders/contingent', data: [
            'amount' => 1,
            'name' => 'BASIC',
        ]);
    }

    public function createKimAddress(KimAddress $kimAddress): Response
    {
        return $this->makeRequest(method: 'post', url: config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/api/codes/', data: [
            'domainId' => config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.product_uuid'),
            'amount' => '1',
            'referenceId' => $kimAddress->additional['customerNumber'],
        ]);
    }

    protected function makeRequest(string $method, string $url, array $data = []): Response
    {
        $this->addSentryBreadcrumb($method, $url, $data);

        try {
            return Http::withToken($this->getAdminAccessToken())
                ->acceptJson()
                ->asJson()
                ->timeout(90)
                ->throw()
                ->{$method}($url, $data);
        } catch (\Throwable $e) {
            report($e);

            throw new ApiException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function addSentryBreadcrumb(string $method, string $url, array $data = []): void
    {
        addBreadcrumb(
            new Breadcrumb(
                level: Breadcrumb::LEVEL_INFO,
                type: Breadcrumb::TYPE_DEFAULT,
                category: 'Kim Rise API Request',
                metadata: [
                    'method' => $method,
                    'url' => $url,
                    'data' => $data,
                ]
            )
        );
    }

    public function getAdminAccessToken(): string
    {
        if ($token = cache()->get(self::KIM_AKQUINET_ADMIN_CACHE_KEY)) {
            return $token;
        }

        $response = Http::asForm()
            ->post(config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.api_url').'/auth/realms/kim/protocol/openid-connect/token', [
                'client_id' => config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.client_id'),
                'username' => config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.client_username'),
                'password' => config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.api.client_password'),
                'grant_type' => 'password',
            ])->throw();

        $accessToken = $response->json()['access_token'];

        cache()->put(self::KIM_AKQUINET_ADMIN_CACHE_KEY, $accessToken, 295);

        return $accessToken;
    }
}
