<?php

namespace App\Helper\Kim;

use App\Enums\Kim\KimClientKonManEnum;
use App\Enums\Kim\KimClientMailSystemEnum;
use App\Enums\Kim\KimClientOSEnum;
use App\Enums\Kim\KimClientRemoteHelpSoftwareEnum;
use App\Enums\KimVendorEnum;
use App\Enums\SalutationEnum;
use App\KimAddress;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Sentry\Breadcrumb;

use function Sentry\addBreadcrumb;

class KimRiseAddressApi
{
    public const KIM_RISE_ADMIN_CACHE_KEY = 'kim_rise_admin_access_token';

    public function createOrder(KimAddress $kimAddress): Collection
    {
        $request = $this->makeRequest(
            method: 'post',
            url: config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.api_url').'/vendors/orders',
            data: $this->convertKimModelToApiData($kimAddress)
        );

        return $this->validateResponseFromCreateOrder($request);
    }

    public function getOrder(int $orderId): Response
    {
        return $this->makeRequest(method: 'get', url: config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.api_url').'/vendors/orders/'.$orderId);
    }

    public function getOrders(array $filter): Response
    {
        return $this->makeRequest(method: 'get', url: config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.api_url').'/vendors/orders', data: $filter);
    }

    protected function makeRequest(string $method, string $url, array $data = []): Response
    {
        $this->addSentryBreadcrumb($method, $url, $data);

        return Http::withToken($this->getKimRiseAdminAccessToken())
            ->acceptJson()
            ->asJson()
            ->{$method}($url, $data)
            ->throw();
    }

    public function addSentryBreadcrumb(string $method, string $url, array $data = []): void
    {
        addBreadcrumb(
            new Breadcrumb(
                level: Breadcrumb::LEVEL_INFO,
                type: Breadcrumb::TYPE_DEFAULT,
                category: 'Kim Rise API Request',
                metadata: [
                    'method' => $method,
                    'url' => $url,
                    'data' => $data,
                ]
            )
        );
    }

    public function getKimRiseAdminAccessToken(): string
    {
        $response = Http::asForm()
            ->post(config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.access_token_url'), [
                'client_id' => config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.client_id'),
                'client_secret' => config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.client_secret'),
                'grant_type' => 'client_credentials',
            ])->throw();

        $accessToken = $response->json()['access_token'];

        cache()->put(self::KIM_RISE_ADMIN_CACHE_KEY, $accessToken, 295);

        return $accessToken;
    }

    public function convertKimModelToApiData(KimAddress $kimAddress): array
    {
        $pharmacy = $kimAddress->pharmacy;
        $owner = $pharmacy->owner();

        if ($owner === null) {
            throw new \RuntimeException('No owner found');
        }

        return [
            'customerId' => $pharmacy->uuid,
            'userId' => $owner->uuid,
            'orderReference' => $kimAddress->id,
            'deliveryAddress' => [
                'street' => $pharmacy->street.' '.$pharmacy->house_number,
                'city' => $pharmacy->city,
                'zipCode' => $pharmacy->postcode,
                'country' => 'Deutschland',
            ],
            'products' => [
                [
                    'productId' => config('kim.vendors.'.KimVendorEnum::RISE->value.'.api.product_uuid'),
                    'amount' => 1,
                    'customAttributes' => [
                        'kimLocalPart' => $this->getEmail($kimAddress),
                        'kimClientOS' => KimClientOSEnum::value($kimAddress->additional['kimClientOS']),
                        'kimClientKonMan' => KimClientKonManEnum::value($kimAddress->additional['kimClientKonMan']),
                        'kimClientMailSystem' => KimClientMailSystemEnum::value($kimAddress->additional['kimClientMailSystem']),
                        'kimClientRemoteHelpSoftware' => KimClientRemoteHelpSoftwareEnum::value($kimAddress->additional['kimClientRemoteHelpSoftware']),
                    ],
                ],
            ],
            'customerInfo' => [
                'name' => $pharmacy->name,
                'medicalId' => $pharmacy->institute_id,
                'address' => [
                    'street' => $pharmacy->street.' '.$pharmacy->house_number,
                    'city' => $pharmacy->city,
                    'zipCode' => $pharmacy->postcode,
                    'country' => 'Deutschland',
                ],
            ],
            'userInfo' => [
                'email' => $owner->email,
                'salutation' => $this->getSalutation($owner->salutation),
                'firstName' => $owner->first_name,
                'lastName' => $owner->last_name,
                'phoneNumber' => $pharmacy->phone,
            ],
        ];
    }

    public function validateResponseFromCreateOrder(Response $response): Collection
    {
        $responseArray = $response->json();

        if (

            ! array_key_exists('uuid', $responseArray)
            || $responseArray['uuid'] === null
            || $responseArray['uuid'] === ''
            || ! array_key_exists('orderNumber', $responseArray)
            || $responseArray['orderNumber'] === null
            || $responseArray['orderNumber'] === ''
            || ! array_key_exists('orderDate', $responseArray)
            || $responseArray['orderDate'] === null
            || $responseArray['orderDate'] === ''
        ) {
            return collect();
        }

        return $response->collect();
    }

    public function getEmail(KimAddress $kimAddress): string
    {
        return Str::before($kimAddress->email, '@');
    }

    public function getSalutation($salutation): string
    {
        return match ($salutation) {
            SalutationEnum::MR => 'MR',
            SalutationEnum::MS => 'MRS',
            SalutationEnum::DI => 'D',
        };
    }
}
