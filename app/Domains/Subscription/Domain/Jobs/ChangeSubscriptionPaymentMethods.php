<?php

namespace App\Domains\Subscription\Domain\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use <PERSON><PERSON>\Cashier\Cashier;
use <PERSON><PERSON>\Cashier\Subscription;

class ChangeSubscriptionPaymentMethods implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param  string[]  $paymentMethods
     */
    public function __construct(
        public Subscription $subscription,
        public array $paymentMethods = ['sepa_debit', 'customer_balance'],
    ) {}

    public function handle(): void
    {
        $stripeSubscription = $this->subscription->asStripeSubscription();

        // If there is already a payment method to charge automatically, we keep the currently used payment method
        if ($stripeSubscription->collection_method === 'charge_automatically') {
            return;
        }

        Cashier::stripe()->subscriptions->update(
            $stripeSubscription->id,
            [
                'payment_settings' => [
                    'payment_method_types' => $this->paymentMethods,
                ],
            ]
        );
    }
}
