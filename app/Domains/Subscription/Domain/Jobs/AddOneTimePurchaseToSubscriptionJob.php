<?php

namespace App\Domains\Subscription\Domain\Jobs;

use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Domains\Subscription\Domain\Actions\Subscription\AddOneTimePurchaseToSubscription;
use App\Pharmacy;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AddOneTimePurchaseToSubscriptionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Pharmacy $pharmacy,
        public StripeProduct $product,
        public ?Carbon $date = null,
    ) {}

    public function handle(AddOneTimePurchaseToSubscription $action): void
    {
        $action->groupMonthly()->execute(
            $this->pharmacy,
            $this->product,
            $this->date ?? now('Europe/Berlin')
        );
    }
}
