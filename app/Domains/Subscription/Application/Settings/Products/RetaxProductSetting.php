<?php

namespace App\Domains\Subscription\Application\Settings\Products;

use App\Domains\Subscription\Application\StripeProducts\AddOns\RetaxStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;

class RetaxProductSetting extends StripeProductSetting
{
    public static function getStripeProduct(): StripeProduct
    {
        return app(RetaxStripeProduct::class);
    }
}
