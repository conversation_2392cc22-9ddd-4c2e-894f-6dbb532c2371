<?php

namespace App\Domains\Subscription\Application\StripeProducts\AddOns;

use App\Domains\Payment\Domain\Data\StripeProductFrontendTextData;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\Settings\Products\SDRProductSetting;
use App\Domains\Subscription\Application\Settings\Products\StripeProductSetting;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Domains\Subscription\Domain\Enums\StripeProductTypeEnum;
use App\Pharmacy;

class RetaxStripeProduct extends StripeProduct
{
    public static StripeProductTypeEnum $type = StripeProductTypeEnum::ADD_ON;

    public static bool $isProrated = true;

    public function getSettings(): StripeProductSetting
    {
        return app(SDRProductSetting::class);
    }

    public function getPublicRepresentationData(Pharmacy $pharmacy): StripePublicRepresentationData
    {
        return new StripePublicRepresentationData(
            class: self::class,
            price: $this->getOneTimeStripePrice($pharmacy)->price,
            text: new StripeProductFrontendTextData(
                name: 'Retax',
                description: '',
                features: []
            )
        );
    }
}
