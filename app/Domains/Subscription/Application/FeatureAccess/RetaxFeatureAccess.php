<?php

namespace App\Domains\Subscription\Application\FeatureAccess;

use App\Domains\Subscription\Application\StripeProducts\AddOns\RetaxStripeProduct;
use App\Settings\RetaxSettings;
use Illuminate\Database\Eloquent\Builder;
use RuntimeException;

class RetaxFeatureAccess extends FeatureAccess
{
    protected function canUseNew(): bool
    {
        return $this->pharmacy->canUseProduct(RetaxStripeProduct::class) && app(RetaxSettings::class)->isAllowedToUse($this->pharmacy->owner()?->fresh());
    }

    protected function scopeCanUseNew(Builder $builder): Builder
    {
        throw new RuntimeException('Not implemented');
    }
}
