<?php

namespace App\Domains\Payment\Domain\Data;

use Illuminate\Support\Number;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class StripePublicRepresentationData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public string $class,
        public int $price,
        public ?StripeProductFrontendTextData $text = null,
        public ?StripeProductFrontendBookingUrlData $bookingUrl = null,
        public ?string $additionalInformation = null,
        public ?string $pricePrefix = null,
    ) {}

    public function displayPrice(): string
    {
        if ($this->pricePrefix) {
            return sprintf('%s %s', $this->pricePrefix, Number::currency($this->price / 100, 'EUR'));
        }

        return sprintf('%s', Number::currency($this->price / 100, 'EUR'));
    }
}
