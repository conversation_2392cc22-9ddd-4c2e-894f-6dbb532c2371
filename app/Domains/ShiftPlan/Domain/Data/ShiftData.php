<?php

namespace App\Domains\ShiftPlan\Domain\Data;

use App\ShiftPlan;
use App\ShiftPlanGroupUser;
use Carbon\Carbon;
use Livewire\Wireable;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class ShiftData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public string $name,
        public string $color,
        public int $shiftPlanId,
        public int $shiftPlanGroupUserId,
        #[WithCast(DateTimeInterfaceCast::class)]
        public Carbon $startsAt,
        #[WithCast(DateTimeInterfaceCast::class)]
        public Carbon $endsAt,
        public int $breakDuration,
        public ?ShiftPlan $shiftPlan = null,
        public ?ShiftPlanGroupUser $planGroupUser = null,
        public ?int $duration = null,
        public ?string $uuid = null,
    ) {}
}
