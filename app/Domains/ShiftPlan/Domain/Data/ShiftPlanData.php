<?php

namespace App\Domains\ShiftPlan\Domain\Data;

use App\ShiftPlan;
use Livewire\Wireable;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class ShiftPlanData extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public string $name,
        public int $ownerId,
        public ?int $id = null,
        public ?string $uuid = null,
    ) {}

    public static function fromShiftPlan(ShiftPlan $shiftPlan): self
    {
        return self::from($shiftPlan->toArray());
    }
}
