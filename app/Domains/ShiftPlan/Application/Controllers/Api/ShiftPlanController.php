<?php

namespace App\Domains\ShiftPlan\Application\Controllers\Api;

use App\Domains\ShiftPlan\Application\Queries\IndexShiftPlanQuery;
use App\Domains\ShiftPlan\Application\Resources\ShiftPlanResource;
use App\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;

class ShiftPlanController extends Controller
{
    public function index(IndexShiftPlanQuery $query): Response|JsonResource
    {
        return ShiftPlanResource::collection($query->get());
    }
}
