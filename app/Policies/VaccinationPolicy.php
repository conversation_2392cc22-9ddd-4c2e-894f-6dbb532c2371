<?php

namespace App\Policies;

use App\CovidVaccination;
use App\Domains\Subscription\Application\FeatureAccess\VaccinationFeatureAccess;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\InfluenzaVaccination;
use App\Pharmacy;
use App\User;
use App\Vaccination;
use Illuminate\Auth\Access\HandlesAuthorization;

class VaccinationPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function viewAny(User $user, Pharmacy $pharmacy)
    {
        if (! VaccinationFeatureAccess::check($pharmacy)->canUse()) {
            return false;
        }

        return $user->can('store', [CovidVaccination::class, $pharmacy]) || $user->can('store', [InfluenzaVaccination::class, $pharmacy]);
    }

    public function update(User $user, Vaccination $vaccination)
    {
        if ($vaccination->type == VaccinationTypeEnum::INFLUENZA) {
            return $user->can('update', $vaccination->influenzaVaccination);
        }
        if ($vaccination->type == VaccinationTypeEnum::COVID) {
            return $user->can('update', $vaccination->covidVaccination);
        }

        return false;
    }
}
