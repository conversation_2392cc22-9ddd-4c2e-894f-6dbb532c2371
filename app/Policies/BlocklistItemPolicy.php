<?php

namespace App\Policies;

use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Traits\AuthorizesStaff;
use Illuminate\Auth\Access\HandlesAuthorization;

class BlocklistItemPolicy
{
    use AuthorizesStaff, HandlesAuthorization, NovaDefaultFunctions;

    /**
     * @return array<string, array<PermissionEnum>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::OPERATIONS => PermissionEnum::except(PermissionEnum::restoreAndForceDelete()),
            StaffRoleEnum::SUPPORT => PermissionEnum::except([PermissionEnum::DELETE, PermissionEnum::RESTORE, PermissionEnum::FORCE_DELETE]),
        ];
    }
}
