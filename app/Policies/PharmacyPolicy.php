<?php

namespace App\Policies;

use App\Domains\Subscription\Application\FeatureAccess\AdvertisingMaterialFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\CalendarFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\ChatFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\DataAccessPeriodFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\PharmacyAdministrationFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\RetaxFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\TelepharmacyFeatureAccess;
use App\Enums\PermissionEnum;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\PharmaceuticalService;
use App\Pharmacy;
use App\Traits\AuthorizesStaff;
use App\User;
use App\Vaccination;
use Illuminate\Auth\Access\HandlesAuthorization;

class PharmacyPolicy
{
    use AuthorizesStaff, HandlesAuthorization;

    /**
     * @return array<string, array<PermissionEnum|string>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::OPERATIONS => [...PermissionEnum::except(PermissionEnum::restoreAndForceDelete()), 'downloadApoguideQrCode'],
            StaffRoleEnum::SUPPORT => [...PermissionEnum::except([PermissionEnum::DELETE, PermissionEnum::RESTORE, PermissionEnum::FORCE_DELETE]), 'downloadApoguideQrCode'],
        ];
    }

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function index(User $user): bool
    {
        return $user->isPharmacyUser();
    }

    public function view(User $user, Pharmacy $pharmacy): bool
    {
        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id)
            );
    }

    public function create(User $user): bool
    {
        return $user->isPharmacyUser() && $user->brochureCode !== null;
    }

    public function update(User $user, Pharmacy $pharmacy): bool
    {
        if (! $this->hasBaseAccess($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            && (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::EDIT_PHARMACY)
            );
    }

    public function selectBillingAddress(User $user, Pharmacy $pharmacy): bool
    {
        return
            $user->isPharmacyUser()
            && (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::SELECT_PHARMACY_BILLING_ADDRESS)
            );
    }

    public function delete(User $user, Pharmacy $pharmacy): bool
    {
        return false; // AP-2489 - deletion is disabled globally

        /*
        return $user->isPharmacyUser()
            && (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER)
            )
           && $pharmacy->subscriptionOrders()->pendingPayment()->count() === 0
           && ! $pharmacy->subscribed()
           && $pharmacy->kimAddresses()->whereIn('status', [KimAddressStatus::ORDERED, KimAddressStatus::ACTIVATED, KimAddressStatus::CANCELLATION_SCHEDULED, KimAddressStatus::SCHEDULED])->doesntExist()
           && (
               ! $pharmacy->cardLinkOrder
               || $pharmacy->cardLinkOrder->status === CardLinkOrderStatusEnum::Reserved
           );
        */
    }

    public function activate(User $user, Pharmacy $pharmacy): bool
    {
        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER)
                ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER)
            );
    }

    public function deactivate(User $user, Pharmacy $pharmacy): bool
    {
        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER)
                ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER)
            );
    }

    public function administrateUsers(User $user, Pharmacy $pharmacy): bool
    {
        // should not have permission if user has expired subscription
        if (! $this->hasBaseAccess($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::ADMINISTRATE_USERS)
            );
    }

    public function administrateSubOwners(User $user): bool
    {
        return $user->isCompany();
    }

    public function administratePharmacySubOwners(User $user, Pharmacy $pharmacy): bool
    {
        return
            $user->isCompany()
            &&
            $user->getPharmacyRole($pharmacy) === PharmacyRoleEnum::OWNER;
    }

    public function accessCalendar($user, $pharmacy): bool
    {
        if (! $pharmacy->uses_calendar) {
            return false;
        }

        if (! $this->activateCalendar($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::CALENDAR) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::CALENDAR_ADMIN)
            );
    }

    public function accessCalendarAsAdmin($user, $pharmacy): bool
    {
        if (! $pharmacy->uses_calendar) {
            return false;
        }

        if (! $this->activateCalendar($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::CALENDAR_ADMIN)
            );
    }

    public function activateCalendar(User $user, ?Pharmacy $pharmacy = null): bool
    {
        return $pharmacy && CalendarFeatureAccess::check($pharmacy)->canUse();
    }

    public function dataAfterSubscription(User $user, Pharmacy $pharmacy): bool
    {
        return DataAccessPeriodFeatureAccess::check($pharmacy)->canUse();
    }

    private function hasBaseAccess(User $user, Pharmacy $pharmacy): bool
    {
        /** @var User $owner */
        $owner = $user->owner();

        if (! $owner) {
            return false;
        }

        if (! PharmacyAdministrationFeatureAccess::check($pharmacy)->exceptTerms()->canUse()) {
            return false;
        }

        return true;
    }

    public function accessChat($user, Pharmacy $pharmacy): bool
    {
        if (! ChatFeatureAccess::check($pharmacy)->canUse()) {
            return false;
        }

        return $pharmacy->uses_chat;
    }

    public function activateChat(User $user, ?Pharmacy $pharmacy = null): bool
    {
        if (! $pharmacy || ! ChatFeatureAccess::check($pharmacy)->canUse()) {
            return false;
        }

        return $user->isOwner() || $user->isSubOwner();
    }

    public function accessTelepharmacy(User $user, Pharmacy $pharmacy): bool
    {
        if (! $pharmacy->uses_telepharmacy) {
            return false;
        }

        if (! $this->activateTelepharmacy($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::TELEPHARMACY) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::TELEPHARMACY_ADMIN)
            );
    }

    public function accessTelepharmacyAsAdmin(User $user, Pharmacy $pharmacy): bool
    {
        if (! $pharmacy->uses_telepharmacy) {
            return false;
        }

        if (! $this->activateTelepharmacy($user, $pharmacy)) {
            return false;
        }

        return
            $user->isPharmacyUser()
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::TELEPHARMACY_ADMIN)
            );
    }

    public function activateTelepharmacy(User $user, ?Pharmacy $pharmacy = null): bool
    {
        return $pharmacy && TelepharmacyFeatureAccess::check($pharmacy)->canUse();
    }

    public function accessOverview(User $user, Pharmacy $pharmacy): bool
    {
        if (! $this->view($user, $pharmacy)) {
            return false;
        }

        $canAccessPharmacyFeature = $user->canAny([
            'update',
            'dataAfterSubscription',
            'administrateUsers',
        ], [$pharmacy]);

        $canAccessServiceFeature = $user->can('index', [PharmaceuticalService::class, $pharmacy])
            || $user->can('viewAny', [Vaccination::class, $pharmacy]);

        return $canAccessPharmacyFeature || $canAccessServiceFeature;
    }

    public function viewPharmacyMenu(User $user, ?Pharmacy $pharmacy = null): bool
    {
        if (! user()->isPharmacyUser() || ! user()->hasPharmacies()) {
            return false;
        }

        if ($pharmacy === null) {
            return false;
        }

        $canAccessPharmacyMenuItem = $user->canAny([
            'accessOverview',
            'administrateUsers',
            'accessCalendar',
            'accessCalendarAsAdmin',
            'accessChat',
            'accessTelepharmacy',
            'accessTelepharmacyAsAdmin',
        ], [$pharmacy]);

        if ($canAccessPharmacyMenuItem) {
            return true;
        }

        return false;
    }

    public function viewApoGuideShopLink(User $user, ?Pharmacy $pharmacy = null): bool
    {
        if ($pharmacy === null) {
            return false;
        }

        return ($user->isOwner() || $user->isSubOwner())
               && AdvertisingMaterialFeatureAccess::check($pharmacy)->canUse();
    }

    public function downloadApoguideQrCode(User $user, Pharmacy $pharmacy): bool
    {
        return $this->update($user, $pharmacy) && $pharmacy->hasAcceptedTerms() && $pharmacy->show_in_apoguide;
    }

    public function indexRetax(User $user, ?Pharmacy $pharmacy = null): bool
    {
        return $pharmacy && $user->isOwner() && $this->hasBaseAccess($user, $pharmacy) && RetaxFeatureAccess::check($pharmacy)->canUse();
    }

    public function useRetax(User $user, ?Pharmacy $pharmacy = null): bool
    {
        return $this->indexRetax($user, $pharmacy) && $user->hasFinishedRetaxOnboarding();
    }
}
