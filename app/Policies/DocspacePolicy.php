<?php

namespace App\Policies;

use App\DocSpace;
use App\Pharmacy;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DocspacePolicy
{
    use HandlesAuthorization;

    public function index(User $user, Pharmacy $pharmacy)
    {
        return $user->can('sdr.viewAny', $pharmacy);
    }

    public function store(User $user, Pharmacy $pharmacy, bool $ignoreMaxSpaces = false)
    {
        return $user->can('sdr.viewAny', $pharmacy)
                && $user->isOwnerOrSubOwnerOfPharmacy($pharmacy)
               && ($ignoreMaxSpaces || $pharmacy->docSpaces->count() < DocSpace::MAX_SPACES);
    }

    public function storeGuided(User $user, Pharmacy $pharmacy)
    {
        return $this->store($user, $pharmacy)
                && $pharmacy->docSpaces->count() === 0;
    }

    public function update(User $user, DocSpace $docSpace)
    {
        return $user->can('sdr.viewAny', $docSpace->pharmacy)
                && $user->isOwnerOrSubOwnerOfPharmacy($docSpace->pharmacy);
    }

    public function viewEditMode(User $user, Pharmacy $pharmacy)
    {
        return $user->can('sdr.viewAny', $pharmacy);
    }
}
