<?php

namespace App\Policies;

use App\Association;
use App\Domains\Subscription\Application\StripeProducts\AddOns\RetaxStripeProduct;
use App\Enums\AssociationPermissionsEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\HealthInsuranceCompany;
use App\Traits\AuthorizesStaff;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AssociationPolicy
{
    use AuthorizesStaff, HandlesAuthorization;

    /**
     * @return array<string, array<PermissionEnum>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::OPERATIONS => PermissionEnum::except(PermissionEnum::restoreAndForceDelete()),
        ];
    }

    public function viewAny(User $user)
    {
        return true;
    }

    public function index(User $user)
    {
        return $user->isAssociationUser();
    }

    public function view(User $user, Association $association)
    {
        return
            $user->isAssociationUser()
            &&
            $user->hasAssociationRole($association->id);
    }

    public function create(User $user)
    {
        return false;
    }

    public function update(User $user, Association $association)
    {
        return
            $user->isAssociationUser()
            &&
            (
                $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
                ||
                $user->hasAssociationPermission($association->id, AssociationPermissionsEnum::EDIT_ASSOCIATION)
            );
    }

    public function delete(User $user, Association $association)
    {
        return false;
    }

    public function vaccinateView(User $user, Association $association)
    {
        return
            (bool) $association->settings->can_vaccinate
            &&
            $user->isAssociationUser()
            &&
            (
                $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
                || $user->hasAssociationPermission($association->id, AssociationPermissionsEnum::VACCINATION_PORTAL)
            );
    }

    public function vaccinateViewModel(User $user, Association $association)
    {
        return
            (bool) $association->settings->can_vaccinate_model
            &&
            $user->isAssociationUser()
            &&
            (
                $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
                || $user->hasAssociationPermission($association->id, AssociationPermissionsEnum::VACCINATION_PORTAL)
            );
    }

    public function vaccinateExport(User $user, Association $association)
    {
        return
            (bool) $association->settings->can_vaccinate
            &&
            $user->isAssociationUser()
            &&
            (
                $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
                || $user->hasAssociationPermission($association->id, AssociationPermissionsEnum::VACCINATION_PORTAL)
            );
    }

    public function administrateUsers(User $user, Association $association)
    {
        return
            $user->isAssociationUser()
            &&
            (
                $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
                ||
                $user->hasAssociationPermission($association->id, AssociationPermissionsEnum::ADMINISTRATE_USERS)
            );
    }

    public function administrateMembers(User $user, Association $association)
    {
        return $user->isAssociationUser();
    }

    public function administrateHealthInsuranceCompanies(User $user, Association $association)
    {
        return $this->update($user, $association) && $this->vaccinateView($user, $association);
    }

    public function attachAnyHealthInsuranceCompany(User $user, Association $association)
    {
        return false;
    }

    public function detachHealthInsuranceCompany(User $user, Association $association, HealthInsuranceCompany $healthInsuranceCompany)
    {
        return false;
    }

    public function manageNews(User $user, Association $association): bool
    {
        return $user->isAssociationUser()
        &&
        (
            $user->hasAssociationRole($association->id, AssociationRoleEnum::ADMIN)
            || $user->hasAssociationPermission($association, AssociationPermissionsEnum::MANAGE_NEWS)
        );
    }

    public function indexRetax(User $user, Association $association): bool
    {
        if (! $user->hasAssociationRole($association->id)) {
            return false;
        }

        return collect($association->currentAssociationFrameworkContractHistory?->contract->instance()->getIncludedProducts())->contains(RetaxStripeProduct::class);

    }
}
