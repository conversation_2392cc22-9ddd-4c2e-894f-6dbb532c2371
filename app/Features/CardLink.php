<?php

namespace App\Features;

use App\Contracts\PennantFeature;
use App\Pharmacy;
use App\Settings\CardLinkSettings;

class CardLink implements PennantFeature
{
    public function resolve(Pharmacy|string $scope): bool
    {
        return true;
    }

    public function before(Pharmacy|string $scope): ?bool
    {
        if (now()->isBefore(app(CardLinkSettings::class)->reservationEnabledAt)) {
            return false;
        }

        return null;
    }
}
