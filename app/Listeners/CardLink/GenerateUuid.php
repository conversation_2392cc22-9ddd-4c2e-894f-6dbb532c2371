<?php

namespace App\Listeners\CardLink;

use App\CardLinkOrder;
use App\Events\CreatingCardLinkOrder;
use Illuminate\Support\Str;

class GenerateUuid
{
    public function handle(CreatingCardLinkOrder $event): void
    {
        do {
            $uuid = Str::uuid()->toString();
        } while (CardLinkOrder::query()->where('uuid', $uuid)->exists());

        $event->cardLinkOrder->uuid = $uuid;
    }
}
