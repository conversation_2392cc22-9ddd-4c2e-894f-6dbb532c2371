<?php

namespace App;

use App\Enums\AssociationMembershipChangeModeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssociationMembershipChange extends Model
{
    protected $guarded = [];

    protected $casts = [
        'change_at' => 'datetime',
        'change_done_at' => 'datetime',
        'canceled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'mode' => AssociationMembershipChangeModeEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function associationBefore(): BelongsTo
    {
        return $this->belongsTo(Association::class, 'association_id_before');
    }

    public function associationAfter(): BelongsTo
    {
        return $this->belongsTo(Association::class, 'association_id_after');
    }
}
