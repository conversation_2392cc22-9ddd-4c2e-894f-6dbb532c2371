<?php

namespace App\Livewire\ShiftPlan;

use App\Domains\ShiftPlan\Domain\Actions\Shift\CreateShiftAction;
use App\Domains\ShiftPlan\Domain\Actions\Shift\UpdateShiftAction;
use App\Domains\ShiftPlan\Domain\Data\ShiftData;
use App\Pharmacy;
use App\Shift;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Livewire\Attributes\On;
use Livewire\Component;
use Spatie\LaravelData\Optional;

class CreateOrEditShiftModal extends Component
{
    public ShiftPlan $shiftPlan;

    public string $title = '';

    public string $color = '';

    /** @var array<array<string, string|Optional|string|null>> */
    public array $availableUsers = [];

    public ?string $day = null;

    public string $group_user_id = '';

    public string $start = '09:00';

    public string $end = '17:00';

    public ?int $break_duration = null;

    public ?Shift $shift = null;

    /** @var array<string, array<string>> */
    public array $rules = [
        'title' => ['required', 'string'],
        'color' => ['required', 'hex_color'],
        'group_user_id' => ['required', 'integer'],
        'day' => ['required', 'date'],
        'start' => ['required', 'date_format:H:i'],
        'end' => ['required', 'date_format:H:i'],
        'break_duration' => ['nullable', 'integer'],
    ];

    public function mount(): void
    {
        $this->reloadAvailableUsers();
        $this->setBusinessHoursRange();
        $this->setBreaktime();
    }

    #[On(['employee.added', 'shiftplan.group.deleted'])]
    public function reloadAvailableUsers(): void
    {
        /** @var Collection<ShiftPlanGroup> $groups */
        $groups = ShiftPlanGroup::query()->with('shiftPlanGroupUsers')
            ->where('shift_plan_id', $this->shiftPlan->id)
            ->get();

        $this->availableUsers = [];

        foreach ($groups as $group) {
            if ($group->shiftPlanGroupUsers === null || $group->shiftPlanGroupUsers->isEmpty()) {
                continue;
            }

            foreach ($group->shiftPlanGroupUsers as $groupUser) {
                $this->availableUsers[] = [
                    'group_user_id' => (string) $groupUser->id,
                    'group_name' => $group->name,
                    'user_full_name' => $groupUser->user?->full_name,
                ];
            }
        }
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareRequest(): array
    {
        return [
            'name' => $this->title,
            'color' => str_replace('#', '', $this->color),
            'shift_plan_id' => $this->shiftPlan->id,
            'shift_plan_group_user_id' => $this->group_user_id,
            'starts_at' => Carbon::parse($this->day.' '.$this->start),
            'ends_at' => Carbon::parse($this->day.' '.$this->end),
            'break_duration' => $this->break_duration ?? 0,
        ];
    }

    #[On('shift.create')]
    public function prepareModalForCreate(?int $groupId = null, ?int $userId = null, ?string $day = ''): void
    {
        $this->cleanUp();
        $this->day = $day;

        if ($groupId && $userId) {
            $shiftPlanGroupUser = ShiftPlanGroupUser::query()->with('group')
                ->where('shift_plan_group_id', $groupId)
                ->where('user_id', $userId)
                ->firstOrFail();

            $this->group_user_id = (string) $shiftPlanGroupUser->id;

            $this->color = sprintf('#%s', $shiftPlanGroupUser->group?->color ?? 'FFFFFF');
        }

        $this->openModal('createOrEditShiftModal');
    }

    /**
     * @throws Exception
     */
    #[On('shift.edit')]
    public function prepareModalForEdit(int $id): void
    {
        $this->cleanUp();
        $this->shift = Shift::query()->findOrFail($id);

        $this->authorize('update', $this->shift);

        $this->fill([
            'title' => $this->shift->name,
            'color' => '#'.$this->shift->color,
            'day' => $this->shift->starts_at->format('Y-m-d'),
            'start' => $this->shift->starts_at->format('H:i'),
            'end' => $this->shift->ends_at->format('H:i'),
            'group_user_id' => (string) $this->shift->shift_plan_group_user_id,
            'break_duration' => $this->shift->break_duration,
        ]);

        $this->openModal('createOrEditShiftModal');
    }

    public function createShift(): void
    {
        $this->authorize('create', [Shift::class, $this->shiftPlan]);

        $this->validate($this->rules);

        $data = ShiftData::from($this->prepareRequest());

        CreateShiftAction::execute($data);

        $this->dispatch('shiftplan.refresh');
        $this->closeModal('createOrEditShiftModal');
    }

    public function updateShift(): void
    {
        assert($this->shift instanceof Shift);

        $this->authorize('update', $this->shift);

        $this->validate();

        $data = ShiftData::from(array_merge($this->shift->toArray(), $this->prepareRequest()));

        UpdateShiftAction::execute($this->shift, $data->except('plan', 'planGroupUser'));

        $this->dispatch('shiftplan.refresh');
        $this->closeModal('createOrEditShiftModal');
    }

    #[On('shift.modal.on-close')]
    public function onCloseModal(): void
    {
        $this->shift = null;
        $this->resetErrorBag();
    }

    public function cleanUp(): void
    {
        $this->reset([
            'title',
            'color',
            'group_user_id',
            'day',
            'start',
            'end',
            'break_duration',
            'shift',
        ]);

        $this->setBusinessHoursRange();
        $this->setBreaktime();
    }

    public function render(): View
    {
        return view('livewire.shift-plan.create-or-edit-shift-modal');
    }

    public function setBusinessHoursRange(): void
    {
        $pharmacy = currentPharmacy();
        $businessHours = collect();

        if ($pharmacy instanceof Pharmacy) {
            $businessHours = $pharmacy->businessHours()->select(['opens', 'closes'])->get();
        }

        if ($businessHours->count() !== 0) {
            $opens = $businessHours->min('opens');
            $closes = $businessHours->max('closes');

            if (is_string($opens)) {
                $this->start = date('H:i', (int) strtotime($opens));
            }

            if (is_string($closes)) {
                $this->end = date('H:i', (int) strtotime($closes));
            }
        }
    }

    public function setBreaktime(): void
    {
        $openInHours = Carbon::parse($this->end)->diff($this->start)->format('%H');

        $this->break_duration = match (true) {
            $openInHours >= 9 => 45,
            $openInHours >= 6 => 30,
            default => 0
        };
    }
}
