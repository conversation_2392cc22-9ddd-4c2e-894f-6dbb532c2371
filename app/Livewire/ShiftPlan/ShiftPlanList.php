<?php

namespace App\Livewire\ShiftPlan;

use App\ShiftPlan;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;

class ShiftPlanList extends Component
{
    use WithPagination;

    #[Computed]
    public function shiftPlans(): LengthAwarePaginator
    {
        return ShiftPlan::query()
            ->when(user()?->owner()?->id, fn (Builder $query, $id) => $query->where('owner_id', $id))
            ->paginate();
    }
}
