<?php

namespace App\Livewire\ShiftPlan\ShiftPlanSteps;

use App\Helper\Color;
use App\Livewire\ShiftPlan\ShiftPlanWizard;
use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;

class AddGroupsStep extends WizardStep
{
    use InteractsWithSession;

    /**
     * @var array<array<string, int|string>>
     */
    public array $groups = [];

    public ?string $nextStep = AddUsersStep::class;

    public function mount(): void
    {
        $sessionData = session()->get(ShiftPlanWizard::sessionPrefix());

        if (is_array($sessionGroups = data_get($sessionData, 'groups'))) {
            $this->groups = $sessionGroups;
        }
    }

    protected function title(): string
    {
        return 'Dienstplan einrichten';
    }

    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            'groups' => ['required', 'array'],
            'groups.*.sort_order' => ['required', 'int'],
            'groups.*.name' => ['required', 'string'],
            'groups.*.color' => ['required', 'hex_color'],
        ];
    }

    public function addGroup(): void
    {
        $this->groups[] = [
            'sort_order' => count($this->groups),
            'name' => '',
            'color' => Color::randomPredefinedColor(),
        ];
    }

    public function removeGroup(int $index): void
    {
        unset($this->groups[$index]);

        sort($this->groups);

        $this->groups = array_map(function ($group, $index) {
            $group['sort_order'] = $index;

            return $group;
        }, $this->groups, array_keys($this->groups));
    }

    public function submit(): void
    {
        $this->validate();

        session()->put(implode('.', [self::sessionPrefix(), 'groups']), $this->groups);

        $this->next();
    }

    protected function sessionPrefix(): ?string
    {
        return ShiftPlanWizard::sessionPrefix();
    }
}
