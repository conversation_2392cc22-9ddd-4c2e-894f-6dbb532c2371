<?php

namespace App\Livewire\Retax;

use App\Settings\RetaxSettings;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Livewire\Component;

class Index extends Component
{
    use AuthorizesRequests;

    public function mount(): void
    {
        $pharmacy = user()?->pharmacies->first();
        $this->authorize('index-retax', $pharmacy);

        if (user() && user()->can('use-retax', $pharmacy)) {
            $this->redirect(app(RetaxSettings::class)->base_url);
        }
    }

    public function render(): View
    {
        return view('livewire.retax.index')
            ->extends('layouts.app', [
                'novue' => true,
            ])
            ->section('content');
    }
}
