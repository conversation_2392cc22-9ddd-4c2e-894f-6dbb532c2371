<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\SessionEnum;
use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;

class WelcomeStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 1;

    public ?string $nextStep = SelectPharmacyStep::class;

    protected function nextStepLabel(): string
    {
        return 'Weiter';
    }

    public function submit(): void
    {
        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
