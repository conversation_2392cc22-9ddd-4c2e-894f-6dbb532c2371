<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\SessionEnum;
use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;

class InfoDocSpaceStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 4;

    public ?string $nextStep = UnlockDocSpaceStep::class;

    protected function nextStepLabel(): string
    {
        return 'Weiter zur SDR-Aktivierung';
    }

    public function submit(): void
    {
        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
