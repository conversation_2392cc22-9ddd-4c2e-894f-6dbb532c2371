<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\SessionEnum;
use App\Livewire\Wizard\WizardStep;
use App\Rules\Ownership;
use App\Traits\InteractsWithWizard;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;
use Livewire\Attributes\Computed;

class SelectPharmacyStep extends WizardStep
{
    use InteractsWithWizard;

    public ?int $selectedPharmacy;

    public ?int $stepNumber = 2;

    public ?string $nextStep = CheckPharmacyInformationStep::class;

    public function mount(): void
    {
        $this->selectedPharmacy = $this->pharmacies
            ->firstWhere('is_main', true)
            ?->id ?? null;
    }

    protected function title(): string
    {
        return 'Retax einrichten';
    }

    /**
     * @return array<string, array<int, Ownership|Exists|string>>
     */
    public function rules(): array
    {
        $user = user();
        $user || abort(403);

        return [
            'selectedPharmacy' => ['required', Rule::exists('pharmacies', 'id'), new Ownership($user)],
        ];
    }

    /**
     * @return array<string, array<string, string>>
     */
    public function messages(): array
    {
        return [
            'selectedPharmacy' => [
                'required' => 'Sie müssen eine Apotheke auswählen um Retax einzurichten.',
            ],
        ];
    }

    public function submit(): void
    {
        $this->validate();

        $this->pharmacies->each(function ($pharmacy) {
            $pharmacy->is_main = $pharmacy->id === $this->selectedPharmacy;
            $pharmacy->save();
        });

        $this->next();
    }

    #[Computed]
    public function pharmacies(): Collection
    {
        $user = user();
        $user || abort(403);

        return $user->pharmacies;
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }

    protected function nextStepLabel(): string
    {
        return 'Weiter';
    }
}
