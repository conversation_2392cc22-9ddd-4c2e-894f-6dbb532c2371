<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\SessionEnum;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;

class UnlockDocSpaceStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 4;

    public ?string $nextStep = CreateDocSpaceStep::class;

    public Pharmacy $pharmacy;

    public bool $usesSDR = false;

    public bool $acceptsSDR = false;

    public function mount(): void
    {
        $user = user();
        $user || abort(403);

        $pharmacy = $user->pharmacies->firstWhere('is_main', true);
        $pharmacy || abort(403);

        $this->pharmacy = $pharmacy;

        if ($this->pharmacy->uses_sdr) {
            $this->next();
        }
    }

    protected function nextStepLabel(): string
    {
        return 'Datenraum aktivieren und anlegen';
    }

    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            'usesSDR' => ['boolean', 'accepted'],
            'acceptsSDR' => ['boolean', 'accepted'],
        ];
    }

    /**
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'usesSDR.accepted' => 'Für die Aktivierung des sicheren Datenraums muss die Freischaltung akzeptiert werden.',
            'acceptsSDR.accepted' => 'Für die Aktivierung des sicheren Datenraums müssen die Nutzungsbedingungen akzeptiert werden.',
        ];
    }

    public function submit(): void
    {
        $this->validate();

        $this->pharmacy->update([
            'uses_sdr' => 1,
        ]);

        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
