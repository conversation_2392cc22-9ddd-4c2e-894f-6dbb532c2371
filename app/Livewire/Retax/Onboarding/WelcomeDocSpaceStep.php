<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\SessionEnum;
use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;
use Illuminate\Support\Collection;

class WelcomeDocSpaceStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 4;

    public ?string $nextStep = InfoDocSpaceStep::class;

    public Collection $docSpaces;

    public function mount(): void
    {
        $user = user();
        $user || abort(403);

        $mainPharmacy = $user->pharmacies->firstWhere('is_main', true);

        $mainPharmacy || abort(403);

        $this->docSpaces = $mainPharmacy->docSpaces;
    }

    protected function nextStepLabel(): string
    {
        return 'Datenraum aktivieren und anlegen';
    }

    public function submit(): void
    {
        if ($this->docSpaces->count() > 0) {
            $this->nextStep = SelectDocSpaceStep::class;
        }

        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
