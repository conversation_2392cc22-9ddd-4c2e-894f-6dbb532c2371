<?php

namespace App\Livewire\Retax\Onboarding;

use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;

class SuccessStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 5;

    public function prevStep(): ?string
    {
        return null;
    }

    public function nextStep(): ?string
    {
        return 'make button visible';
    }

    public function nextStepLabel(): string
    {
        return 'Retax öffnen';
    }

    public function submit(): void
    {
        $this->dispatch('open-new-tab', ['url' => config('services.retax.url')]);
        $this->redirect(route('dashboard'));
    }
}
