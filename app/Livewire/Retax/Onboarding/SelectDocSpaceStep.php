<?php

namespace App\Livewire\Retax\Onboarding;

use App\Attributes\Persist;
use App\Enums\SessionEnum;
use App\Enums\Settings\UserSettingTypes;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Rules\Ownership;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;
use App\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;

class SelectDocSpaceStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 4;

    public ?string $nextStep = SuccessStep::class;

    #[Persist]
    public ?int $selectedDocSpace;

    public User $user;

    public Pharmacy $pharmacy;

    public Collection $docSpaces;

    public function mount(): void
    {
        $user = user();
        $user || abort(403);

        $this->user = $user;

        $mainPharmacy = $user->pharmacies->firstWhere('is_main', true);

        $mainPharmacy || abort(403);

        $this->pharmacy = $mainPharmacy;
        $this->docSpaces = $mainPharmacy->docSpaces;
    }

    protected function nextStepLabel(): string
    {
        return 'Datenraum aktivieren und anlegen';
    }

    /**
     * @return array<string, array<int, Ownership|Exists|string>>
     */
    public function rules(): array
    {
        return [
            'selectedDocSpace' => ['required', Rule::exists('doc_spaces', 'id')],
        ];
    }

    /**
     * @return array<string, array<string, string>>
     */
    public function messages(): array
    {
        return [
            'selectedDocSpace' => [
                'required' => 'Sie müssen einen Datenraum auswählen um Retax einzurichten.',
            ],
        ];
    }

    public function submit(): void
    {
        $this->validate();

        try {
            DB::beginTransaction();
            $this->user->setGeneralSetting(UserSettingTypes::RETAX_ONBOARDING_FINISHED, true);

            $this->pharmacy->update([
                // 'uses_retax' => true, // TODO: {AP-2634}-retax-elastic-search needs to be merged
            ]);

            $this->pharmacy->docSpaces->where('id', $this->selectedDocSpace)->sole()->update([
                'used_by_retax' => true,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            throw $e;
        }

        $this->persistToSession();

        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
