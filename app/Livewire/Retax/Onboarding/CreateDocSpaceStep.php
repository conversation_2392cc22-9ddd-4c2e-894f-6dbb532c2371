<?php

namespace App\Livewire\Retax\Onboarding;

use App\Enums\DocSpaceCreateGuidedOptions;
use App\Enums\Sdr\GuidedDocspaceProcessStatus;
use App\Enums\SessionEnum;
use App\GuidedDocspaceCreationProcess;
use App\Jobs\CreateGuidedDocSpaceJob;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;
use Illuminate\Support\Collection;

class CreateDocSpaceStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public string $title = 'Retax einrichten';

    public ?int $stepNumber = 4;

    public ?string $nextStep = 'workaround';

    public Pharmacy $pharmacy;

    public Collection $processes;

    public function mount(): void
    {
        $user = user();
        $user || abort(403);

        $mainPharmacy = $user->pharmacies->firstWhere('is_main', true);
        $mainPharmacy || abort(403);

        $this->pharmacy = $mainPharmacy;
        $this->processes = collect();
    }

    protected function nextStepLabel(): string
    {
        return 'Datenräume anlegen';
    }

    public function submit(): void
    {
        if ($this->pharmacy->guidedDocspaceCreationProcesses()->where('status', GuidedDocspaceProcessStatus::COMPLETED)->exists()) {
            return;
        }

        $process = GuidedDocspaceCreationProcess::create([
            'pharmacy_id' => $this->pharmacy->id,
            'status' => GuidedDocspaceProcessStatus::PENDING,
            'requested_at' => now(),
        ]);
        $this->processes->add($process);

        $config = config('sdr.createGuided.options.'.DocSpaceCreateGuidedOptions::PHARMACEUTICAL_DATA_ONLY->value);
        CreateGuidedDocSpaceJob::dispatch($this->pharmacy, $config, $process);

        $this->nextStep = null;

    }

    public function refreshProcess(): void
    {
        $this->processes = $this->pharmacy->guidedDocspaceCreationProcesses;
        $this->nextStep = '';

        if ($this->processes->where('status', GuidedDocspaceProcessStatus::COMPLETED)->isNotEmpty()) {
            $this->goToNextStep();
        }
    }

    public function goToNextStep(): void
    {
        $this->nextStep = SelectDocSpaceStep::class;
        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }
}
