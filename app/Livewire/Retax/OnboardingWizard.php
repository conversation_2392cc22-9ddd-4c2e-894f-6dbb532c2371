<?php

namespace App\Livewire\Retax;

use App\Enums\SessionEnum;
use App\Livewire\Retax\Onboarding\CheckPharmacyInformationStep;
use App\Livewire\Retax\Onboarding\CreateDocSpaceStep;
use App\Livewire\Retax\Onboarding\InfoDocSpaceStep;
use App\Livewire\Retax\Onboarding\SelectDocSpaceStep;
use App\Livewire\Retax\Onboarding\SelectPharmacyStep;
use App\Livewire\Retax\Onboarding\SuccessStep;
use App\Livewire\Retax\Onboarding\UnlockDocSpaceStep;
use App\Livewire\Retax\Onboarding\WelcomeDocSpaceStep;
use App\Livewire\Retax\Onboarding\WelcomeStep;
use App\Livewire\Wizard\Wizard;

class OnboardingWizard extends Wizard
{
    public string $currentStep = WelcomeStep::class;

    public int $totalSteps = 5;

    protected array $steps = [
        WelcomeStep::class,
        SelectPharmacyStep::class,
        CheckPharmacyInformationStep::class,
        WelcomeDocSpaceStep::class,
        InfoDocSpaceStep::class,
        UnlockDocSpaceStep::class,
        CreateDocSpaceStep::class,
        SelectDocSpaceStep::class,
        SuccessStep::class,
    ];

    public function before(): void
    {
        session()->forget(SessionEnum::RETAX_ONBOARDING->value);
    }

    public function onClose(): void
    {
        session()->forget(SessionEnum::RETAX_ONBOARDING->value);

        $this->currentStep = WelcomeStep::class;
    }
}
