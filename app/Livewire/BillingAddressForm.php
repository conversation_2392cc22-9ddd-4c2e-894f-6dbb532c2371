<?php

namespace App\Livewire;

use App\Actions\BillingAddresses\CreateBillingAddress;
use App\BillingAddress;
use App\Enums\FlashMessageTypeEnum;
use Exception;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class BillingAddressForm extends Component
{
    use AuthorizesRequests;

    public $company;

    public $first_name;

    public $last_name;

    public $email;

    public $optional_address_line;

    public $street;

    public $house_number;

    public $postal_code;

    public $city;

    public $state;

    public $phone;

    public $billingAddress;

    public $successUrl;

    public $pharmacies = [];

    public function mount($billingAddress = null)
    {
        $this->successUrl = url()->previous();

        if ($billingAddress) {
            $this->billingAddress = $billingAddress;
            $this->company = $billingAddress->company;
            $this->first_name = $billingAddress->first_name;
            $this->last_name = $billingAddress->last_name;
            $this->email = $billingAddress->email;
            $this->optional_address_line = $billingAddress->optional_address_line;
            $this->street = $billingAddress->street;
            $this->house_number = $billingAddress->house_number;
            $this->postal_code = $billingAddress->postal_code;
            $this->city = $billingAddress->city;
            $this->state = $billingAddress->state;
            $this->phone = $billingAddress->phone;
            $this->pharmacies = $billingAddress->pharmacies->pluck('id')->toArray();
        }
    }

    public function rules()
    {
        return CreateBillingAddress::getValidationRules();
    }

    public function togglePharmacy(int $pharmacyId): void
    {
        if (in_array($pharmacyId, $this->pharmacies, false)) {
            $this->pharmacies = array_diff($this->pharmacies, [$pharmacyId]);
        } else {
            $this->pharmacies[] = $pharmacyId;
        }
    }

    public function save()
    {
        $this->validate();

        $data = [
            'user_id' => user()->id,
            'company' => $this->company,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'optional_address_line' => $this->optional_address_line,
            'street' => $this->street,
            'house_number' => $this->house_number,
            'postal_code' => $this->postal_code,
            'city' => $this->city,
            'state' => $this->state,
            'phone' => $this->phone,
        ];

        $oldBillingAddress = null;
        if ($this->billingAddress) {
            $this->authorize('update', $this->billingAddress);

            if ($this->billingAddress->pharmacies->every(fn ($pharmacy) => user()?->isOwnerOrSubOwnerOfPharmacy($pharmacy) ?? false)) {
                $this->billingAddress->update($data);
            } else {
                $oldBillingAddress = $this->billingAddress;
                $this->billingAddress = (new CreateBillingAddress)->fromArray($data);
            }
        } else {
            $this->authorize('create', BillingAddress::class);

            $this->billingAddress = (new CreateBillingAddress)->fromArray($data);
        }
        foreach ($this->pharmacies as $pharmacyId) {
            $pharmacies = user()->pharmacies;
            $pharmacies->where('id', $pharmacyId)->flatMap->update(['billing_address_id' => $this->billingAddress->id]);
        }
        if ($oldBillingAddress && $oldBillingAddress->isDeletable()) {
            $oldBillingAddress->delete();
        }

        return redirect($this->successUrl);
    }

    public function render()
    {
        return view('livewire.billing-address-create');
    }

    public function exception(Exception $e, callable $stopPropagation): void
    {
        if ($e instanceof ValidationException) {
            return;
        }
        $stopPropagation();
        report($e);
        notify('Es ist ein Fehler aufgetreten. Unsere Techniker wurden informiert. Versuchen Sie es später erneut.', FlashMessageTypeEnum::ERROR->value, seconds: 10);
        $this->redirect(route('users.billing-addresses'));
    }
}
