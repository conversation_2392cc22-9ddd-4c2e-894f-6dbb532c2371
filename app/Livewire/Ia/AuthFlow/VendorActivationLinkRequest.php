<?php

namespace App\Livewire\Ia\AuthFlow;

use App\Helper\IaHelper;
use App\Livewire\Wizard\WizardStep;
use App\Mail\VendorActivationLinkRequestMail;
use App\Traits\InteractsWithSession;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Mail;

class VendorActivationLinkRequest extends WizardStep
{
    use InteractsWithSession;

    public string $title = 'Shop-Backend umziehen';

    public ?string $subtitle = 'Shop-Backend von Ihrem Vendor ins GEDISA ApothekenPortal umziehen.';

    public ?string $nextStep = VendorActivationLinkRequestConfirmation::class;

    public ?string $prevStep = Terms::class;

    public string $nextStepLabel = 'Aktivierungslink anfordern';

    public function submit(): void
    {
        $pharmacy = currentPharmacy();
        $user = user();

        if (! $pharmacy || ! $user) {
            notify('Es ist ein Fehler aufgetreten.', 'error');

            return;
        }

        $pharmacyAddress = $pharmacy->address;

        Mail::to('<EMAIL>')
            ->queue(new VendorActivationLinkRequestMail($user, $pharmacy, $pharmacyAddress));

        notify('Ihre Anfrage wurde gesendet.');

        $this->next();
    }

    protected function sessionPrefix(): ?string
    {
        return IaHelper::wizardSessionPrefix();
    }

    public function render(): View
    {
        return view('livewire.ia.auth-flow.vendor-activation-link-request');
    }
}
