<?php

namespace App\Livewire\Pharmacy\Components;

use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class EditUser extends Component
{
    public ?Pharmacy $pharmacy;

    public ?User $user = null;

    public array $roles = [];

    public array $permissions = [];

    public ?string $currentRole;

    public array $oldPermissions = [];

    public $salutation = null;

    public $title = null;

    public $first_name = null;

    public $last_name = null;

    public $login_email = null;

    public $notifications_email = null;

    public $phone = null;

    public $differing_notifications_email_enabled = false;

    public $selectedPermissions = [];

    public bool $see_choose_apomail = false;

    public bool $choose_apomail_enabled = false;

    public $apomails = [];

    public array $selectConfig = [];

    private $oldSelectedPermissions = [];

    public mixed $weekly_working_hours = null;

    public function mount(Pharmacy $pharmacy, ?User $user): void
    {
        if ($user && $user->exists) {
            $this->user = $user;
            $this->currentRole = old('role', $user->getPharmacyRole($pharmacy));
            $this->oldPermissions = $user->getPharmacyPermissions($pharmacy) ?? [];
        } else {
            $this->currentRole = old('role', PharmacyRoleEnum::EMPLOYEE);
            $this->oldPermissions = [];

            $this->apomails = $pharmacy->owner()->apomails()->active()->unused()->get()
                ->mapWithKeys(fn ($item) => [$item->email => $item->email]);

            if ($this->apomails->count() > 0) {
                $this->apomails->prepend('Bitte auswählen');
                $this->see_choose_apomail = $pharmacy->uses_apomail;
                $this->choose_apomail_enabled = old('choose_apomail_enabled', false);
            }
        }

        $this->salutation = old('salutation', $user->id ? $user->salutation : SalutationEnum::MS);
        $this->title = old('title', $user->id ? $user->title : '');
        $this->first_name = old('first_name', $user->id ? $user->first_name : '');
        $this->last_name = old('last_name', $user->id ? $user->last_name : '');
        $this->login_email = old('login_email');
        $this->notifications_email = old('notifications_email', $user->id ? $user->email : '');
        $this->differing_notifications_email_enabled = (bool) old('differing_notifications_email_enabled');
        $this->phone = old('phone', $user->id ? $user->phone : '');
        $this->weekly_working_hours = old('weekly_working_hours', $user?->weekly_working_hours);
        $this->selectedPermissions = old('permissions', $user->id ? ($user->getPharmacyPermissions($pharmacy) ?? []) : []);

        $this->pharmacy = $pharmacy;
        $this->roles = PharmacyRoleEnum::getAssignable($this->pharmacy);
        $this->permissions = PharmacyPermissionsEnum::getAssignableLabels($this->pharmacy, $this->currentRole);
    }

    public function updatingSelectedPermissions(): void
    {
        $this->oldSelectedPermissions = $this->selectedPermissions;
    }

    public function updatedSelectedPermissions(): void
    {
        if ($this->selectedPermissions === false) {
            return;
        }

        if (! is_array($this->oldSelectedPermissions)) {
            return;
        }

        $this->handlePermissionChange(PharmacyPermissionsEnum::CALENDAR, PharmacyPermissionsEnum::CALENDAR_ADMIN, $this->selectedPermissions);
        $this->handlePermissionChange(PharmacyPermissionsEnum::TELEPHARMACY, PharmacyPermissionsEnum::TELEPHARMACY_ADMIN, $this->selectedPermissions);
    }

    public function render(): View
    {
        return view('livewire.pharmacy.components.edit-user');
    }

    public function updatedCurrentRole(): void
    {
        $this->permissions = PharmacyPermissionsEnum::getAssignableLabels($this->pharmacy, $this->currentRole);
    }

    public function updatedChooseApomailEnabled(): void
    {
        $this->login_email = null;
    }

    public function updatingDifferingNotificationsEmailEnabled(): void
    {
        $this->notifications_email = $this->differing_notifications_email_enabled ? $this->notifications_email : null;
    }

    public function handlePermissionChange($permission, $adminPermission, $val): void
    {
        if (in_array($permission, $this->oldSelectedPermissions) && in_array($adminPermission, $val)) {
            if (($key = array_search($permission, $this->oldSelectedPermissions)) !== false) {
                unset($this->selectedPermissions[$key]);
                $this->selectedPermissions = array_values($this->selectedPermissions);
            }
        }

        if (in_array($adminPermission, $this->oldSelectedPermissions) && in_array($permission, $val)) {
            if (($key = array_search($adminPermission, $this->oldSelectedPermissions)) !== false) {
                unset($this->selectedPermissions[$key]);
                $this->selectedPermissions = array_values($this->selectedPermissions);
            }
        }
    }
}
