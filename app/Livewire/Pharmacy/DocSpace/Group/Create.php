<?php

namespace App\Livewire\Pharmacy\DocSpace\Group;

use App\Actions\DocSpaces\CreateGroupAction;
use App\Actions\DocSpaces\UpdateGroupAction;
use App\DocSpaceGroup;
use App\Enums\Sdr\NameLengthEnum;
use App\Exceptions\DocSpace\RiseSDRValidationException;
use App\Helper\RiseSDRApi;
use App\Pharmacy;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\On;
use Livewire\Component;

class Create extends Component
{
    public $docSpaceGroupEditMode = false;

    public bool $modalOpen = false;

    public Pharmacy $pharmacy;

    public string $name;

    public array $selectedUsers;

    public int $groupId;

    protected $messages = [
        'name.not_regex' => 'Das technische Kürzel wird automatisch hinten angestellt. Sie können es nicht selbst im Namen verwenden.',
    ];

    public function rules(): array
    {
        $suffixLength = Str::length(RiseSDRApi::formatSuffix($this->pharmacy->suffix));
        $uniqueNameMaxLength = NameLengthEnum::GROUP->value;

        return [
            'pharmacy' => ['required'],
            'name' => ['required', 'string', 'max:'.$uniqueNameMaxLength - $suffixLength, 'min:2', 'not_regex:/'.$this->pharmacy->getPharmacyIdBase64().'/i'],
            'selectedUsers' => ['required', 'array'],
            'selectedUsers.*.id' => ['nullable', 'integer', Rule::in($this->pharmacy->users->pluck('id'))],
        ];
    }

    public function mount(Pharmacy $pharmacy): void
    {
        $this->pharmacy = $pharmacy;
        $this->name = $pharmacy->upperShortName.' ';
        $this->selectedUsers = $this->preselectPharmacyOwner();
    }

    private function preselectPharmacyOwner(): array
    {
        $selectedUsers = [];

        foreach ($this->pharmacy->users as $user) {
            $selectedUsers[$user->id] = $user->id === $this->pharmacy->owner()->id;
        }

        return $selectedUsers;
    }

    private function preselectCurrentGroupUsers(int $id): array
    {
        $group = $this->pharmacy->docSpaceGroups()->findOrFail($id);

        return array_fill_keys($group->users->pluck('id')->toArray(), true);
    }

    #[On('open-doc-space-create-group-modal')]
    public function open(?int $id = null): void
    {
        if ($id !== null) {
            $this->groupId = $id;

            $group = $this->pharmacy->docSpaceGroups()->findOrFail($id);
            $this->name = $group->name;

            $this->selectedUsers = $this->preselectCurrentGroupUsers($id);

            $this->docSpaceGroupEditMode = true;
        } else {
            $this->name = $this->pharmacy->upperShortName.' ';
            $this->selectedUsers = $this->preselectPharmacyOwner();

            $this->docSpaceGroupEditMode = false;
        }

        $this->modalOpen = true;

        $this->resetErrorBag();
        $this->resetValidation();

        $this->openModal('doc-space-create-group-modal');
    }

    public function close(): void
    {
        $this->modalOpen = false;
        $this->resetErrorBag();
        $this->resetValidation();

        $this->closeModal('doc-space-create-group-modal');
    }

    public function create()
    {
        if (! $this->validateAndSetErrorMessages()) {
            return false;
        }

        try {
            $action = app(CreateGroupAction::class, [
                'pharmacy' => $this->pharmacy,
                'name' => $this->name,
                'selectedUsers' => $this->selectedUsers,
            ])->execute();

            if (count($action->failedAssignments) > 0) {
                $this->notify('Gruppe wurde erfolgreich angelegt. Einige Benutzer konnten nicht hinzugefügt werden.', 'warning');
            } else {
                $this->notify('Gruppe wurde erfolgreich angelegt.');
            }
        } catch (\Exception $e) {

            if ($e instanceof RiseSDRValidationException) {
                foreach ($e->validator->getMessageBag()->getMessages() as $field => $message) {
                    $this->addError($field, $message);
                }
            } else {
                $this->addError('general', __('errors.general_api_error'));
            }

            report($e);

            return false;
        }

        $this->dispatch('update-groups');
        $this->close();
    }

    public function update()
    {
        if (! $this->validateAndSetErrorMessages()) {
            return false;
        }

        $group = DocSpaceGroup::findOrFail($this->groupId);

        try {
            app(UpdateGroupAction::class, [
                'pharmacy' => $this->pharmacy,
                'group' => $group,
                'name' => $this->name,
                'selectedUsers' => $this->selectedUsers,
            ])->execute();
        } catch (\Exception $e) {
            $this->addError('general', __('errors.general_api_error'));
            report($e);

            return false;
        }

        $this->notify('Gruppe wurde erfolgreich bearbeitet.');
        $this->dispatch('update-groups');
        $this->close();
    }

    private function validateAndSetErrorMessages(): bool
    {
        try {
            $this->resetErrorBag();
            $this->validate();
        } catch (ValidationException $e) {
            $this->setErrorMessages($e->errors());

            return false;
        }

        return true;
    }

    private function setErrorMessages($errors): void
    {
        foreach ($errors as $key => $value) {
            $this->addError($key, $value);
        }
    }

    public function render()
    {
        return view('livewire.pharmacy.doc-space.group.create');
    }
}
