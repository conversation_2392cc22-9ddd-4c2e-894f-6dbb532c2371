<?php

namespace App\Livewire\Association\Components;

use App\Association;
use App\HealthInsuranceCompany;
use Livewire\Component;
use Livewire\WithPagination;

class HealthInsuranceCompanyList extends Component
{
    use WithPagination;

    public Association $association;

    public $show = 'all';

    public function render()
    {
        return view('livewire.association.components.health-insurance-company-list', [
            'association' => $this->association,
            'healthInsuranceCompanies' => HealthInsuranceCompany::query()
                ->with('associations')
                ->where('vaccinate_enabled', true)
                ->when($this->show !== 'all', fn ($query) => $this->show == 'connected' ? $query->whereHas('associations', fn ($query) => $query->where('associations.id', '=', $this->association->id)) : $query->whereDoesntHave('associations', fn ($query) => $query->where('associations.id', '=', $this->association->id)))
                ->orderBy('name', 'asc')
                ->get(),
        ]);
    }

    public function toggleHealthInsuranceCompany(HealthInsuranceCompany $healthInsuranceCompany)
    {
        abort_unless(user()->can('administrateHealthInsuranceCompanies', $this->association), 403);
        abort_unless($healthInsuranceCompany->vaccinate_enabled, 404);

        $this->association->healthInsuranceCompanies()->toggle($healthInsuranceCompany);
    }
}
