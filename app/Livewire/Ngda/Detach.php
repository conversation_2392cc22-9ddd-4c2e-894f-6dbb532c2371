<?php

namespace App\Livewire\Ngda;

use App\Pharmacy;
use App\Processes\DetachNgdaFromPharmacy;
use App\Processes\Payloads\DetachNgdaFromPharmacyPayload;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Livewire\Component;
use Livewire\Features\SupportRedirects\Redirector;

class Detach extends Component
{
    public function render(): View
    {
        return view('livewire.ngda.detach');
    }

    public function detach(): Redirector|RedirectResponse
    {
        $pharmacy = currentPharmacy();
        assert($pharmacy instanceof Pharmacy);

        $payload = new DetachNgdaFromPharmacyPayload($pharmacy);
        $process = new DetachNgdaFromPharmacy;
        $process->run($payload);

        return redirect()->route('pharmacies.edit', $pharmacy);
    }
}
