<?php

namespace App\Livewire\Ngda\WizardSteps;

use App\Livewire\Wizard\WizardStep;

class ForeignAccountStep extends WizardStep
{
    public string $title = 'Fremde N-ID';

    public ?string $subtitle = 'Sie haben versucht eine fremde N-ID zu koppeln.';

    public ?int $stepNumber = 2;

    public int $totalSteps = 2;

    /**
     * @var string[]
     */
    protected $listeners = ['linking-nid-wizard-modal-closed' => 'closeModal'];

    public function closeModal(): void
    {
        $this->redirect(route('pharmacies.edit', ['pharmacy' => currentPharmacy()]));
    }
}
