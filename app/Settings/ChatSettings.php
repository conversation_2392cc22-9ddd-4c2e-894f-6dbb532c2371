<?php

namespace App\Settings;

use App\Enums\SystemSettings\SystemSettingGroupEnum;
use App\Features\WebChatLatestVersion;
use <PERSON><PERSON>\LaravelSettings\Settings;

class ChatSettings extends Settings implements HasPurgeablesInterface
{
    /**
     * @var array<int>
     */
    public array $use_latest_paths = [];

    public static function group(): string
    {
        return SystemSettingGroupEnum::CHAT->value;
    }

    public function purgeables(): array
    {
        return [WebChatLatestVersion::class];
    }
}
