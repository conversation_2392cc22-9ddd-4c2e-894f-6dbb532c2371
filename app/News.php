<?php

namespace App;

use App\Enums\NewsStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Class News
 *
 * @mixin IdeHelperNews
 */
class News extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'release_date' => 'datetime',
        'with_login_wall' => 'boolean',
        'visible_intern' => 'boolean',
        'visible_extern' => 'boolean',
    ];

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('masonry')
            ->width(650)
            ->sharpen(10)
            ->performOnCollections('header');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('header')
            ->singleFile()
            ->withResponsiveImages();
    }

    /** RELATIONS */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class)->withTimestamps();
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(Author::class);
    }

    /** SCOPES */
    public function scopeReleased(Builder $query)
    {
        return $query
            ->where('release_date', '<=', Carbon::now())
            ->where('status', NewsStatusEnum::PUBLISHED);
    }

    public function scopeVisibility(Builder $query, $type = null)
    {
        if ($type) {
            $query->where($type == 'blog' ? 'visible_intern' : 'visible_extern', true);
        }

        return $query;
    }

    public function scopePublic(Builder $query)
    {
        return $query
            ->where('release_date', '<=', Carbon::now())
            ->where('status', NewsStatusEnum::PUBLISHED)
            ->where('visible_extern', '=', 1);
    }

    /** METHODS */
    public function isReleased()
    {
        if (now() < $this->release_date) {
            return false;
        }

        if (in_array($this->status, [NewsStatusEnum::PUBLISHED])) {
            return true;
        }

        return false;
    }

    public function shouldDisplayLoginWall()
    {
        return ! user() && $this->with_login_wall === true;
    }
}
