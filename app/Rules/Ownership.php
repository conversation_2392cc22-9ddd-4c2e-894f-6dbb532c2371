<?php

namespace App\Rules;

use App\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class Ownership implements ValidationRule
{
    public function __construct(
        protected User $user,
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->user->ownedPharmacies()->whereKey($value)->doesntExist()) {
            $fail('Diese Apotheke hat einen anderen Inhaber.');
        }
    }
}
