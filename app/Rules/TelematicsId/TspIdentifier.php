<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;

class TspIdentifier implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (is_string($value) === false) {
            return false;
        }

        $tspIdentifier = match (str_contains($value, '.')) {
            true => TelematicsIdHelper::explode($value)['tspIdentifier'],
            default => $value,
        };

        if ($tspIdentifier === null) {
            return true;
        }

        return in_array($tspIdentifier, TelematicsId::ALLOWED_TSP_IDENTIFIERS);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
