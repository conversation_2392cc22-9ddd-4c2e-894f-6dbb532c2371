<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Str;

class MemberId implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (is_string($value) === false) {
            return false;
        }

        $value = Str::of($value);

        $memberId = match (str_contains($value, '.')) {
            true => TelematicsIdHelper::explode($value)['memberId'],
            default => $value,
        };

        $chars = str_split($memberId);
        foreach ($chars as $char) {
            if (! in_array($char, TelematicsId::ALLOWED_MEMBER_ID_SYMBOLS)) {
                return false;
            }
        }

        return mb_strlen($memberId) >= 1 && mb_strlen($memberId) <= 10;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
