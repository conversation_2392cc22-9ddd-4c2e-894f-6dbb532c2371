<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Str;

class SectoralMark implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (is_string($value) === false) {
            return false;
        }

        $value = Str::of($value);

        $sectoralMark = match (str_contains($value, '.')) {
            true => TelematicsIdHelper::explode($value)['sectoralMark'],
            default => $value,
        };

        return in_array($sectoralMark, TelematicsId::ALLOWED_SECTORAL_MARKS);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
