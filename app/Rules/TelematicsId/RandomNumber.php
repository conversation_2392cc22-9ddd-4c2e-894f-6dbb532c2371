<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use Illuminate\Contracts\Validation\Rule;

class RandomNumber implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (is_string($value) === false) {
            return false;
        }

        $randomNumber = match (str_contains($value, '.')) {
            true => TelematicsIdHelper::explode($value)['randomNumber'],
            default => $value,
        };

        return mb_strlen($randomNumber) === 3
            && is_numeric($randomNumber)
            && ! str_contains($randomNumber, '.');
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
