<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use Illuminate\Contracts\Validation\Rule;

class RandomNumber implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        switch ($attribute) {
            case 'telematics_id':
                $randomNumber = TelematicsIdHelper::explode($value)['randomNumber'];

                break;

            case 'random_number':
                $randomNumber = $value;

                break;

            case 'telematicsId.random_number':
                $randomNumber = $value;

                break;

            default:
                return false;
        }

        return mb_strlen($randomNumber) === 3
            && is_numeric($randomNumber)
            && ! str_contains($randomNumber, '.');
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
