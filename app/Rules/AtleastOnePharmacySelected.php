<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Arr;

class AtleastOnePharmacySelected implements ValidationRule
{
    public function __construct(
        protected string $key = 'id',
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // @phpstan-ignore-next-line
        $pharmacies = collect($value)->filter(fn (array $item) => Arr::get($item, $this->key));

        if ($pharmacies->isEmpty()) {
            $fail('Es muss mindestens eine Apotheke ausgewählt werden.');
        }
    }
}
