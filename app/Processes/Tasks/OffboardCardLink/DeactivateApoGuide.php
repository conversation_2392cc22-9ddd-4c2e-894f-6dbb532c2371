<?php

namespace App\Processes\Tasks\OffboardCardLink;

use App\Jobs\DeactivateApoGuideCardLinkVendor;
use App\Processes\Payloads\OffboardCardLinkPayload;

class DeactivateApoGuide
{
    public function __invoke(OffboardCardLinkPayload $payload, \Closure $next): OffboardCardLinkPayload
    {
        if ($payload->pharmacy->cardLinkOrder) {
            DeactivateApoGuideCardLinkVendor::dispatchSync($payload->pharmacy->cardLinkOrder);
        }

        return $next($payload);
    }
}
