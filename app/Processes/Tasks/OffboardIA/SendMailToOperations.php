<?php

namespace App\Processes\Tasks\OffboardIA;

use App\Mail\OffboardIAMail;
use App\Processes\Payloads\OffboardIAPayload;
use App\Settings\AppSettings;
use Illuminate\Support\Facades\Mail;

class SendMailToOperations
{
    public function __invoke(OffboardIAPayload $payload, \Closure $next): OffboardIAPayload
    {
        Mail::to(app(AppSettings::class)->operations_email_address)->queue(new OffboardIAMail($payload->pharmacy, $payload->integration));

        return $next($payload);
    }
}
