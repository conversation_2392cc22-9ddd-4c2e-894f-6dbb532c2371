<?php

namespace App\Processes\Tasks\CancelKimAddress;

use App\Mail\KimAddressCancelledMail;
use App\Processes\Payloads\CancelKimAddressPayload;
use Closure;
use Illuminate\Support\Facades\Mail;

class SendMail
{
    public function __invoke(CancelKimAddressPayload $payload, Closure $next): CancelKimAddressPayload
    {
        if ($payload->kimAddress->pharmacy) {
            Mail::to($payload->kimAddress->owner())->queue(new KimAddressCancelledMail($payload->customerName, $payload->kimAddressEmail));
        }

        return $next($payload);
    }
}
