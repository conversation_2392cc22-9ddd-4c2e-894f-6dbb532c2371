<?php

namespace App;

use App\Enums\ApomailStatus;
use App\Enums\AssociationEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Enums\Settings\UserSettingTypes;
use App\Mail\UserChangeEmailMail;
use App\Settings\TermsOfServiceSettings;
use App\Support\FamedlyApi;
use App\Traits\AssociationRules;
use App\Traits\HasGeneralSettings;
use App\Traits\InteractsWithBetaPhases;
use App\Traits\InteractsWithIaPreflightPhase;
use App\Traits\NotifiableTrait;
use App\Traits\NotificationClosable;
use App\Traits\PharmacyRules;
use Exception;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Laravel\Nova\Actions\Actionable;

/**
 * Class User
 *
 * @mixin IdeHelperUser
 *
 * @property ?string $weekly_working_hours
 */
class User extends Authenticatable implements MustVerifyEmail
{
    use Actionable,
        AssociationRules,
        HasFactory,
        HasGeneralSettings,
        InteractsWithBetaPhases,
        InteractsWithIaPreflightPhase,
        NotifiableTrait,
        NotificationClosable,
        PharmacyRules,
        SoftDeletes;

    protected $with = ['brochureCode'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'salutation',
        'title',
        'first_name',
        'last_name',
        'email',
        'idp_email',
        'password',
        'phone',
        'username',
        'email_verified_at',
        'password_status',
        'updated_at',
        'failed_login_attempts',
        'organisation_uuid',
        'mailcoach_id',
        'is_beta_tester',
        'sdr_user_id',
        'is_at_idp',
        'association_news_active',
        'matrix_bootstrap_done',
        'weekly_working_minutes',
        'subscription_service_id',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login' => 'datetime',
        'is_beta_tester' => 'boolean',
        'matrix_bootstrap_done' => 'boolean',
        'association_news_active' => 'boolean',
        'first_login' => 'datetime',
    ];

    public function routeNotificationForMail($notification): ?string
    {
        return $this->getRouteEmailsToAttribute();
    }

    // <editor-fold desc="Relations">
    public function pharmacies(): BelongsToMany
    {
        return $this
            ->belongsToMany(Pharmacy::class, 'pharmacy_role_user')
            ->using(PharmacyRoleUser::class)
            ->withPivot(['role_name', 'permissions'])
            ->withTimestamps()
            ->orderBy('name');
    }

    public function getPharmaciesWhichDidNotAcceptNewTermsOfService(): Collection
    {
        return $this
            ->pharmacies
            ->filter(fn (Pharmacy $pharmacy) => $pharmacy->hasAcceptedTermsBefore(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since));
    }

    public function ownedPharmacies(): BelongsToMany
    {
        return $this
            ->pharmacies()
            ->wherePivot('role_name', '=', PharmacyRoleEnum::OWNER);
    }

    public function associations(): BelongsToMany
    {
        return $this
            ->belongsToMany(Association::class, 'association_role_user')
            ->using(AssociationRoleUser::class)
            ->withPivot(['role_name', 'permissions'])
            ->withTimestamps()
            ->orderBy('name');
    }

    public function pharmacyProfile(): HasOne
    {
        return $this->hasOne(UserPharmacyProfile::class);
    }

    public function associationProfile(): HasOne
    {
        return $this->hasOne(UserAssociationProfile::class);
    }

    public function associationMembershipHistories(): HasMany
    {
        return $this->hasMany(AssociationMembershipHistory::class);
    }

    public function currentAssociationMembershipHistory(): HasOne
    {
        return $this->hasOne(AssociationMembershipHistory::class)->whereNull('canceled_at')->latest();
    }

    public function vaccinations(): HasMany
    {
        return $this->hasMany(Vaccination::class);
    }

    public function brochureCode(): HasOne
    {
        return $this->hasOne(BrochureCode::class);
    }

    /**
     * @deprecated this only returns orders that the user did the actual order on. If you want
     * to get all orders that the user is related to, collect all orders via the `pharmacies` relation instead.
     */
    public function cardLinkOrders(): HasMany
    {
        return $this->hasMany(CardLinkOrder::class);
    }

    public function companyUser(): HasOne
    {
        return $this->hasOne(CompanyUser::class);
    }

    public function companyUserProfiles(): HasMany
    {
        return $this->hasMany(UserPharmacyProfile::class, 'company_user_id');
    }

    public function apomails(): HasMany
    {
        return $this->hasMany(Apomail::class, 'owner_id');
    }

    public function usingApomails(): BelongsToMany
    {
        return $this->belongsToMany(Apomail::class, 'apomail_user');
    }

    public function associationMembershipChanges(): HasMany
    {
        return $this->hasMany(AssociationMembershipChange::class);
    }

    public function currentAssociationMembershipChange(): HasOne
    {
        return $this->hasOne(AssociationMembershipChange::class)->whereNull(['change_done_at', 'canceled_at']);
    }

    public function invoices(): HasManyThrough
    {
        return $this->hasManyThrough(
            Invoice::class,
            BillingAddress::class,
            'user_id',
            'billing_address_id',
            'id',
            'id'
        );
    }

    public function billingAddresses(): HasMany
    {
        return $this->hasMany(BillingAddress::class);
    }

    public function closedNotifications(): HasMany
    {
        return $this->hasMany(ClosedNotification::class);
    }

    public function shiftPlans(): HasMany
    {
        return $this->hasMany(ShiftPlan::class, 'owner_id');
    }
    // </editor-fold>

    // <editor-fold desc="Scopes">

    public function scopeVerified($builder)
    {
        return $builder->whereNotNull('email_verified_at');
    }

    public function scopeForAssociation($builder, Association $association)
    {
        return $builder->whereHas('pharmacyProfile', fn ($query) => $query->where('association_id', $association->id));
    }

    public function scopeOnlyOwner(Builder $builder)
    {
        return $builder->whereHas('brochureCode');
    }
    // </editor-fold>

    // <editor-fold desc="Tokens">
    public function injectToken($token)
    {
        $this->token = $token;
    }

    public function getAccessToken()
    {
        return $this->token;
    }

    public function getAuthIdentifierName(): string
    {
        return 'sub';
    }

    public function getAuthIdentifier()
    {
        return $this->id;
    }

    public function getAuthPassword()
    {
        throw new \LogicException('Not applicable for OIDC auth.');
    }

    public function getRememberToken(): array
    {
        return [];
    }

    public function setRememberToken($value)
    {
        throw new \LogicException('Not implemented.');
    }

    public function getRememberTokenName()
    {
        throw new \LogicException('Not implemented.');
    }
    // </editor-fold>

    // <editor-fold desc="Accessors">
    public function getFullNameAttribute(): string
    {
        return trim($this->title.' '.$this->first_name.' '.$this->last_name);
    }

    public function getNameAttribute(): string
    {
        if ($this->isCompany()) {
            return $this->companyUser->name;
        }

        return $this->full_name;
    }

    public function getNovaNameAttribute(): string
    {
        return $this->companyUser ? $this->companyUser->name.' ('.$this->full_name.')' : $this->full_name;
    }

    public function getShortNameAttribute(): string
    {
        if ($this->isCompany()) {
            return $this->companyUser->name;
        }

        return trim($this->first_name.' '.$this->last_name);
    }

    public function getGreetingAttribute(): string
    {
        if ($this->isCompany()) {
            return 'Sehr geehrte Damen und Herren';
        }

        if ($this->salutation == SalutationEnum::MR) {
            return 'Sehr geehrter Herr '.trim($this->title.' '.$this->last_name);
        }

        return 'Sehr geehrte Frau '.trim($this->title.' '.$this->last_name);
    }

    public function getRouteEmailsToAttribute(): ?string
    {
        if ($this->email) {
            return $this->email;
        } else {
            /** @var Pharmacy|null $pharmacy */
            $pharmacy = $this->pharmacies->first();
            if (! $pharmacy) {
                return null;
            }

            return $pharmacy->email;
        }
    }

    public function getAssociationIdAttribute()
    {
        return $this->association() ? $this->association()->id : null;
    }

    public function getIsBetaTesterAttribute()
    {
        return $this->attributes['is_beta_tester'] ?? false;
    }

    public function association()
    {
        if ($this->isPharmacyUser()) {
            return $this->pharmacyProfile->association;
        }
        if ($this->isAssociationUser()) {
            return $this->associations->first();
        }

        return null;
    }

    public function hasPharmacies(): bool
    {
        return $this->pharmacies->count() > 0;
    }

    public function isCompany(): bool
    {
        return $this->companyUser !== null;
    }

    public function isPharmacyUser(): bool
    {
        return $this->pharmacyProfile !== null;
    }

    public function isAssociationUser(): bool
    {
        return $this->associationProfile !== null;
    }

    public function isOwner(): bool
    {
        return $this->isPharmacyUser() && $this->brochureCode;
    }

    public function isOwnerOfPharmacy(?Pharmacy $pharmacy = null): bool
    {
        if ($pharmacy === null) {
            $pharmacy = currentPharmacy();
        }

        return $this->isOwner() && $this->id === $pharmacy?->owner()?->id;
    }

    public function isSubOwner(): bool
    {
        return $this->isPharmacyUser() && $this->pharmacies()->wherePivot(
            'role_name',
            PharmacyRoleEnum::SUB_OWNER
        )->exists();
    }

    public function isOwnerOrSubOwner(): bool
    {
        return $this->isOwner() || $this->isSubOwner();
    }

    public function belongsToAssociation(Association|int|null $association = null)
    {
        if ($this->isOwner()) {
            return $this->pharmacyProfile->association_id == ($association instanceof Association ? $association->id : $association);
        }

        $owner = $this->pharmacies->first()->owner();

        return $owner->pharmacyProfile->association_id == ($association instanceof Association ? $association->id : $association);
    }

    public function belongsToAnyAssociation(?array $associationIds = null): bool
    {
        if ($this->isOwner()) {
            return empty($associationIds) ? (bool) $this->pharmacyProfile->association_id : in_array(
                ($this->pharmacyProfile->association_id ?? 'null'),
                $associationIds
            );
        }

        $owner = $this->pharmacies->first()->owner();

        return empty($associationIds) ? (bool) $owner->pharmacyProfile->association_id : in_array(
            ($owner->pharmacyProfile->association_id ?? 'null'),
            $associationIds
        );
    }

    public function emailVerified(): bool
    {
        return $this->email_verified_at !== null;
    }

    public function sendEmailChangeEmail($newEmail): void
    {
        $verificationTimeInSeconds = config('auth.verification.expire') * 60;
        $email = new UserChangeEmailMail($this, $newEmail);

        Cache::forget('emailChanges.email.'.$this->email);
        Cache::forget('emailChanges.user'.$this->id);
        Cache::forget('emailChanges.user.'.$this->id.'url');

        Cache::put('emailChanges.email.'.$newEmail, $this->id, $verificationTimeInSeconds);
        Cache::put('emailChanges.user.'.$this->id, $newEmail, $verificationTimeInSeconds);

        Mail::to($newEmail)->send($email);

        Cache::put(
            'emailChanges.user.'.$this->id.'.url',
            sha1($email->actionUrl),
            $verificationTimeInSeconds
        );
    }

    /**
     * @deprecated
     */
    public function needsToPayForPortal(): bool
    {
        if (! $this->isPharmacyUser()) {
            return false;
        }

        return ! $this->association() ||
            $this->association()->id === AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V;
    }

    /**
     * @throws Exception
     */
    public function owner(): ?User
    {
        if ($this->isOwner()) {
            return $this;
        }

        return optional($this->pharmacies()->first())->owner();
    }

    /**
     * @throws Exception
     */
    public function ownerOrFail(): User
    {
        return $this->owner() ?? throw new Exception('User is expected to either be an owner or have an owner.');
    }

    /**
     * @return Collection|BillingAddress[]
     */
    public function billingAddressesAll(): Collection
    {
        $billingAddresses = $this->billingAddresses->concat(
            $this->pharmacies->map(fn (Pharmacy $pharmacy) => $pharmacy->billingAddress)->filter()
        )->concat(
            $this->invoices->map(fn (Invoice $invoice) => $invoice->billingAddress)->filter()
        );

        if ($this->isOwner()) {
            $this->getSubOwners()->each(function (User $subOwner) use (&$billingAddresses) {
                $billingAddresses = $billingAddresses->concat($subOwner->billingAddresses);
            });
        }

        return $billingAddresses->unique()->sort(function (BillingAddress $a, BillingAddress $b) {
            return $a->pharmacies->count() > $b->pharmacies->count() ? -1 : 1;
        });
    }

    /**
     * @return Collection<User>
     *
     * @throws Exception
     */
    public function getAllOwnerRelatedUsers(): Collection
    {
        $ownerPharmacyIds = $this->owner()->pharmacies->pluck('id')->toArray();

        return PharmacyRoleUser::select('user_id')
            ->distinct()
            ->leftJoin('users', 'pharmacy_role_user.user_id', '=', 'users.id')
            ->whereIn('pharmacy_id', $ownerPharmacyIds)
            ->whereNull('users.deleted_at')
            ->with('user')
            ->get()
            ->pluck('user');
    }

    /**
     * @return Collection<User>
     */
    public function getSubOwners(): Collection
    {
        return $this->getAllOwnerRelatedUsers()->filter(fn (User $user) => $user->isSubOwner());
    }

    public function docSpaceGroups(?Pharmacy $pharmacy = null): BelongsToMany
    {
        return $this->belongsToMany(DocSpaceGroup::class)
            ->where('pharmacy_id', $pharmacy->id ?? currentPharmacy()->id)
            ->using(DocSpaceGroupUser::class);
    }

    public function docSpaceGroupsWithDocSpaces(): BelongsToMany
    {
        return $this->docSpaceGroups()->whereHas('docSpaces')->with('docSpaces');
    }

    public function getAccessibleDocSpaces()
    {
        return DocSpace::whereIn(
            'id',
            Arr::flatten($this->docSpaceGroupsWithDocSpaces()->get()->pluck('docSpaces.*.id')->toArray())
        )
            ->distinct()
            ->get();
    }

    public function hasRegisteredApomail(): bool
    {
        return $this->usingApomails()->where('status', ApomailStatus::ACTIVE)->exists();
    }

    public function hasReservedApomail(): bool
    {
        return $this->usingApomails()->where('status', ApomailStatus::RESERVED)->exists();
    }

    public function getMxidAttribute()
    {
        if ($this->owner()) {
            return app(FamedlyApi::class)->getFullFamedlyIdForUser($this);
        } else {
            return null;
        }
    }

    public function canBeDeleted(): bool
    {
        return ! $this->isOwner()
            && ! $this->isSubOwner()
            && $this->isPharmacyUser()
            && ! $this->hasPharmacies();
    }

    /** @deprecated  */
    public function latestBillingDate(): ?Carbon
    {
        /** @var ?Carbon $date */
        $date = $this->pharmacies
            ->map(fn (Pharmacy $pharmacy) => $pharmacy->subscriptionOrders()
                ->whereNotNull('invoice_id')
                ->orderBy('ended_at')
                ->get()
            )
            ->flatten()
            ->pluck('ended_at')
            ->last();

        return $date;
    }

    public function getLastPossibleDateChangingAssociationRetroactively(): Carbon
    {
        $date = $this->latestBillingDate();

        /** @var Carbon $membershipStartedAtDate */
        $membershipStartedAtDate = Carbon::make($this->currentAssociationMembershipHistory?->started_at ?? now());

        if ($date === null || $date < $membershipStartedAtDate) {
            $date = $membershipStartedAtDate;
        }

        return $date;
    }

    protected function weeklyWorkingHours(): Attribute
    {
        return Attribute::make(
            get: fn () => is_numeric($this->weekly_working_minutes) ? $this->weekly_working_minutes / 60 : null,
        );
    }

    public function isBetaTester(): bool
    {
        return $this->attributes['is_beta_tester'] ?? false;
    }

    public function hasFinishedRetaxOnboarding(): bool
    {
        return $this->getGeneralSetting(UserSettingTypes::RETAX_ONBOARDING_FINISHED)?->value ?? false;
    }
    // </editor-fold>
}
