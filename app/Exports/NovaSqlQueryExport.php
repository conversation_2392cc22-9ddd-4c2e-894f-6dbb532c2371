<?php

namespace App\Exports;

use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

readonly class NovaSqlQueryExport implements FromArray, ShouldAutoSize, WithCustomCsvSettings, WithHeadings, WithStrictNullComparison
{
    /** @param array<string, mixed> $result */
    public function __construct(
        private array $result
    ) {}

    /** @return array<string, mixed> */
    public function getCsvSettings(): array
    {
        return [
            'delimiter' => ',',
            'enclosure' => '',
        ];
    }

    /** @return array<string, mixed> */
    public function array(): array
    {
        return $this->result;
    }

    /** @return array<int, int|string> */
    public function headings(): array
    {
        $item = Arr::first($this->result);

        assert(is_array($item));

        return array_keys($item);
    }
}
