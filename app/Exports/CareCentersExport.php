<?php

namespace App\Exports;

use App\KimAddressCareCenter;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

readonly class CareCentersExport implements FromCollection, ShouldAutoSize, WithHeadings, WithMapping
{
    public function __construct(
        private Collection $careCenters
    ) {}

    /** @return array<int, string> */
    public function headings(): array
    {
        return [
            'Name der Einrichtung',
            'An<PERSON><PERSON>chpartner',
            'Telefonnummer',
            'Straße',
            'PLZ',
            'Stadt',
        ];
    }

    public function collection(): Collection
    {
        return $this->careCenters;
    }

    /**
     * @param  KimAddressCareCenter  $row
     * @return array<int, float|string>
     */
    public function map($row): array
    {
        return [
            $row->name,
            $row->contact_person,
            $row->phone,
            $row->street.' '.$row->house_number,
            $row->postcode,
            $row->city,
        ];
    }
}
