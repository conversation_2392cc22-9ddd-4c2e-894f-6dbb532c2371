<?php

namespace App;

use App\Enums\VaccinationImport\InvoiceStatusEnum;
use App\Mail\VaccinationImportAccountingFinishedMail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class VaccinationImportInvoice extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoice')
            ->singleFile()
            ->useDisk('vaccination-import-invocies');
    }

    public function finished(bool $notify = true)
    {
        $this->update([
            'status' => InvoiceStatusEnum::GENERATED,
        ]);

        if ($notify) {
            if ($this->user) {
                $user = $this->user;
            } else {
                $user = $this->pharmacy->owner();
            }

            Mail::to($user->routeEmailsTo)->send(new VaccinationImportAccountingFinishedMail($user, $this));
        }
    }

    public function isFinished()
    {
        return $this->status == InvoiceStatusEnum::GENERATED;
    }
}
