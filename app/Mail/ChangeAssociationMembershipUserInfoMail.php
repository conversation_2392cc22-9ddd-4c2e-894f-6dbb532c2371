<?php

namespace App\Mail;

use App\Enums\AssociationEnum;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class ChangeAssociationMembershipUserInfoMail extends Mailable
{
    use Queueable, SerializesModels;

    protected User $user;

    protected ?int $associationId;

    protected Carbon $changeDate;

    public function __construct(User $user, ?int $associationId, Carbon $changeDate)
    {
        $this->user = $user;
        $this->associationId = $associationId;
        $this->changeDate = $changeDate;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Achtung: Änderung der Verbandsmitgliedschaft im Portal',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.change-association-membership-user-info',
            with: [
                'user' => $this->user,
                'association' => $this->associationId ? AssociationEnum::getLabels()[$this->associationId] : null,
                'changeDate' => Carbon::make($this->changeDate)->addDay(),
                'deadlineDate' => $this->getDeadlineForTosAndSubscription(Carbon::make($this->changeDate)),
            ]
        );
    }

    public function getDeadlineForTosAndSubscription(Carbon $date): Carbon
    {
        return $date <= now()
            ? now()->addMonthsNoOverflow()->addDay()
            : $date->endOfMonth()->addMonthsNoOverflow()->addDay();
    }
}
