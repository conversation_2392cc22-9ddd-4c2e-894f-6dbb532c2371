<?php

namespace App\Mail;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RevertAssociationMembershipUserInfoMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected User $user,
        protected ?int $associationId
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Achtung: Änderung der Verbandsmitgliedschaft widerrufen',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.revert-association-membership-user-info',
            with: [
                'user' => $this->user,
                'associationId' => $this->associationId,
            ]
        );
    }
}
