<?php

namespace App\Http\Middleware;

use App\Http\Controllers\IPaymentPostbackMiddleware;
use Closure;
use Illuminate\Http\Request;
use Response;

class AbilitapayPostbackMiddleware implements IPaymentPostbackMiddleware
{
    private string $incomingApiKey;

    public function __construct(string $incomingApiKey)
    {
        $this->incomingApiKey = $incomingApiKey;
    }

    public function handle(Request $request, Closure $next)
    {
        if ($request->getContentType() !== 'form') {
            return Response::make('Invalid Content Type', 400);
        }

        $params = array_map(static fn ($value) => $value ?? '', $request->all());

        unset($params['checksum']);
        $query = http_build_query($params, null, '&', PHP_QUERY_RFC1738);
        $calculatedChecksum = sha1($query.$this->incomingApiKey);

        return $request->get('checksum') === $calculatedChecksum
            ? $next($request)
            : Response::make('Invalid checksum', 401);
    }
}
