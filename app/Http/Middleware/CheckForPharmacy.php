<?php

namespace App\Http\Middleware;

use App\Exceptions\OIDCUserNotFoundException;
use App\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckForPharmacy
{
    public function handle(Request $request, Closure $next)
    {
        if (($user = Auth::user())) {
            if (! ($user instanceof User)) {
                throw new \RuntimeException('User not found');
            }

            if ($user->canBeDeleted()) {
                $user->delete();

                throw new OIDCUserNotFoundException;
            }
        }

        return $next($request);
    }
}
