<?php

namespace App\Http\Middleware;

use App\Helper\OIDCHelper;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use OpenIDConnectClient\AccessToken;
use RuntimeException;
use Throwable;

class RefreshToken
{
    public function __construct(
        protected OIDCHelper $oidc
    ) {}

    /** @phpstan-ignore-next-line return type is too restrictive and may only cause more problems - decided by TechLead */
    public function handle(Request $request, Closure $next)
    {
        $token = $this->oidc->token();

        if (! $token) {
            return $next($request);
        }

        if (time() < $token->getExpires() - 60) {
            return $next($request);
        }

        /** @var ?int $notBeforePolicy */
        $notBeforePolicy = Arr::get($token->getValues(), 'not-before-policy');

        if (now()->isBefore(Carbon::createFromTimestamp($notBeforePolicy ?? 0))) {
            return $next($request);
        }

        try {
            $response = $this->oidc->refresh($token);

            if (! $response->successful()) {
                $exception = $response->toException();

                if ($exception === null) {
                    throw new \Exception('Cannot create a response exception.');
                }

                throw $exception;
            }

            $data = $response->json();

            if (! is_array($data)) {
                throw new RuntimeException('No data returned.');
            }

            $token = new AccessToken($data);

            $this->oidc->toSession($token);
        } catch (Throwable $exception) {
            $this->logout($token, $request);

            return redirect()->route('home');
        }

        return $next($request);
    }

    protected function logout(AccessToken $token, Request $request): void
    {
        $this->oidc->destroyUserSessionByLogoutToken($token->getToken());

        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();
    }
}
