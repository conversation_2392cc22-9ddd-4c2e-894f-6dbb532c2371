<?php

namespace App\Http\Controllers;

use App\Enums\PharmacyRoleEnum;
use App\Http\Requests\PharmacyUserRequest;
use App\Pharmacy;
use App\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class PharmacyUserController extends Controller
{
    public function create(Pharmacy $pharmacy): View
    {
        $this->authorize('administrateUsers', $pharmacy);

        SEOMeta::setTitle('Nutzer erstellen');

        return view('pharmacy.users.create', [
            'pharmacy' => $pharmacy,
        ]);
    }

    /**
     * @param  Request  $request
     *
     * @throws AuthorizationException
     */
    public function store(Pharmacy $pharmacy, PharmacyUserRequest $request): RedirectResponse
    {
        $this->authorize('administrateUsers', $pharmacy);

        $request->createPharmacyUser($pharmacy);

        notify(__('notifications.employee.created'));

        return redirect()->route('pharmacies.users', [$pharmacy]);
    }

    public function edit(Pharmacy $pharmacy, User $user): View
    {
        $this->authorize('administrateUsers', $pharmacy);
        $this->authorize('update', $user);

        SEOMeta::setTitle('Nutzer bearbeiten');

        return view('pharmacy.users.edit', [
            'pharmacy' => $pharmacy,
            'user' => $user,
        ]);
    }

    /**
     * @param  Request  $request
     *
     * @throws AuthorizationException
     */
    public function update(Pharmacy $pharmacy, User $user, PharmacyUserRequest $request): RedirectResponse
    {
        $this->authorize('administrateUsers', $pharmacy);
        $this->authorize('update', $user);
        abort_if($user->getPharmacyRole($pharmacy) === PharmacyRoleEnum::OWNER, 403);
        abort_if($user->getPharmacyRole($pharmacy) === PharmacyRoleEnum::SUB_OWNER, 403);

        $request->updatePharmacyUser($pharmacy, $user);

        notify(__('notifications.employee.updated'));

        return redirect()->route('pharmacies.users', [$pharmacy]);
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(Pharmacy $pharmacy, User $user): RedirectResponse
    {
        $this->authorize('administrateUsers', $pharmacy);
        $this->authorize('delete', $user);

        $user->delete();

        notify('Mitarbeiter/in erfolgreich gelöscht');

        return redirect()->route('pharmacies.users', [$pharmacy]);
    }
}
