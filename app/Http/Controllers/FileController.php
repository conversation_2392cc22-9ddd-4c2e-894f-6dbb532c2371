<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    public function uploadTemp(Request $request)
    {
        try {
            $fileName = Storage::disk('temp')->put('', $request->image);
        } catch (Exception $exception) {
            return response()->json([
                'success' => 'error',
                'message' => $exception->getMessage(),
            ]);
        }

        return response()->json([
            'success' => 'success',
            'fileName' => $fileName,
        ]);
    }

    public function getTemp($filename)
    {
        $content = Storage::disk('temp')->get($filename);

        return response($content)->header('Content-Type', 'image/jpeg');

        $response = Response::make($file, 200);
        $response->header('Content-Type', $type);

        return $response;
    }
}
