<?php

namespace App\Http\Controllers;

use App\CovidVaccination;
use App\Enums\Vaccinate\CovidVaccinationStepEnum;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Pharmacy;
use App\Vaccination;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class PharmacyCovidVaccinateController extends Controller
{
    public function index(Pharmacy $pharmacy)
    {
        $this->authorize('index', [CovidVaccination::class, $pharmacy]);

        return view('pharmacy.vaccinateCovid.index', [
            'pharmacy' => $pharmacy,
            'vaccinations' => $pharmacy->vaccinations()->where('type', VaccinationTypeEnum::COVID)->orderBy('id', 'DESC')->paginate(8),
        ]);
    }

    /**
     * @return Application|Factory|RedirectResponse|View
     *
     * @throws AuthorizationException
     */
    public function start(Pharmacy $pharmacy)
    {
        $this->authorize('store', [CovidVaccination::class, $pharmacy]);

        DB::beginTransaction();

        try {
            /** @var Vaccination $vaccination */
            $vaccination = $pharmacy->vaccinations()->create([
                'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
                'type' => VaccinationTypeEnum::COVID,
                'date' => now()->startOfDay(),
            ]);

            $vaccination->vaccinationPatient()->create();
            $vaccination->covidVaccination()->create();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return response()->redirectToRoute('pharmacies.vaccinate-covid.start-information', [$pharmacy, $vaccination]);
    }

    public function edit(Pharmacy $pharmacy, Vaccination $vaccination)
    {
        $this->authorize('update', $vaccination->covidVaccination);

        return \redirect()->route(CovidVaccinationStepEnum::getRedirectRoute($vaccination), [$pharmacy, $vaccination]);
    }
}
