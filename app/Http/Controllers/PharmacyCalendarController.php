<?php

namespace App\Http\Controllers;

use App\Helper\ApomondoApi;
use App\Pharmacy;
use Illuminate\Http\RedirectResponse;

class PharmacyCalendarController extends Controller
{
    public function redirector(Pharmacy $pharmacy, ApomondoApi $apomondoApi): RedirectResponse
    {
        $this->authorize('accessCalendar', $pharmacy);

        $link = $apomondoApi->getCalendarLoginLink(user(), $pharmacy);

        return response()->redirectTo($link);
    }
}
