<?php

namespace App\Http\Controllers\PharmacyMeasureBloodPressure;

use App\Enums\MeasureBloodPressure\MeasureBloodPressureStepEnum;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Http\Controllers\Controller;
use App\PharmaceuticalService;
use App\Pharmacy;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class FirstDocumentController extends Controller
{
    public function edit(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService)
    {
        $this->authorize('update', $pharmaceuticalService->measureBloodPressurePatient);

        SEOMeta::setTitle(trans('messages.measureBloodPressure'));

        if ($pharmaceuticalService->status == PharmaceuticalServiceStatus::FINISHED) {
            return redirect()->route('pharmacies.pharmaceutical-services.measure-blood-pressures.finished', [$pharmacy, $pharmaceuticalService]);
        }

        if ($pharmaceuticalService->step < MeasureBloodPressureStepEnum::FIRST_DOCUMENT) {
            return \redirect()->route(MeasureBloodPressureStepEnum::getRedirectRoute($pharmaceuticalService), [$pharmacy, $pharmaceuticalService]);
        }

        return view('pharmacy.measureBloodPressure.first-document', [
            'pharmacy' => $pharmaceuticalService->pharmacy,
            'pharmaceuticalService' => $pharmaceuticalService,
            'accepted' => $pharmaceuticalService->step != MeasureBloodPressureStepEnum::FIRST_DOCUMENT,
        ]);
    }

    /**
     * @return RedirectResponse
     *
     * @throws AuthorizationException
     */
    public function update(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService, Request $request)
    {
        $this->authorize('update', $pharmaceuticalService->measureBloodPressurePatient);

        $request->validate([
            'downloaded' => ['accepted'],
        ]);

        if ($pharmaceuticalService->step < MeasureBloodPressureStepEnum::ACQUISITION_DATA) {
            $pharmaceuticalService->update(['step' => MeasureBloodPressureStepEnum::ACQUISITION_DATA]);
        }

        return response()->redirectToRoute('pharmacies.pharmaceutical-services.measure-blood-pressures.acquisition-data', [$pharmacy, $pharmaceuticalService]);
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function generateAndDownload(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService, Request $request)
    {
        $pdf = app('dompdf.wrapper');
        $pdf->loadHtml(view('pharmacy.measureBloodPressure.pdf.arbeitshilfe-vereinbarung', [
            'pharmaceuticalService' => $pharmaceuticalService,
        ]));

        return $pdf->download($pharmaceuticalService->uuid.'-arbeitshilfe-vereinbarung.pdf');
    }
}
