<?php

namespace App\Http\Controllers\Api;

use App\Domains\ApoPortal\Application\ApoPortalPharmacyHelper;
use App\Domains\Subscription\Application\FeatureAccess\ChatFeatureAccess;
use App\Enums\Apomondo\ApomondoRoleEnum;
use App\Enums\AssociationRoleEnum;
use App\Helper\TokenServiceApi;
use App\Http\Requests\ChatRequest;
use App\Pharmacy;
use App\Settings\ApomondoSettings;
use App\Settings\JwtSettings;
use App\Settings\RetaxSettings;
use App\Support\FamedlyApi;
use Firebase\JWT\JWT;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;

class UserController extends ClientCredentialsController
{
    public function chatToken(ChatRequest $request): JsonResponse
    {
        $user = $request->user;

        if ($user->pharmacies->count() === 0) {
            abort(403, 'User has no pharmacy.');
        }

        $userCanChat = $user->pharmacies->contains(static fn (Pharmacy $pharmacy) => ChatFeatureAccess::check($pharmacy)->canUse());
        abort_unless($userCanChat, 403, 'User has pharmacies but none of them has chat access.');

        $auth = $request->auth;
        $displayname = null;
        if (is_object($auth) && property_exists($auth, 'name')) {
            $displayname = $auth->name;
        }

        return Response::json([
            'token' => app(FamedlyApi::class)->generateJWTForUser($user, displayname: $displayname),
        ]);
    }

    /**
     *  Documentation: https://gedisa.atlassian.net/wiki/spaces/A/pages/1003683846/Pharmacies+Token+Apomondo+Token
     */
    public function pharmaciesToken(ChatRequest $request, ApomondoSettings $apomondoSettings): JsonResponse
    {
        $user = $request->user;

        $permissions = app(TokenServiceApi::class)->makeSynchronizePermissionsPayload($user)['users'][0]['pharmacies'];

        $displayname = $user->name;
        $auth = $request->auth;
        if (is_object($auth) && property_exists($auth, 'name')) {
            $displayname = $auth->name;
        }

        $pharmaciesToken = JWT::encode([
            'iss' => $apomondoSettings->jwtIssuer(),
            'iat' => now()->timestamp,
            'exp' => now()->addMinutes(60)->timestamp,
            'aud' => $apomondoSettings->jwtAudience(),
            'sub' => $user->id,
            'user_id' => $user->id,
            'displayname' => $displayname,
            'pharmacies' => $this->extractApomondoPermissions($permissions),
        ], $apomondoSettings->privateKey(), 'RS256');

        return Response::json([
            'token' => JWT::encode([
                'iss' => $apomondoSettings->pharmaciesIssuer(),
                'iat' => now()->timestamp,
                'exp' => now()->addMinutes(60)->timestamp,
                'aud' => $apomondoSettings->pharmaciesAudience(),
                'sub' => $user->uuid,
                'permissions' => $permissions,
                'pharmacy_information' => app(ApoPortalPharmacyHelper::class)->mapPharmacyInformation($user),
                'apomondo_token' => $pharmaciesToken,
            ], $apomondoSettings->pharmaciesPrivateKey(), 'RS256'),
        ]);
    }

    /**
     * @param  string|array<string, array<int, string>|int|string|null>  $permissions
     * @return mixed[]
     */
    public function extractApomondoPermissions(mixed $permissions): array
    {
        $apomondoPermissions = [];

        $permissions = is_array($permissions) ? $permissions : [];

        foreach ($permissions as $pharmacyUuid => $permissionArray) {
            /** @phpstan-ignore-next-line */
            $filteredPermissions = collect(ApomondoRoleEnum::cases())->filter(fn (ApomondoRoleEnum $role) => in_array($role->value, $permissionArray, true))->values()->toArray();

            // nur die obersten 2 können true sein, das ist aber wohl so gewollt
            if (count($filteredPermissions) > 0) {
                $apomondoPermissions[] = [
                    'id' => $pharmacyUuid,
                    'permissions' => [
                        'terminmanagement_manager' => in_array('terminmanagement_manager', $filteredPermissions),
                        'terminmanagement' => in_array('terminmanagement', $filteredPermissions),
                        'telepharmazie' => in_array('telepharmacy', $filteredPermissions),
                        'telepharmazie_manager' => in_array('telepharmacy_manager', $filteredPermissions),
                        'terminmanagement_employee' => in_array('terminmanagement_employee', $filteredPermissions),
                        'telepharmazie_employee' => in_array('telepharmacy_employee', $filteredPermissions),
                    ],
                ];
            }
        }

        return $apomondoPermissions;
    }

    public function retaxToken(ChatRequest $request, JwtSettings $jwtSettings, RetaxSettings $retaxSettings): JsonResponse
    {
        $user = $request->user;

        $pharmacies = $user->pharmacies->mapWithKeys(function ($pharmacy) use ($user) {
            return [$pharmacy->uuid => [
                'roles' => [$pharmacy->pivot->role_name],
                'permissions' => $user->can('useRetax', $pharmacy) && $user->isOwner() ? ['retax_owner'] : [],
            ]];
        });

        $associations = $user->associations->mapWithKeys(function ($association) use ($user) {
            return [$association->id => [
                'roles' => [$association->pivot->role_name],
                'permissions' => $user->can('indexRetax', $association)
                    ? match ($association->pivot->role_name) {
                        AssociationRoleEnum::ADMIN => ['retax_leader'],
                        AssociationRoleEnum::EMPLOYEE => ['retax_employee'],
                        default => [],
                    }
                    : [],
            ]];
        });

        $jwtData = [
            'exp' => now()->addMinutes(60)->timestamp,
            'iat' => now()->timestamp,
            'iss' => $retaxSettings->issuer(),
            'aud' => $retaxSettings->audience(),
            'sub' => $user->uuid,
            'resource_access' => [
                'retax' => [
                    'pharmacies' => $pharmacies,
                    'associations' => $associations,
                ],
            ],
        ];

        return Response::json([
            'token' => JWT::encode($jwtData, $jwtSettings->privateKey(), 'RS256'),
        ]);
    }
}
