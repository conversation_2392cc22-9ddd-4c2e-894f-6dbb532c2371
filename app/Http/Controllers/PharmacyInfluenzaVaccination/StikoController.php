<?php

namespace App\Http\Controllers\PharmacyInfluenzaVaccination;

use App\Enums\Vaccinate\InfluenzaVaccinationStepEnum;
use App\Enums\YesNoUnknownEnum;
use App\Http\Controllers\Controller;
use App\Pharmacy;
use App\Vaccination;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;

class StikoController extends Controller
{
    /**
     * STEP 5
     */
    public function edit(Pharmacy $pharmacy, Vaccination $vaccination)
    {
        $this->authorize('update', $vaccination);

        SEOMeta::setTitle(trans('messages.vaccinate'));

        if ($vaccination->step < InfluenzaVaccinationStepEnum::STIKO) {
            return \redirect()->route(InfluenzaVaccinationStepEnum::getRedirectRoute($vaccination), [$pharmacy, $vaccination]);
        }

        return view('pharmacy.vaccinateInfluenza.vaccinate_stiko', [
            'pharmacy' => $vaccination->pharmacy,
            'vaccination' => $vaccination,
            'influenca' => $vaccination->influenzaVaccination,
        ]);
    }

    public function update(Pharmacy $pharmacy, Vaccination $vaccination, Request $request)
    {
        $this->authorize('update', $vaccination);

        $validated = $request->validate([
            'group_stiko_job' => ['required', 'in:'.YesNoUnknownEnum::getAll()->implode(',')],
            'group_stiko_health' => ['required', 'in:'.YesNoUnknownEnum::getAll()->implode(',')],
        ]);

        $vaccination->influenzaVaccination->stiko_job_related = $request->group_stiko_job;
        $vaccination->influenzaVaccination->stiko_health_related = $request->group_stiko_health;

        $vaccination->influenzaVaccination->save();

        if ($vaccination->step < InfluenzaVaccinationStepEnum::ACCEPTANCE) {
            $vaccination->update(['step' => InfluenzaVaccinationStepEnum::ACCEPTANCE]);
        }

        return response()->redirectToRoute('pharmacies.vaccinate.acceptance', [$pharmacy, $vaccination]);
    }
}
