<?php

namespace App\Http\Controllers;

use App\News;
use App\RssFeedItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    public function index()
    {
        $blog = Cache::remember(
            'dashboard-news-feed',
            Carbon::now()->addMinutes(5)->diffInSeconds(),
            static function () {
                return News::query()
                    ->released()
                    ->where('visible_intern', true)
                    ->with(['media', 'author.media'])
                    ->orderBy('release_date', 'DESC')
                    ->orderBy('id', 'DESC')
                    ->take(3)
                    ->get();
            }
        );

        return view('user.dashboard', [
            'blog' => $blog,
            'amkNews' => $this->getNewsForSource('amk-news'),
            'newsroom' => $this->getNewsForSource('newsroom'),
            'pz' => $this->getNewsForSource('ps-online'),
        ]);
    }

    private function getNewsForSource(string $source)
    {
        return Cache::remember(
            'rss-feed-items-'.$source,
            Carbon::now()->endOfHour()->addMinutes(5)->diffInSeconds(),
            function () use ($source) {
                return RssFeedItem::query()
                    ->whereHas('source', fn ($q) => $q->where('identifier', $source))
                    ->orderByDesc('modification_date')
                    ->limit(5)
                    ->get();
            }
        );
    }
}
