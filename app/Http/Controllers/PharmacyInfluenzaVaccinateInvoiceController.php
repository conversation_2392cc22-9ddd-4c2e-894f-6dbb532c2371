<?php

namespace App\Http\Controllers;

use App\InfluenzaVaccinationInvoice;
use App\Pharmacy;

class PharmacyInfluenzaVaccinateInvoiceController extends Controller
{
    public function index(Pharmacy $pharmacy)
    {
        abort(404);
        $this->authorize('viewAny', [InfluenzaVaccinationInvoice::class, $pharmacy]);

        return view('pharmacy.vaccinateInfluenza.invoice.index', [
            'pharmacy' => $pharmacy,
        ]);
    }

    public function download(Pharmacy $pharmacy, InfluenzaVaccinationInvoice $influenzaVaccinationInvoice)
    {
        abort(404);
        $this->authorize('view', [InfluenzaVaccinationInvoice::class, $pharmacy]);

        abort_unless($influenzaVaccinationInvoice->pharmacy->is($pharmacy), 403);

        return response()->download($influenzaVaccinationInvoice->getFirstMedia('invoice')->getPath(), 'abrechnung_'.$influenzaVaccinationInvoice->start_date->format('d-m-Y').'_'.$influenzaVaccinationInvoice->end_date->format('d-m-Y').'.pdf');
    }
}
