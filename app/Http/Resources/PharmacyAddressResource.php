<?php

namespace App\Http\Resources;

use App\Helper\ApomondoApi;
use App\Http\Resources\Util\HasNormalPagination;
use App\PharmacyAddress;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin PharmacyAddress */
class PharmacyAddressResource extends JsonResource
{
    use HasNormalPagination;

    public function toArray($request)
    {
        return [
            'identifier' => $this->pharmacy->uuid,
            'name' => $this->pharmacy->name,
            'adress' => [
                'type' => $this->type,
                'optional_address_line' => $this->optional_address_line,
                'street' => $this->street,
                'house_number' => $this->house_number,
                'postalCode' => $this->postcode,
                'city' => $this->city,
            ],
            'position' => [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            'telcom' => [
                [
                    'system' => 'phone',
                    'value' => $this->pharmacy->phone,
                ],
                [
                    'system' => 'fax',
                    'value' => $this->pharmacy->fax,
                ],
                [
                    'system' => 'email',
                    'value' => $this->pharmacy->email,
                ],
                [
                    'system' => 'url',
                    'value' => $this->pharmacy->website,
                ],
            ],
            'courierService' => [
                'active' => (bool) $this->pharmacy->courier_service,
                'radius' => $this->pharmacy->courier_service_radius,
            ],
            'transport' => [
                'parkingSpace' => (bool) $this->pharmacy->has_near_parking_space,
                'stations' => TransportResource::collection($this->whenLoaded('pharmacy.publicTransportStations')),
            ],
            'shippingPharmacy' => [
                'enabled' => $this->pharmacy->shipping_pharmacy_enabled,
                'name' => $this->pharmacy->shipping_pharmacy_name,
                'website' => $this->pharmacy->shipping_pharmacy_website,
            ],
            'focusAreas' => FocusAreaResource::collection($this->whenLoaded('focusAreas')),
            'hoursOfOperation' => BusinessHourResource::collection($this->pharmacy->businessHours),
            'languages' => $this->pharmacy->languages->pluck('code'),
            'images' => PharmacyImageResource::collection($this->pharmacy->pharmacyImages),
            'meta' => [
                'instituteId' => $this->pharmacy->institute_id,
                'nId' => $this->pharmacy->n_id,
                'telematicsId' => optional($this->pharmacy->telematicsId)->fullId(),
                'pharmacyId' => $this->pharmacy->pharmacy_id,
                'coronaRapidTest' => (bool) $this->pharmacy->corona_rapid_test,
                'acceptsEPrescription' => (bool) $this->pharmacy->accepts_e_prescription,
                'coronaRapidTestBookingUrl' => $this->pharmacy->corona_rapid_test_booking_url,
                'active' => (bool) $this->pharmacy->active,
                'isMainPharmacy' => (bool) $this->pharmacy->is_main,
                'siblingPharmacies' => $this->pharmacy->siblingPharmacies()->pluck('uuid'),
                'type' => new PharmacyTypeResource($this->whenLoaded('pharmacy.pharmacyType')),
                'distance' => $this->when($this->distance != null, $this->distance),
                'vaccinationImport' => $this->pharmacy->vaccination_import,
                'sperateCertificationCenter' => $this->pharmacy->pharmacyAddresses->contains('type', 2),
                'calendarBookingUrl' => $this->pharmacy->calendar_uuid != null && $this->pharmacy->uses_calendar ? ApomondoApi::getUrl($this->pharmacy) : null,
            ],
            'calendarTopics' => $this->pharmacy->uses_calendar ? CalendarTopicResource::collection($this->pharmacy->calendarTopics) : [],
            'pharmaceuticalServiceTypes' => PharmaceuticalServiceTypeResource::collection($this->pharmacy->pharmaceuticalServiceTypes),
            'vaccination' => [
                'covid' => (bool) $this->pharmacy->does_covid_vaccination,
                'influenza' => (bool) $this->pharmacy->does_influenza_vaccination,
            ],
            'created_at' => $this->pharmacy->created_at,
            'updated_at' => $this->pharmacy->updated_at,
        ];
    }
}
