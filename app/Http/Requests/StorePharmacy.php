<?php

namespace App\Http\Requests;

use App\Pharmacy;
use App\User;

class StorePharmacy extends \App\Http\Requests\Pharmacy
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        if (! ($this->user() instanceof User)) {
            throw new \RuntimeException('User not found');
        }

        return $this->user()->can('create', Pharmacy::class);
    }
}
