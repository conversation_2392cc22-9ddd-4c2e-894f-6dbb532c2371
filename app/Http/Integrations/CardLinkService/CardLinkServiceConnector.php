<?php

namespace App\Http\Integrations\CardLinkService;

use App\Http\Integrations\Traits\CachedAccessToken;
use App\Settings\CardLinkSettings;
use Saloon\Helpers\OAuth2\OAuthConfig;
use Saloon\Http\Connector;
use Saloon\Traits\OAuth2\ClientCredentialsGrant;
use Saloon\Traits\Plugins\AcceptsJson;

class CardLinkServiceConnector extends Connector
{
    use AcceptsJson;
    use CachedAccessToken;
    use ClientCredentialsGrant;

    public function resolveBaseUrl(): string
    {
        return CardLinkSettings::url();
    }

    protected function defaultOauthConfig(): OAuthConfig
    {
        return OAuthConfig::make()
            ->setClientId(CardLinkSettings::clientId())
            ->setClientSecret(CardLinkSettings::clientSecret())
            ->setTokenEndpoint(CardLinkSettings::tokenEndpoint());
    }
}
