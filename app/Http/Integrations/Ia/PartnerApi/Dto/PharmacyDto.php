<?php

namespace App\Http\Integrations\Ia\PartnerApi\Dto;

use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\BusinessHoursDto;
use Illuminate\Support\Collection;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class PharmacyDto extends Data implements Wireable
{
    use WireableData;

    public ?string $pharmacyId = null;

    public ?string $partnerId = null;

    public ?string $name = null;

    public ?string $legalEntity = null;

    /** @var Collection<int, OwnerDto> */
    public ?Collection $owners = null;

    /** @var Collection<int, PharmacyServiceDto> */
    public ?Collection $services = null;

    public ?string $phone = null;

    public ?string $email = null;

    public ?string $fax = null;

    public ?string $website = null;

    public ?string $street = null;

    public ?string $city = null;

    public ?string $postcode = null;

    public ?string $region = null;

    public bool $isZpaCustomer = true;

    public ?BusinessHoursDto $businessHours = null;

    public function hasBusinessHours(): bool
    {
        return $this->businessHours !== null;
    }
}
