<?php

namespace App\Http\Integrations\Apomail\Requests;

use GuzzleHttp\Cookie\CookieJar;
use Saloon\Enums\Method;

class DeleteApomailRequest extends AbstractApomailRequest
{
    protected Method $method = Method::DELETE;

    public function __construct(public CookieJar $cookie, private string $email)
    {
        parent::__construct($this->cookie);
    }

    public function resolveEndpoint(): string
    {
        return sprintf('/api/user/%s', $this->email);
    }
}
