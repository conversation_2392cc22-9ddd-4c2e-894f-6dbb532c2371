<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IaRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url',
        'method',
        'request_body',
        'response_status_code',
        'response_body',
    ];

    public function sync(): BelongsTo
    {
        return $this->belongsTo(IaSync::class);
    }
}
