<?php

namespace App\Data;

use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\BusinessHoursDto;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class IaMergeResultDto extends Data implements Wireable
{
    use WireableData;

    public string $name;

    public string $phone;

    public string $email;

    public string $fax;

    public string $website;

    public string $street;

    public string $house_number;

    public string $postcode;

    public string $city;

    public BusinessHoursDto $businessHours;
}
