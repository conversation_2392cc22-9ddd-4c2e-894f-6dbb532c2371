APP_NAME="Mein Apothekenportal"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
APP_SERVICE="mein-apothekenportal.test"
#APP_TRACKING_ENABLED=false
# maybe useful for local development
#APP_PORT=
#APP_TRACKING_ENABLED=false

# maybe useful for local development
# not nesessary for php7.4 - sail does not configure xdebug via the .env vars
# @see https://gedisa.atlassian.net/wiki/spaces/A/pages/39452673/Sail+Docker+Xdebug
# xdebug must be configured in /docker/7.4/php.ini
#SAIL_XDEBUG_MODE=develop,debug,coverage
#SAIL_XDEBUG_CONFIG="client_host=host.docker.internal client_port=9003 idekey=PHPSTORM"

SELENIUM_APP_TEST_URL=http://localhost
SELENIUM_CHROME_DRIVER_BASE_SERVER_URL=http://localhost:4444
SELENIUM_FAILURES_SCREENSHOTS_PATH=storage/selenium/failures/screenshots
SELENIUM_FAILURES_VIDEOS_PATH=storage/selenium/failures/videos

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=host.docker.internal
DB_PORT=3306
DB_DATABASE=laravel_sail
DB_USERNAME=laravel_sail
DB_PASSWORD=password
#FORWARD_DB_PORT=

KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_CLIENT_SECRET=VKNEPaSM98JyGbUHslvvfCjMW1eMODFe
KEYCLOAK_CLIENT_ID=apothekenmanager
KEYCLOAK_ADMIN_URL=host.docker.internal:8080/admin/realms/Test
KEYCLOAK_ID_TOKEN_ISSUER=http://localhost:8080/realms/Test
KEYCLOAK_AUTHORIZE_URL=http://localhost:8080/realms/Test/protocol/openid-connect/auth
KEYCLOAK_ACCESS_TOKEN_URL=host.docker.internal:8080/realms/Test/protocol/openid-connect/token
KEYCLOAK_LOGOUT_URL=host.docker.internal:8080/realms/Test/protocol/openid-connect/logout
KEYCLOAK_USER_INFO_URL=host.docker.internal:8080/realms/Test/protocol/openid-connect/userinfo
KEYCLOAK_CERTS_URL=host.docker.internal:8080/realms/Test/protocol/openid-connect/certs
KEYCLOAK_KEY_FILE_NAME=keycloak.pem

USE_RISE_IDP=false
RISE_CLIENT_ID=apothekenportal
RISE_CLIENT_SECRET=secret
RISE_USER_MANAGEMENT_CLIENT_ID=gedisa-api
RISE_USER_MANAGEMENT_CLIENT_SECRET=secret
RISE_ID_TOKEN_ISSUER=https://int.idp-le.rise-service.de/auth/realms/gedisa
RISE_ADMIN_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa
RISE_AUTHORIZE_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa/protocol/openid-connect/auth
RISE_ACCESS_TOKEN_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa/protocol/openid-connect/token
RISE_LOGOUT_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa/protocol/openid-connect/logout
RISE_USER_INFO_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa/protocol/openid-connect/userinfo
RISE_CERTS_URL=https://int.idp-le.rise-service.de/auth/realms/gedisa/protocol/openid-connect/certs
RISE_USER_PROFILE_URL="https://int.idp-le.rise-service.de/auth/admin/gedisa/console/#/realms/gedisa/users/"
RISE_KEY_FILE_NAME="/var/www/html/storage/rise-stage.pem"

NOVA_SQL_QUERY_READ_HOST=host.docker.internal
NOVA_SQL_QUERY_USERNAME=laravel_sail
NOVA_SQL_QUERY_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

##REDIS SENTINEL CONFIG
#REDIS_USE_SENTINEL=true
#REDIS_CLIENT=predis
#REDIS_SENTINEL_SERVICE=main
#REDIS_HOST_1=**********
#REDIS_PORT_1=26379
#REDIS_HOST_2=**********
#REDIS_PORT_2=26379
#REDIS_HOST_3=**********
#REDIS_PORT_3=26379
#
#REDIS_DEFAULT_DB=10
#REDIS_CACHE_DB=11
#REDIS_SESSION_DB=12
#REDIS_HORIZON_DB=13

#LOCAL REDIS CONFIG
REDIS_CLIENT=phpredis
REDIS_HOST=host.docker.internal
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_SCHEME=tcp
# maybe useful for local development
#FORWARD_REDIS_PORT=

MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
# maybe useful for local development
#FORWARD_MAILHOG_PORT=
#FORWARD_MAILHOG_DASHBOARD_PORT=

#MAILCOACH_ENABLED=true
# newslettertool mailcoach url
#MAILCOACH_URL=host.docker.internal:8089/mailcoach/api

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SENTRY_LARAVEL_DSN=

PHARMACIES_EXPORT_MAIL_TO=
SCHEDULE_TEST_MAIL_TO=

IBM_VACCINATION_CENTER_PRIVATE_KEY_PASSWORD=
IBM_VACCINATION_CENTER_UUID=
IBM_VACCINATION_CENTER_USE_PHARMACY_UUID=
IBM_VACCINATION_CENTER_URL=
IBM_VACCINATION_THROTTLE=0
IBM_VACCINATION_TIMEOUT=0

PDF_FAST_LANE_MAX_PROCESSES=1
PDF_SLOW_LANE_MAX_PROCESSES=1

SCOUT_PREFIX=
SCOUT_DRIVER=null
SCOUT_QUEUE_ENABLED=false

ELASTIC_HOST=localhost:9200
ELASTIC_USER=elastic
ELASTIC_PASSWORD=changeme
LOG_LOGIN_ATTEMPTS=false

FILESYSTEM_CLOUD=minio
MINIO_ENDPOINT=http://minio:9000
MINIO_ENDPOINT_PUBLIC=http://minio:9000
MINIO_USE_PATH_STYLE_ENDPOINT=true
MINIO_KEY=sail
MINIO_SECRET=password
MINIO_REGION=us-east-1
MINIO_BUCKET=apothekenportal-dev_2
# maybe useful for local development
#FORWARD_MINIO_PORT=
#FORWARD_MINIO_CONSOLE_PORT=

S3_BUCKET_PREFIX=

NOTIFICATIONS_API_ENDPOINT_URL=
NOTIFICATIONS_API_ENDPOINT_TOKEN=
NOTIFICATIONS_API_TOKEN=

BLOG_REAL_IMAGE=1
AFTER_DECEMBER=true
GOCODING_ENABLED=false
GOOGLE_MAPS_API_KEY=
SUPPORT_EMAIL="<EMAIL>"
DAV_VACCINATION_PORTAL_URL=localhost:100
CHECK_NAME_ON_REGISTER=false
ELASTIC_STATISTICS_ENABLED=false

RKI_DIM_CERTIFICATE_PASSWORD=
RKI_DIM_CLIENT_ID=
RKI_DIM_CLIENT_SECRET=
RKI_DIM_USERNAME=
RKI_DIM_PASSWORD=
RKI_DIM_URL=

FAMEDLY_MESSENGER_JWT_SERVER="cyrano-jwt-test.famedly.de"
FAMEDLY_MESSENGER_JWT_SECRET=""

IBM_REGISTRATION_MAIL_ADDRESS_RECIPIENT="<EMAIL>"
IBM_REGISTRATION_MAIL_ADDRESS_SENDER="<EMAIL>"

PAYMENT_PROVIDER=abilitapay-test
ABILITAPAY_USE_FAKE_RESPONSES=true
ABILITAPAY_URL=https://some.api.url
ABILITAPAY_API_KEY=api-key
ABILITAPAY_INCOMING_API_KEY=incoming-key
ABILITAPAY_OUTGOING_API_KEY=outgoing-key
ABILITAPAY_TEST_URL=https://some.test-api.url
ABILITAPAY_TEST_API_KEY=test-api-key
ABILITAPAY_TEST_INCOMING_API_KEY=test-incoming-key
ABILITAPAY_TEST_OUTGOING_API_KEY=test-outgoing-key

SUBSCRIPTION_WL_CHANGE_DATE=2023-06-30

SDR_API_URL=https://int.gedisa-sdr.rise-service.de/api
SDR_UI_URL=https://int.gedisa-sdr.rise-service.de
SDR_SCHEMA_ID=60c348ba-4983-44e3-838c-cf31983dfcda

SERVICES_TOKEN_SERVICE_IMPORT_TOKEN=""
SERVICES_TOKEN_SERVICE_URL="host.docker.internal:8089"

APOMAIL_URL=
APOMAIL_ACTIVE=false
APOMAIL_CERTIFICATE_NAME=
APOMAIL_ADMIN_LOGIN_COOKIE_EXPIRING_MINUTES=
APOMAIL_ADMIN_USERNAME=
APOMAIL_ADMIN_PASSWORD=

REPROGRESS_EMAIL=<EMAIL>
APOGUIDE_SHOP_URL=https://shop-gedisa.de/

COVID_VACCINATION_CERTIFICATE_DISABLED_AT=2024-01-01

KIM_AKQUINET_DOMAIN=gedisa.kim.telematik
KIM_RISE_DOMAIN=rise.domain.kim.telematik

KIM_RISE_ACCESS_TOKEN_URL=
KIM_RISE_CLIENT_ID=
KIM_RISE_CLIENT_SECRET=
KIM_RISE_URL=
KIM_RISE_PRODUCT_UUID=

KIM_AKQUINET_CLIENT_ID=
KIM_AKQUINET_CLIENT_USERNAME=
KIM_AKQUINET_CLIENT_PASSWORD=""
KIM_AKQUINET_URL=https://ehealth-stage.akquinet.de
KIM_AKQUINET_PRODUCT_UUID=0

SETTINGS_CACHE_ENABLED=false

KIM_AKQUINET_APPOINTMENT_BOOKING_URL=

NNF_API_URL=https://datahub-int.ngdalabor.de

NGDA_IDP_CLIENT_ID=
NGDA_IDP_CLIENT_SECRET=
NGDA_IDP_URL=https://aro-authcluster-int-01.ngdalabor.de/auth/realms/entity
NGDA_IDP_AUTHORIZE_URL=https://aro-authcluster-int-01.ngdalabor.de/auth/realms/entity/protocol/openid-connect/auth
NGDA_IDP_TOKEN_URL=https://aro-authcluster-int-01.ngdalabor.de/auth/realms/entity/protocol/openid-connect/token
NGDA_IDP_LOGOUT_URL=https://aro-authcluster-int-01.ngdalabor.de/auth/realms/entity/protocol/openid-connect/logout
NGDA_RECIPIENT_ID=NBID10121T

PENNANT_STORE=database

CONSENT_SERVICE_URL=https://consent-service.public.preprod.api.gedisa.de/api
USE_CONSENT_SERVICE=true

# dev, qa, production
IA_UPDATE_PHARMACY_DATA_API_ENV=dev

IA_UPDATE_PHARMACY_DATA_API_URL_DEV=https://dev.ihreapotheken.de/partners/api
IA_UPDATE_PHARMACY_DATA_API_URL_DEV_KEY=e5fc9741970075f022c223de509ac8bfe5a9df6d7f2196a2415dfe1c361ce723

IA_UPDATE_PHARMACY_DATA_API_URL_QA=https://qa.ihreapotheken.de/partners/api
IA_UPDATE_PHARMACY_DATA_API_URL_QA_KEY=

IA_UPDATE_PHARMACY_DATA_API_URL_PRODUCTION=https://ihreapotheken.de/partners/api
IA_UPDATE_PHARMACY_DATA_API_URL_PRODUCTION_KEY=

# dev, qa, production
IA_PARTNER_API_ENV=dev

IA_PARTNER_API_DEV_URL=https://dev-bss-api-partner.azurewebsites.net
IA_PARTNER_API_QA_URL=https://qa-bss-api-partner.azurewebsites.net
IA_PARTNER_API_PRODUCTION_URL=https://prod-bss-api-partner.azurewebsites.net

IA_PARTNER_API_KEY=a37946af-792a-4d5b-9eea-cced411e675d

IA_WEB_COMPONENTS_ENV=qa

CHAT_WEBCHAT_URL=
CHAT_WEBCHAT_BUCKET=webchat-chat-files

SUBSCRIPTION_SERVICE_URL=https://subscription-service.public.staging.api.gedisa.de
SUBSCRIPTION_SERVICE_TOKEN=S5ew2xHenpn3SuIl2smO6ygLiLOILmjL

CARD_LINK_SERVICE_URL=https://cardlink-service.public.staging.api.gedisa.de
CARD_LINK_SERVICE_CLIENT_ID=4b1418be-ca18-4722-b6a2-e843b5dd9abc
CARD_LINK_SERVICE_CLIENT_SECRET=YER7F158idESwvD5
CARD_LINK_SERVICE_CLIENT_ID_VENDOR=4b1418be-ca18-4722-b6a2-e843b5dd9abc
CARD_LINK_SERVICE_CLIENT_SECRET_VENDOR=YER7F158idESwvD5
CARD_LINK_APOGUIDE_CHANNEL_ID=dec19720-e403-4163-8d65-1808f0e5db04

JWT_PHARMACIES_TOKEN_PRIVATE_KEY=storage/pharmacies_token_private_key.pem
JWT_PHARMACIES_TOKEN_PLUBLIC_KEY=storage/pharmacies_token_public_key.pem
STRIPE_KEY=PLACEHOLDER_STRIPE_KEY
STRIPE_SECRET=PLACEHOLDER_STRIPE_SECRET
STRIPE_WEBHOOK_SECRET=PLACEHOLDER_STRIPE_WEBHOOK_SECRET
CASHIER_CURRENCY=eur
CASHIER_CURRENCY_LOCALE=de_DE

RETAX_URL=https://staging.retax.gedisa.de
