<?php

namespace Database\Seeders;

use App\Enums\PharmaceuticalsTypeEnum;
use App\Enums\Vaccinate\AgeGroupEnum;
use App\Pharmaceutical;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PharmaceuticalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Pharmaceuticals came with a migration
        // - 2020_11_20_153136_insert_into_pharmaceuticals_table.php
        // - 2022_01_13_153136_insert_new_into_pharmaceuticals_table.php
        // for Influenza pharmaceuticals we just need some updates on the table

        foreach (Pharmaceutical::query()->whereNull('type')->get() as $key => $pharmaceutical) {
            $type = PharmaceuticalsTypeEnum::VACCINE_INFLUENZA;
            $displayName = $pharmaceutical->name;
            if ($key >= 10) {
                $type = PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE;
                $displayName .= ' (high)';
            }

            $pharmaceutical->update([
                'type' => $type,
                'display_name' => $displayName,
            ]);
        }

        $this->deactivateCurrentPharmaceuticals();
        $this->updatePharmaceuticals();

    }

    protected function updatePharmaceuticals(): Collection
    {
        $pharmaceuticals = [
            [
                'name' => 'Afluria Tetra 2023/2024',
                'pzn' => 17620971,
                'display_name' => 'Afluria Tetra 2023/2024',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Efluelda 2023/2024',
                'display_name' => 'Efluelda 2023/2024 (10 Stück, high)',
                'pzn' => 18190107,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE,
            ],
            [
                'name' => 'Efluelda 2023/2024',
                'display_name' => 'Efluelda 2023/2024 (1 Stück, high)',
                'pzn' => 18190099,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE,
            ],
            [
                'name' => 'Fluad Tetra 2023/2024',
                'display_name' => 'Fluad Tetra 2023/2024 (1x0,5 ml)',
                'pzn' => 18230769,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Fluad Tetra 2023/2024',
                'display_name' => 'Fluad Tetra 2023/2024 (10x0,5 ml)',
                'pzn' => 18230775,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Flucelvax 2023/2024',
                'display_name' => 'Flucelvax 2023/2024 (10x0,5 ml o.K.)',
                'pzn' => 18230812,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Flucelvax 2023/2024',
                'display_name' => 'Flucelvax 2023/2024 (10x0,5 ml m.K.)',
                'pzn' => 18230806,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Flucelvax 2023/2024',
                'display_name' => 'Flucelvax 2023/2024 (1x0,5 ml m.K.)',
                'pzn' => 18230798,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Influsplit Tetra 2023/2024',
                'display_name' => 'Influsplit Tetra 2023/2024 (1x0,5 ml)',
                'pzn' => 183653555,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Influsplit Tetra 2023/2024',
                'display_name' => 'Influsplit Tetra 2023/2024 (10x0,5 ml)',
                'pzn' => 18353561,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Influvac Tetra 2023/2024',
                'display_name' => 'Influvac Tetra 2023/2024 (10x0,5 ml o.K.)',
                'pzn' => 18272733,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Influvac Tetra 2023/2024',
                'display_name' => 'Influvac Tetra 2023/2024 (10x0,5 ml m.K.)',
                'pzn' => 18272704,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Influvac Tetra 2023/2024',
                'display_name' => 'Influvac Tetra 2023/2024 (1x0,5 ml m.K.)',
                'pzn' => 18272696,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Vaxigrip Tetra 2023/2024',
                'display_name' => 'Vaxigrip Tetra 2023/2024 (10 St m.K.)',
                'pzn' => 18190165,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Vaxigrip Tetra 2023/2024',
                'display_name' => 'Vaxigrip Tetra 2023/2024 (1 St o.K.)',
                'pzn' => 18190136,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Vaxigrip Tetra 2023/2024',
                'display_name' => 'Vaxigrip Tetra 2023/2024 (10 St o.K.)',
                'pzn' => 18190142,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Vaxigrip Tetra 2023/2024',
                'display_name' => 'Vaxigrip Tetra 2023/2024 (20 St o.K.)',
                'pzn' => 18190159,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
            [
                'name' => 'Xanaflu Tetra 2023/2024',
                'display_name' => 'Xanaflu Tetra 2023/2024 (10x0,5 ml m.K.)',
                'pzn' => 18272756,
                'active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => PharmaceuticalsTypeEnum::VACCINE_INFLUENZA,
            ],
        ];

        foreach ($pharmaceuticals as $pharmaceutical) {
            $pharmaceuticalObject = Pharmaceutical::where('pzn', $pharmaceutical['pzn'])->first();
            if ($pharmaceuticalObject instanceof Pharmaceutical) {
                if ($pharmaceutical['name'] === 'Efluelda 2023/2024') {
                    $pharmaceuticalObject->min_agegroup = AgeGroupEnum::AGE_60_69;
                } else {
                    $pharmaceuticalObject->max_agegroup = AgeGroupEnum::AGE_50_59;
                }
                $pharmaceuticalObject->active = true;
                $pharmaceuticalObject->save();
            } else {
                if ($pharmaceutical['name'] === 'Efluelda 2023/2024') {
                    $pharmaceutical['min_agegroup'] = AgeGroupEnum::AGE_60_69;
                } else {
                    $pharmaceutical['max_agegroup'] = AgeGroupEnum::AGE_50_59;
                }
                Pharmaceutical::create($pharmaceutical);
            }
        }

        return collect($pharmaceuticals);
    }

    protected function deactivateCurrentPharmaceuticals(): void
    {
        DB::table('pharmaceuticals')
            ->whereIn('type', [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA, PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE])
            ->update([
                'active' => 0,
            ]);
    }
}
