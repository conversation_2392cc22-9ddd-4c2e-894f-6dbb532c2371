<?php

namespace Database\Seeders;

use App\BillingAddress;
use App\Pharmacy;
use Illuminate\Database\Seeder;

class BillingAddressSeeder extends Seeder
{
    public function run(): void
    {
        $dispatcher = Pharmacy::getEventDispatcher();
        Pharmacy::unsetEventDispatcher();

        Pharmacy::eachById(function (Pharmacy $pharmacy) {
            $billing_adresses = BillingAddress::factory()->create([
                'uuid' => $pharmacy->uuid,
                'user_id' => $pharmacy->owner()->id,
            ]);

            $pharmacy->billing_address_id = $billing_adresses->id;
            $pharmacy->save();
        });

        Pharmacy::setEventDispatcher($dispatcher);
    }
}
