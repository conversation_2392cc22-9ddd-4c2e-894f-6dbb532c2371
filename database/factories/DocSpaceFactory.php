<?php

namespace Database\Factories;

use App\DocSpace;
use App\Enums\DocSpaceRetentionDuration;
use App\Enums\DocSpaceType;
use App\Pharmacy;
use Illuminate\Database\Eloquent\Factories\Factory;

class DocSpaceFactory extends Factory
{
    protected $model = DocSpace::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'pharmacy_id' => Pharmacy::factory(),
            'name' => $this->faker->pharmacyName.' Doc Space',
            'suffix' => rtrim(base64_encode(sprintf('%04d', 1)), '=').'-'.now()->timestamp,
            'description' => $this->faker->text(100),
            'sdr_doc_space_id' => $this->faker->uuid,
            'soft_quota' => 4.5 * 1024,
            'hard_quota' => 5 * 1024,
            'max_upload_file_size' => 10,
            'retention_duration' => $this->faker->randomElement([
                DocSpaceRetentionDuration::NONE->value,
                DocSpaceRetentionDuration::ONE_MONTH->value,
                DocSpaceRetentionDuration::SEVEN_YEARS->value,
                DocSpaceRetentionDuration::TEN_YEARS->value,
            ]),
            'allowed_file_types' => '*/*',
            'encrypted' => true,
            'type' => $this->faker->randomElement([
                DocSpaceType::NORMAL->value,
                DocSpaceType::REVISION_SAFE->value,
            ]),
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}
