<?php

namespace Database\Factories;

use App\Enums\PharmacyAddressTypeEnum;
use App\PharmacyAddress;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PharmacyAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PharmacyAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'type' => PharmacyAddressTypeEnum::HOME,
            'city' => 'Münster',
            'postcode' => rand(10000, 99999),
            'street' => $this->faker->streetName,
            'house_number' => rand(1, 9999).((bool) rand(0, 5) ? Str::random(1) : ''),
            'latitude' => floatval(rand(5240000000, 5260000000) / 100000000),
            'longitude' => floatval(rand(1340000000, 1350000000) / 100000000),
        ];
    }
}
