<?php

use App\Domains\Subscription\Application\Settings\Products\KimInstallProductSetting;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add(KimInstallProductSetting::group().'.product_id', 'prod_SBJGZys0Tl7kHa');
        $this->migrator->add(KimInstallProductSetting::group().'.recurring_price_id', 'price_1RGweAE7mrGmlRDBQZIiS0b9');
        $this->migrator->add(KimInstallProductSetting::group().'.one_time_price_id', 'price_1RGweAE7mrGmlRDBQZIiS0b9');
    }
};
