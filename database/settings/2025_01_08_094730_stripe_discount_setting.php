<?php

use App\Domains\Subscription\Application\Settings\Discounts\BaseSubscriptionAssociationPaidDiscountSetting;
use App\Domains\Subscription\Application\Settings\Discounts\CardLinkPartnerPharmacyDiscountSetting;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add(CardLinkPartnerPharmacyDiscountSetting::group().'.discount_id', 'NMc9HOdN');
        $this->migrator->add(BaseSubscriptionAssociationPaidDiscountSetting::group().'.discount_id', 'BabWnXNc');
    }
};
