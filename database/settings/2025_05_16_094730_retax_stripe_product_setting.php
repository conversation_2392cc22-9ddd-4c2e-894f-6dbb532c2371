<?php

use App\Domains\Subscription\Application\Settings\Products\RetaxProductSetting;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add(RetaxProductSetting::group().'.product_id', 'prod_SJxmkkN9BH3VJp');
        $this->migrator->add(RetaxProductSetting::group().'.recurring_price_id', 'price_');
        $this->migrator->add(RetaxProductSetting::group().'.one_time_price_id', 'price_');
    }
};
