<?php

use Illuminate\Database\Migrations\Migration;

class CreateCalendarTopics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $topics = [
            'Erweiterte Medikationsberatung bei Polymedikation',
            'Pharmazeutische Betreuung von Organtransplantierten',
            'Pharmazeutische Betreuung bei oraler Antitumortherapie',
            'Standardisierte Risikoerfassung hoher Blutdruck',
            'Standardisierte Einweisung in die korrekte Arzneimittelanwendung und Üben der Inhalationstechnik',
            'Grippeschutzimpfung',
            'Covid-19-Impfung',
            'Covid-19-Schnelltest',
        ];

        foreach ($topics as $topic) {
            \App\CalendarTopic::create([
                'name' => $topic,
                'is_public' => true,
            ]);
        }
    }
}
