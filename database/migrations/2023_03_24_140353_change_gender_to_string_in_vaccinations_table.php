<?php

use App\Enums\Vaccinate\GenderEnum;
use App\Vaccination;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('vaccinations', function (Blueprint $table) {
            $table->string('gender')->nullable()->change();
        });

        DB::transaction(function () {
            Vaccination::eachById(function ($vaccination) {
                if ($vaccination->gender === 0) {
                    $vaccination->gender = GenderEnum::MALE;
                } elseif ($vaccination->gender === 1) {
                    $vaccination->gender = GenderEnum::FEMALE;
                } elseif ($vaccination->gender === 2) {
                    $vaccination->gender = GenderEnum::DIVERSE;
                }
                $vaccination->save();
            });
        });
    }

    public function down()
    {
        // Do nothing to avoid exceptions.
    }
};
