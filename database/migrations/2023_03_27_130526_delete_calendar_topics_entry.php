<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public $topics = [
        'Ohrlochstechen',
    ];

    public function up()
    {
        \App\CalendarTopic::query()->whereIn('name', $this->topics)->delete();
    }

    public function down()
    {
        foreach ($this->topics as $topic) {
            \App\CalendarTopic::create([
                'name' => $topic,
                'is_public' => true,
            ]);
        }
    }
};
