<?php

use App\BrochureCode;
use App\Enums\SalutationEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        DB::transaction(function () {
            BrochureCode::eachById(function ($brochureCode) {
                $brochureCode->salutation = $brochureCode->salutation === 'Herr' ? SalutationEnum::MR : SalutationEnum::MS;
                $brochureCode->save();
            });
        });
    }

    public function down()
    {
        DB::transaction(function () {
            BrochureCode::eachById(function ($brochureCode) {
                $brochureCode->salutation = $brochureCode->salutation === SalutationEnum::MR ? 'Herr' : 'Frau';
                $brochureCode->save();
            });
        });
    }
};
