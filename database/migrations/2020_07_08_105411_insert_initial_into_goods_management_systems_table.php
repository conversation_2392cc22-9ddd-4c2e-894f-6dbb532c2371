<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class InsertInitialIntoGoodsManagementSystemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('goods_management_systems')->insert([
            [
                'company' => 'ADG - Apotheken - Dienstleistungsgesellschaft mbH',
                'system_id' => 'WA0001',
                'system_name' => 'S3000',
                'tag' => 'ADG_S30',
                'city' => 'Mannheim',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'ADG - Apotheken - Dienstleistungsgesellschaft mbH',
                'system_id' => 'WA0001',
                'system_name' => 'A3000',
                'tag' => 'ADG_A30',
                'city' => 'Mannheim',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'ADG - Apotheken - Dienstleistungsgesellschaft mbH',
                'system_id' => 'WA0001',
                'system_name' => 'ADG Wawi',
                'tag' => 'ADG_WWI',
                'city' => 'Mannheim',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'ADV - AD Apotheken Datenverarbeitung GmbH & CoKG',
                'system_id' => 'WA0002',
                'system_name' => 'GAWIS',
                'tag' => 'ADV_GAW',
                'city' => 'Oberhausen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'awinta GmbH(Noventi)',
                'system_id' => 'WA0003',
                'system_name' => 'awintaONE',
                'tag' => 'AWI_AWO',
                'city' => 'Bietigheim - Bissingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'awinta GmbH(Noventi)',
                'system_id' => 'WA0003',
                'system_name' => 'PROKAS',
                'tag' => 'AWI_PRO',
                'city' => 'Bietigheim - Bissingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'awinta GmbH(Noventi)',
                'system_id' => 'WA0003',
                'system_name' => 'INFOPHARM',
                'tag' => 'AWI_IPH',
                'city' => 'Bietigheim - Bissingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'awinta GmbH(Noventi)',
                'system_id' => 'WA0003',
                'system_name' => 'jump',
                'tag' => 'AWI_JUM',
                'city' => 'Bietigheim - Bissingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'awinta GmbH(Noventi)',
                'system_id' => 'WA0003',
                'system_name' => 'PHARMASOFT',
                'tag' => 'AWI_PHS',
                'city' => 'Bietigheim - Bissingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'CIDA Computerleistungen für Apotheken GmbH',
                'system_id' => 'WA0004',
                'system_name' => 'CIDAnova - Plus',
                'tag' => 'CID_CNP',
                'city' => 'Darmstadt',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'CIDA Computerleistungen für Apotheken GmbH',
                'system_id' => 'WA0004',
                'system_name' => 'CORA',
                'tag' => 'CID_COR',
                'city' => 'Darmstadt',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'LAUER - FISCHER GmbH(CGM)',
                'system_id' => 'WA0005',
                'system_name' => 'WINAPO ux',
                'tag' => 'LAF_WIN',
                'city' => 'Fürth',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'OPTIPHARM Plus GmbH',
                'system_id' => 'WA0006',
                'system_name' => 'Optipharm Plus',
                'tag' => 'OPP_OPP',
                'city' => 'Oberhausen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Pharmatechnik GmbH & Co . KG',
                'system_id' => 'WA0007',
                'system_name' => 'IXOS',
                'tag' => 'PHT_IXO',
                'city' => 'Starnberg',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Pharmatechnik GmbH & Co . KG',
                'system_id' => 'WA0007',
                'system_name' => 'XT',
                'tag' => 'PHT_XT',
                'city' => 'Starnberg',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'PRISMA DATENSYSTEME GmbH',
                'system_id' => 'WA0008',
                'system_name' => 'aposoft',
                'tag' => 'PDS_APS',
                'city' => 'Moomerland',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apotechnik GmbH',
                'system_id' => 'WU0009',
                'system_name' => 'APOOFFICE',
                'tag' => 'APT_APO',
                'city' => 'Obermarchtal',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'DV Nordrhein',
                'system_id' => 'WU0010',
                'system_name' => 'UniPharm',
                'tag' => 'DVN_UPH',
                'city' => 'Dinslaken',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Avp Service AG - Department ApoComp',
                'system_id' => 'WU0011',
                'system_name' => 'ApoComp WinSQL',
                'tag' => 'AVP_WSQ',
                'city' => 'Düsseldorf',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'DataSoftPro e . K . -Apothekensoftware',
                'system_id' => 'WU0012',
                'system_name' => 'Meine WaWi',
                'tag' => 'DSP_MWW',
                'city' => 'Karlsfeld',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Christian Wernicke Software',
                'system_id' => 'WU0013',
                'system_name' => 'Pharma Warenwirtschaft 2.0',
                'tag' => 'CWS_PW2',
                'city' => 'Nordkirchen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Comsol AG',
                'system_id' => 'WU0014',
                'system_name' => 'Comsol DynApo',
                'tag' => 'CAG_CDA',
                'city' => 'Köln',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'DEOS Software GmbH',
                'system_id' => 'WU0015',
                'system_name' => 'Secret',
                'tag' => 'DEO_SEC',
                'city' => 'Burladingen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'foxpert Gesundheitssysteme GmbH',
                'system_id' => 'WU0016',
                'system_name' => 'PC-AS',
                'tag' => 'FGS_PCA',
                'city' => 'Norderstedt',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Hagedorn Bürotronic',
                'system_id' => 'WU0017',
                'system_name' => 'ApoComp',
                'tag' => 'HBT_APC',
                'city' => 'Düsseldorf',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Ingenieurbüro Tröster',
                'system_id' => 'WU0018',
                'system_name' => 'Warenwirtschaft',
                'tag' => 'IBT_WWI',
                'city' => 'Bad Dürrheim',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Kyberg Pharma Vertriebs GmbH',
                'system_id' => 'WU0019',
                'system_name' => 'dsp / 3',
                'tag' => 'KPV_DSP',
                'city' => 'Oberhaching',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'PharmaHera Service GmbH',
                'system_id' => 'WU0020',
                'system_name' => 'Aponeo Logistic Tools',
                'tag' => 'PHS_ALT',
                'city' => 'Berlin',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('goods_management_systems')->whereIn('tag', [
            'ADG_S30',
            'ADG_A30',
            'ADG_WWI',
            'ADV_GAW',
            'AWI_AWO',
            'AWI_PRO',
            'AWI_IPH',
            'AWI_JUM',
            'AWI_PHS',
            'CID_CNP',
            'CID_COR',
            'LAF_WIN',
            'OPP_OPP',
            'PHT_IXO',
            'PHT_XT',
            'PDS_APS',
            'APT_APO',
            'DVN_UPH',
            'AVP_WSQ',
            'DSP_MWW',
            'CWS_PW2',
            'CAG_CDA',
            'DEO_SEC',
            'FGS_PCA',
            'HBT_APC',
            'IBT_WWI',
            'KPV_DSP',
            'PHS_ALT',
        ])->delete();
    }
}
