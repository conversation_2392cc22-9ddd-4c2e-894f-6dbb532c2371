<?php

use App\AssociationMembershipHistory;
use App\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(static function () {
            User::whereDoesntHave('currentAssociationMembershipHistory')
                ->whereHas('brochureCode')
                ->whereHas('pharmacyProfile')
                ->eachById(function (User $user) {
                    $startedAt = $user->associationMembershipHistories->isEmpty()
                        ? $user->created_at
                        : $user->associationMembershipHistories->last()->terminated_at->addDay();

                    AssociationMembershipHistory::create([
                        'user_id' => $user->id,
                        'association_id' => $user->pharmacyProfile->association_id,
                        'started_at' => $startedAt,
                    ]);
                });
        });
    }

    public function down(): void
    {
        Schema::table('association_membership_histories', function (Blueprint $table) {
            //
        });
    }
};
