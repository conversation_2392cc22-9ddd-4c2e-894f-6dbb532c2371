<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('covid_vaccinations', 'vaccination_import_id')) {
            if (DB::getDriverName() !== 'sqlite') {
                Schema::table('covid_vaccinations', function (Blueprint $table) {
                    $table->dropForeign(['vaccination_import_id']);
                });
            }

            Schema::table('covid_vaccinations', function (Blueprint $table) {
                $table->dropColumn('vaccination_import_id');
            });
        }
    }
};
