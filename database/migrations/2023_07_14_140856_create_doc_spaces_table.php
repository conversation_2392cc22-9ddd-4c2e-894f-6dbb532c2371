<?php

use App\Enums\DocSpaceRetentionDuration;
use App\Enums\DocSpaceType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('doc_spaces', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('additional_description')->nullable();
            $table->string('sdr_doc_space_id')->index();
            $table->integer('soft_quota');
            $table->integer('hard_quota');
            $table->integer('current_usage')->default(0);
            $table->integer('max_upload_file_size')->default(10);
            $table->string('retention_duration')->default(DocSpaceRetentionDuration::NONE->value);
            $table->json('allowed_file_types')->nullable();
            $table->boolean('encrypted')->default(true);
            $table->enum('type', DocSpaceType::getCases())->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('doc_spaces');
    }
};
