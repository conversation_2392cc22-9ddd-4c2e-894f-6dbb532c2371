<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('registration_requests', function (Blueprint $table) {
            $table->foreignId('association_id')
                ->nullable()
                ->after('telematics_id')
                ->constrained()
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('registration_requests', function (Blueprint $table) {
            $table->dropForeign('registration_requests_association_id_foreign');

            $table->dropColumn('association_id');
        });
    }
};
