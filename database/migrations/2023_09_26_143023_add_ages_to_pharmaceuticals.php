<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (! Schema::hasColumns('pharmaceuticals', ['min_agegroup', 'max_agegroup'])) {
            Schema::table('pharmaceuticals', function (Blueprint $table) {
                $table->integer('min_agegroup')->nullable();
                $table->integer('max_agegroup')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumns('pharmaceuticals', ['min_agegroup', 'max_agegroup'])) {
            Schema::table('pharmaceuticals', function (Blueprint $table) {
                $table->dropColumn(['min_agegroup', 'max_agegroup']);
            });
        }
    }
};
