<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePharmacyStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pharmacy_statistics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->index()->constrained()->cascadeOnDelete();
            $table->integer('vaccination_import_total')->default(0);
            $table->integer('vaccination_import_without_vaccination_id')->default(0);
            $table->integer('vaccination_import_with_vaccination_id')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pharmacy_statistics');
    }
}
