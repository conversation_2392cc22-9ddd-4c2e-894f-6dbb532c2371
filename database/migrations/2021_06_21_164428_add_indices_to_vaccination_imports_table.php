<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndicesToVaccinationImportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->index('created_at');
            $table->index('accounting_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->dropIndex('vaccination_imports_created_at_index');
            $table->dropIndex('vaccination_imports_accounting_type_index');
        });
    }
}
