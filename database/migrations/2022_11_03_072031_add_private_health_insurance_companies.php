<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPrivateHealthInsuranceCompanies extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('health_insurance_companies', function (Blueprint $table) {
            $table->boolean('is_private')->default(false);
        });

        foreach (['Private Krankenversicherung', 'Beihilfe versichert'] as $item) {
            \App\HealthInsuranceCompany::create([
                'name' => $item,
                'vaccinate_enabled' => true,
                'is_private' => true,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
