<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeNewIndexOnVaccinationImportsTableForType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->index(['is_recovered', 'is_recovered_only']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->dropIndex(['is_recovered', 'is_recovered_only']);
        });
    }
}
