<?php

use App\Enums\ApomailStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apomails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id')->constrained('users')->cascadeOnDelete();
            $table->string('status')->default(ApomailStatus::RESERVED);
            $table->string('email')->unique();
            $table->string('alternative_email')->nullable();
            $table->foreignId('idp_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('idp_user_uuid')->nullable();
            $table->string('idp_user_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apomails');
    }
};
