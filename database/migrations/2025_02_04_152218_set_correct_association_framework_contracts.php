<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \App\Association::eachById(function (\App\Association $association) {
            $contact = match ($association->id) {
                \App\Enums\AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V, null => \App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum::NoAssociationFrameworkContract,
                \App\Enums\AssociationEnum::LANDESAPOTHEKERVERBAND_BADEN_WUERTTEMBERG_E_V, \App\Enums\AssociationEnum::BREMER_APOTHEKERVERBAND_E_V, \App\Enums\AssociationEnum::LANDESAPOTHEKERVERBAND_NIEDERSACHSEN_E_V, \App\Enums\AssociationEnum::THUERINGER_APOTHEKERVERBAND_E_V => \App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum::BaseAssociationFrameworkContract,
                default => \App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum::PlusAssociationFrameworkContract,
            };

            $association->update(['framework_contract' => $contact]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
