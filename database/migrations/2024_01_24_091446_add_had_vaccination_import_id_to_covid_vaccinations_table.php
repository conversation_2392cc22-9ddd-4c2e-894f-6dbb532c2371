<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('covid_vaccinations', function (Blueprint $table) {
            $table->boolean('had_vaccination_import_id')->default(false);
        });

        if (Schema::hasColumn('covid_vaccinations', 'vaccination_import_id')) {
            DB::transaction(function () {
                DB::table('covid_vaccinations')
                    ->whereNotNull('vaccination_import_id')
                    ->update(['had_vaccination_import_id' => true]);
            });
        }
    }
};
