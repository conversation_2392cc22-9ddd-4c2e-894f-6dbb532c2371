<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRecoveredCertificateStatusToPharmacySettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pharmacy_settings', function (Blueprint $table) {
            $table->unsignedTinyInteger('recovered_certificate_status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pharmacy_settings', function (Blueprint $table) {
            $table->dropColumn('recovered_certificate_status');
        });
    }
}
