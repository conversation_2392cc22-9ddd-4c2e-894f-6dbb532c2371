<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('brochure_codes', function (Blueprint $table) {
            $table->string('email')->after('city')->nullable();
            $table->string('phone')->after('email')->nullable();
            $table->boolean('is_company')->after('phone')->default(false);
            $table->dateTime('reg_link_sent_at')->after('is_company')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('brochure_codes', function (Blueprint $table) {
            $table->dropColumn('reg_link_sent_at');
            $table->dropColumn('is_company');
            $table->dropColumn('phone');
            $table->dropColumn('email');
        });
    }
};
