<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddForeignKeysForGoodsManagementSystemsAndAccountingCentersToPharmaciesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pharmacies', function (Blueprint $table) {
            $table->foreign('goods_management_system_id')
                ->references('id')
                ->on('goods_management_systems')
                ->onDelete('set null');

            $table->foreign('accounting_center_id')
                ->references('id')
                ->on('accounting_centers')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pharmacies', function (Blueprint $table) {
            $table->dropForeign('pharmacies_goods_management_system_id_foreign');
            $table->dropForeign('pharmacies_accounting_center_id_foreign');
        });
    }
}
