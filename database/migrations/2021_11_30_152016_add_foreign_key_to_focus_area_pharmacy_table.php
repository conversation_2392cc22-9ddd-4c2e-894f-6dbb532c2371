<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddForeignKeyToFocusAreaPharmacyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('focus_area_pharmacy', function (Blueprint $table) {
            $table->foreign('focus_area_id')
                ->references('id')
                ->on('focus_areas')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('focus_area_pharmacy', function (Blueprint $table) {
            $table->dropForeign(['focus_area_id']);
        });
    }
}
