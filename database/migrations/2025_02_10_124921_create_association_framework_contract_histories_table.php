<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('association_framework_contract_histories', function (Blueprint $table) {
            $table->id();

            $table->string('contract');

            $table->date('starts_at');
            $table->date('ends_at');

            $table->foreignId('association_id')->constrained()->cascadeOnDelete();
            $table->foreignId('staff_id')->nullable()->constrained()->nullOnDelete();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('association_framework_contract_histories');
    }
};
