<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('kim_addresses', function (Blueprint $table) {
            $table->uuid('cancellation_id')->nullable()->after('additional');
            $table->string('cancellation_reason')->nullable()->after('cancellation_id');
        });
    }

    public function down(): void
    {
        Schema::table('kim_addresses', function (Blueprint $table) {
            $table->dropColumn('cancellation_id');
            $table->dropColumn('cancellation_reason');
        });
    }
};
