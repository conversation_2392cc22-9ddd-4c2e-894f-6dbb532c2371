<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class InsertInitialIntoAssociationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('associations')->insert([
            [
                'name' => 'Apothekerverband Brandenburg e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Apothekerverband Mecklenburg-Vorpommern e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Apothekerverband Nordrhein e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Apothekerverband Rheinland-Pfalz e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Apothekerverband Westfalen-Lippe e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Apothekerverband Schleswig-Holstein e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Bayerischer Apothekerverband e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Berliner Apotheker-Verein',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Bremer Apothekerverband e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Hamburger Apothekerverein e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Hessischer Apothekerverband e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Landesapothekerverband Baden-Württemberg e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Landesapothekerverband Niedersachsen e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Landesapothekerverband Sachsen-Anhalt e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Saarländischer Apothekerverein e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Sächsischer Apothekerverband e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Thüringer Apothekerverband e.V.',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('associations')->whereIn('name', [
            'Apothekerverband Brandenburg e.V.',
            'Apothekerverband Mecklenburg-Vorpommern e.V.',
            'Apothekerverband Nordrhein e.V.',
            'Apothekerverband Rheinland-Pfalz e.V.',
            'Apothekerverband Westfalen-Lippe e.V.',
            'Apothekerverband Schleswig-Holstein e.V.',
            'Bayerischer Apothekerverband e.V.',
            'Berliner Apotheker-Verein',
            'Bremer Apothekerverband e.V.',
            'Hamburger Apothekerverein e.V.',
            'Hessischer Apothekerverband e.V.',
            'Landesapothekerverband Baden-Württemberg e.V.',
            'Landesapothekerverband Niedersachsen e.V.',
            'Landesapothekerverband Sachsen-Anhalt e.V.',
            'Saarländischer Apothekerverein e.V.',
            'Sächsischer Apothekerverband e.V.',
            'Thüringer Apothekerverband e.V.',
        ])->delete();
    }
}
