<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropEntityNewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('entity_news');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('entity_news', function (Blueprint $table) {
            $table->id();

            $table->string('slug')->unique();
            $table->string('title');
            $table->text('excerpt');
            $table->text('text');
            $table->unsignedTinyInteger('status');
            $table->timestamp('release_date');

            $table->unsignedBigInteger('entity_newsable_id');
            $table->string('entity_newsable_type');

            $table->timestamps();
        });
    }
}
