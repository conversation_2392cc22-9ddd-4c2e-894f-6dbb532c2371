<?php

use App\Subscription;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddStartedAtFieldToSubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->date('started_at')->after('cycle_ends_at')->nullable();
        });

        // removed because base table has been changed
        //        Subscription::query()->whereNull('started_at')->update([
        //            'started_at' => DB::raw('cycle_started_at'),
        //        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('started_at');
        });
    }
}
