<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBillingAddressIdToPharmaciesTable extends Migration
{
    public function up(): void
    {
        Schema::table('pharmacies', static function (Blueprint $table) {
            $table->foreignId('billing_address_id')->nullable()->constrained('billing_addresses');
        });
    }

    public function down(): void
    {
        Schema::table('pharmacies', static function (Blueprint $table) {
            $table->dropForeign(['billing_address_id']);
            $table->dropColumn('billing_address_id');
        });
    }
}
