<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCovidVaccinationIdToVaccinationImportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->foreignId('covid_vaccination_id')->nullable()->constrained()->nullOnDelete();
            $table->index(['pharmacy_id', 'covid_vaccination_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->dropForeign(['covid_vaccination_id']);
            $table->dropColumn('covid_vaccination_id');
        });
    }
}
