<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('brochure_codes', function (Blueprint $table) {
            $table->string('company_name')->nullable()->after('is_company');
            $table->string('telematics_id')->nullable()->after('company_name');
        });
    }

    public function down()
    {
        Schema::table('brochure_codes', function (Blueprint $table) {
            $table->dropColumn('company_name');
            $table->dropColumn('telematics_id');
        });
    }
};
