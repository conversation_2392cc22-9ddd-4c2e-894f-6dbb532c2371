<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('idp_email')->after('email')->nullable();
        });

        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('search_name');
            });
            Schema::table('users', function (Blueprint $table) {
                $table->string('search_name')->virtualAs("SUBSTRING(TRIM(CONCAT(COALESCE(title,''), ' ', COALESCE(first_name,''), ' ', COALESCE(last_name,''), ' (', TRIM(CONCAT(COALESCE(email,''), ' ')), ')', ' (', TRIM(CONCAT(COALESCE(idp_email,''), ' ')), ')')), 1, 255)");
            });
        }

        foreach (\App\User::query()
            ->whereHas('pharmacyProfile')
            ->get() as $user) {
            $user->update([
                'idp_email' => $user->email,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('search_name');
            });
            Schema::table('users', function (Blueprint $table) {
                $table->string('search_name')->virtualAs("SUBSTRING(TRIM(CONCAT(COALESCE(title,''), ' ', COALESCE(first_name,''), ' ', COALESCE(last_name,''), ' (', TRIM(CONCAT(COALESCE(email,''), ' ')), ')')), 1, 255)");
            });
        }

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('idp_email');
        });
    }
};
