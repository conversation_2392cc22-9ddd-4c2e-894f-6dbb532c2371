<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('association_membership_histories', function (Blueprint $table) {
            $table->dropColumn(['terminated_at', 'termination_completed_at']);
        });
    }

    public function down(): void
    {
        Schema::table('association_membership_histories', function (Blueprint $table) {
            $table->dateTime('terminated_at')->nullable();
            $table->dateTime('termination_completed_at')->nullable();
        });
    }
};
