<?php

use App\Enums\Ia\IaOrderStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('ia_orders', function (Blueprint $table) {
            $table->string('status')
                ->default(IaOrderStatusEnum::New)
                ->after('user_id');
        });
    }

    public function down(): void
    {
        Schema::table('ia_orders', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
