<?php

namespace Tests\Unit\Jobs;

use App\CovidVaccination;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Enums\VaccinationRKIStatus;
use App\InfluenzaVaccination;
use App\Jobs\SendVaccinationToRki;
use App\Vaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use RuntimeException;
use Tests\TestCase;
use Tests\Unit\Traits\UsesRKIApi;

class SendVaccinationToRkiTest extends TestCase
{
    use RefreshDatabase, UsesRKIApi;

    public function test_cannot_send_covid_vaccination_to_rki_if_no_pharmacy(): void
    {
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Vaccination has no pharmacy');

        $vaccination = Vaccination::factory()
            ->has(
                CovidVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::CREATED,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::COVID,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }

    public function test_cannot_send_already_dispatched_covid_vaccination_to_rki(): void
    {
        $this->expectExceptionMessage('This vaccination is handled by other job');

        $vaccination = Vaccination::factory()
            ->has(
                CovidVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::SENDING,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::COVID,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }

    public function test_cannot_send_failed_covid_vaccination_to_rki(): void
    {
        $this->expectExceptionMessage('This vaccination failed before');

        $vaccination = Vaccination::factory()
            ->has(
                CovidVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::FAILED,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::COVID,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }

    public function test_cannot_send_influenza_vaccination_to_rki_because_no_pharmacy(): void
    {
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Vaccination has no pharmacy');

        $vaccination = Vaccination::factory()
            ->has(
                InfluenzaVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::CREATED,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::INFLUENZA,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }

    public function test_cannot_send_already_dispatched_influenza_vaccination_to_rki(): void
    {
        $this->expectExceptionMessage('This vaccination is handled by other job');

        $vaccination = Vaccination::factory()
            ->has(
                InfluenzaVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::SENDING,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::INFLUENZA,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }

    public function test_cannot_send_failed_influenza_vaccination_to_rki(): void
    {
        $this->expectExceptionMessage('This vaccination failed before');

        $vaccination = Vaccination::factory()
            ->has(
                InfluenzaVaccination::factory([
                    'rki_status' => VaccinationRKIStatus::FAILED,
                ])
            )
            ->has(VaccinationPatient::factory())
            ->create([
                'type' => VaccinationTypeEnum::INFLUENZA,
            ]);

        $job = new SendVaccinationToRki($vaccination);
        $job->handle();
    }
}
