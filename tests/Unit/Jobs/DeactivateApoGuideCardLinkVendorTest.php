<?php

namespace Tests\Unit\Jobs;

use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Helper\CardLinkOrderHelper;
use App\Jobs\DeactivateApoGuideCardLinkVendor;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class DeactivateApoGuideCardLinkVendorTest extends TestCase
{
    public function test_it_does_not_request_deactivation_if_apoguide_is_not_activated(): void
    {
        Queue::fake();

        $data = [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'package' => CardLinkPackageEnum::cases()[array_rand(CardLinkPackageEnum::cases())]->value,
                'show_in_apoguide' => false,
                'activate_apo_guide_vendor' => false,
            ],
        ];

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $owner->id, $data);

        dispatch_sync(new DeactivateApoGuideCardLinkVendor($cardLinkOrder));

        Queue::assertPushed(DeactivateApoGuideCardLinkVendor::class);
        $this->assertFalse(CardLinkOrderHelper::orderInformation($cardLinkOrder)->activateApoGuideVendor);
    }

    public function test_it_queues_deactivation_request_on_terms_declined(): void
    {
        Queue::fake();

        $data = [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'package' => CardLinkPackageEnum::cases()[array_rand(CardLinkPackageEnum::cases())]->value,
                'show_in_apoguide' => true,
                'activate_apo_guide_vendor' => true,
            ],
        ];

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createCardLinkOrder($pharmacy->id, $owner->id, $data);
        $pharmacy->acceptTerms(false);

        Queue::assertPushed(DeactivateApoGuideCardLinkVendor::class);
    }

    public function test_it_queues_deactivation_request_on_deleting_pharmacy(): void
    {
        Queue::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createCardLinkOrder($pharmacy->id, $owner->id);
        $pharmacy->delete();

        Queue::assertPushed(DeactivateApoGuideCardLinkVendor::class);
    }
}
