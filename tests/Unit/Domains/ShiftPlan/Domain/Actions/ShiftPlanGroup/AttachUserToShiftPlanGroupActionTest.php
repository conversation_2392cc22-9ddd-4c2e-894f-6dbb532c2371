<?php

namespace Tests\Unit\Domains\ShiftPlan\Domain\Actions\ShiftPlanGroup;

use App\Domains\ShiftPlan\Domain\Actions\ShiftPlanGroup\AddUserToShiftPlanGroupAction;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/** @group shiftplan */
class AttachUserToShiftPlanGroupActionTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_attaches_a_user_to_a_shift_plan_group(): void
    {
        $user = $this->createPharmacyUser();

        $shiftPlan = ShiftPlan::factory()->create([
            'owner_id' => $user->id,
        ]);

        $group = ShiftPlanGroup::factory()->create([
            'shift_plan_id' => $shiftPlan->id,
        ]);

        $resultingDto = AddUserToShiftPlanGroupAction::execute($user, $group);

        $this->assertDatabaseCount('shift_plan_group_user', 1);
        $this->assertCount(1, $group->shiftPlanGroupUsers);

        $this->assertDatabaseHas('shift_plan_group_user', [
            'id' => $resultingDto->id,
            'user_id' => $user->id,
            'shift_plan_group_id' => $group->id,
        ]);

        $pivot = ShiftPlanGroupUser::find($resultingDto->id);

        $this->assertSame($pivot->uuid, $resultingDto->uuid);
        $this->assertNotNull($pivot->uuid, $resultingDto->uuid);
        $this->assertSame($pivot->user_id, $resultingDto->user_id);
        $this->assertSame($pivot->shift_plan_group_id, $resultingDto->shift_plan_group_id);
        $this->assertSame($pivot->id, $resultingDto->id);

    }
}
