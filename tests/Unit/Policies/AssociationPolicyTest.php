<?php

namespace Tests\Unit\Policies;

use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Policies\AssociationPolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssociationPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_support_has_no_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new AssociationPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_editor_has_no_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new AssociationPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new AssociationPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new AssociationPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operations_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new AssociationPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operations_has_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new AssociationPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operations_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new AssociationPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_retax_access(): void
    {
        $association = $this->getAssociationWithContract(AssociationFrameworkContractEnum::BaseAssociationFrameworkContract);
        [$user] = $this->createAssociationUser($association);
        $this->assertFalse($user->can('index-retax', $association));

        $association = $this->getAssociationWithContract(AssociationFrameworkContractEnum::PlusAssociationFrameworkContract);
        $user->associationProfile()->update(['association_id' => $association->id]);
        $association->assignUser($user, AssociationRoleEnum::ADMIN);
        $association->refresh();
        $this->assertTrue($user->can('index-retax', $association));

        $association->unassignUser($user);
        $this->assertFalse($user->can('index-retax', $association));
        $association->assignUser($user, AssociationRoleEnum::EMPLOYEE);
        $this->assertTrue($user->can('index-retax', $association));
    }
}
