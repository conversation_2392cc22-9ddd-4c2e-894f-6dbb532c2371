<?php

namespace Tests\Unit\Policies;

use App\Association;
use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\AssociationRoleEnum;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\PharmacyVaccinateStatusEnum;
use App\Enums\Settings\PharmacySettingTypes;
use App\HealthInsuranceCompany;
use App\Pharmacy;
use App\Policies\InfluenzaVaccinationPolicy;
use App\User;
use App\UserAssociationProfile;
use App\UserPharmacyProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InfluenzaVaccinationPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_vaccinate_activate(): void
    {
        $influenzaVaccinationPolicy = new InfluenzaVaccinationPolicy;
        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::factory()->create();
        /** @var Association $association */
        $association = Association::factory()->create();
        $association->settings->can_vaccinate = true;
        $association->settings->save();
        /** @var User $user */
        $pharmacyUser = User::factory()->create();
        /** @var User $associationUser */
        $associationUser = User::factory()->create();
        UserAssociationProfile::create([
            'user_id' => $associationUser->id,
        ]);
        $association->assignUser($associationUser, AssociationRoleEnum::ADMIN);

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser, $pharmacy));
        UserPharmacyProfile::create([
            'user_id' => $pharmacyUser->id,
        ]);

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser, $pharmacy));

        $pharmacy->assignUser($pharmacyUser, PharmacyRoleEnum::OWNER);

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser, $pharmacy));

        $pharmacyUser->pharmacyProfile->association_id = $association->id;
        $pharmacyUser->pharmacyProfile->save();

        $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->activate($associationUser, $pharmacy));

        $associationUser = User::factory()->create();

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser, $pharmacy));

        UserAssociationProfile::create([
            'user_id' => $associationUser->id,
        ]);

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser->refresh(), $pharmacy));

        $association->assignUser($associationUser, AssociationRoleEnum::ADMIN);

        $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->activate($associationUser->refresh(), $pharmacy));

        $association->settings->can_vaccinate = false;
        $association->settings->save();

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->activate($associationUser->refresh(), $pharmacy));
    }

    public function test_vaccinate_deactivate(): void
    {
        $influenzaVaccinationPolicy = new InfluenzaVaccinationPolicy;
        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::factory()->create();
        /** @var Association $association */
        $association = Association::factory()->create();
        $association->settings->can_vaccinate = true;
        $association->settings->save();
        /** @var User $user */
        $pharmacyUser = User::factory()->create();
        /** @var User $associationUser */
        $associationUser = User::factory()->create();
        UserAssociationProfile::create([
            'user_id' => $associationUser->id,
        ]);
        $association->assignUser($associationUser, AssociationRoleEnum::ADMIN);

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser, $pharmacy));

        UserPharmacyProfile::create([
            'user_id' => $pharmacyUser->id,
        ]);

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser, $pharmacy));

        $pharmacy->assignUser($pharmacyUser, PharmacyRoleEnum::OWNER);

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser, $pharmacy));

        $pharmacyUser->pharmacyProfile->association_id = $association->id;
        $pharmacyUser->pharmacyProfile->save();

        $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->deactivate($associationUser, $pharmacy));

        $associationUser = User::factory()->create();

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser, $pharmacy));

        UserAssociationProfile::create([
            'user_id' => $associationUser->id,
        ]);

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser->refresh(), $pharmacy));

        $association->assignUser($associationUser, AssociationRoleEnum::ADMIN);

        $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->deactivate($associationUser->refresh(), $pharmacy));

        $association->settings->can_vaccinate = false;
        $association->settings->save();

        $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->deactivate($associationUser->refresh(), $pharmacy));
    }

    public function test_vaccinate(): void
    {
        /** @var Association $association */
        $association = $this->getAssociationWithContract(AssociationFrameworkContractEnum::PlusAssociationFrameworkContract);
        $association->settings()->create(['can_vaccinate' => true]);

        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        $influenzaVaccinationPolicy = new InfluenzaVaccinationPolicy;

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $subscription = $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $subscription->delete();

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $owner->pharmacyProfile->update([
            'association_id' => $association->id,
        ]);

        $pharmacy->setGeneralSetting(
            PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT,
            PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED
        );

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner, $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner, $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($employee, $pharmacy));

        $pharmacy->reassignUser($employee, PharmacyRoleEnum::EMPLOYEE, [PharmacyPermissionsEnum::VACCINATE_INFLUENZA]);

        $this->assertTrue($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $pharmacy->unassignUser($owner);
        $pharmacy->unassignUser($subOwner);
        $pharmacy->unassignUser($employee);

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertFalse($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $pharmacy->assignUser($owner, PharmacyRoleEnum::OWNER);
        $pharmacy->assignUser($subOwner, PharmacyRoleEnum::OWNER);
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE, [PharmacyPermissionsEnum::VACCINATE_INFLUENZA]);

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $pharmacy->settings->update(['vaccinate_status' => PharmacyVaccinateStatusEnum::INACTIVE]);

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $pharmacy->settings->update(['vaccinate_status' => PharmacyVaccinateStatusEnum::REQUESTED]);

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));

        $pharmacy->settings->update(['vaccinate_status' => PharmacyVaccinateStatusEnum::ACTIVE]);

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->index($owner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($subOwner->refresh(), $pharmacy));
        $this->assertTrue($influenzaVaccinationPolicy->index($employee->refresh(), $pharmacy));
    }

    public function test_request_vaccinate(): void
    {
        $healthInsuranceCompany = HealthInsuranceCompany::factory()->create();

        /** @var Association $association */
        $association = Association::factory()->create();
        $association->healthInsuranceCompanies()->attach($healthInsuranceCompany->id);
        $association->settings->can_vaccinate = true;
        $association->settings->can_vaccinate_model = true;
        $association->settings->save();

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user->pharmacyProfile->association_id = $association->id;
        $user->pharmacyProfile->save();

        $pharmacy->settings->vaccinate_status = PharmacyVaccinateStatusEnum::INACTIVE;
        $pharmacy->settings->save();

        $influenzaVaccinationPolicy = new InfluenzaVaccinationPolicy;

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));

        $pharmacy->unassignUser($user);

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->request($user, $pharmacy));

        $pharmacy->assignUser($user, PharmacyRoleEnum::OWNER);

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));

        $user->pharmacyProfile->association_id = null;
        $user->pharmacyProfile->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->request($user, $pharmacy));

        $user->pharmacyProfile->association_id = $association->id;
        $user->pharmacyProfile->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));

        $association->settings->can_vaccinate = false;
        $association->settings->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->request($user, $pharmacy));

        $association->settings->can_vaccinate = true;
        $association->settings->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));

        $pharmacy->settings->vaccinate_status = PharmacyVaccinateStatusEnum::REQUESTED;
        $pharmacy->settings->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));

        $pharmacy->settings->vaccinate_status = PharmacyVaccinateStatusEnum::ACTIVE;
        $pharmacy->settings->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertFalse($influenzaVaccinationPolicy->request($user, $pharmacy));

        $pharmacy->settings->vaccinate_status = PharmacyVaccinateStatusEnum::INACTIVE;
        $pharmacy->settings->save();

        $pharmacy = $pharmacy->refresh();

        $this->assertTrue($influenzaVaccinationPolicy->request($user, $pharmacy));
    }
}
