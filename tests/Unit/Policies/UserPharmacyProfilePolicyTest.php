<?php

namespace Tests\Unit\Policies;

use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Policies\UserPharmacyProfilePolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserPharmacyProfilePolicyTest extends TestCase
{
    use RefreshDatabase;
    use RefreshDatabase;

    public function test_support_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_support_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_support_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_support_has_no_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_support_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_editor_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operations_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operations_has_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operations_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new UserPharmacyProfilePolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }
}
