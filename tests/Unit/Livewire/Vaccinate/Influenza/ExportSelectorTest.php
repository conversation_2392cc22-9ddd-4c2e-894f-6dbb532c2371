<?php

namespace Tests\Unit\Livewire\Vaccinate\Influenza;

use App\Jobs\GenerateInfluenzaVaccinationInvoice;
use App\Livewire\Vaccinate\Influenza\ExportSelector;
use Illuminate\Support\Facades\Queue;
use Livewire\Livewire;
use Tests\TestCase;

class ExportSelectorTest extends TestCase
{
    public function test_it_switches_options_correctly(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user);

        $livewire = Livewire::test(ExportSelector::class, ['pharmacy' => $pharmacy]);

        $livewire->assertSet('timeRangeRadio', 0);

        $livewire->assertSeeHTML('wire:submit="downloadExportsMonthly"')
            ->assertDontSeeHTML('wire:submit="downloadExportsCustomTime"');

        $livewire->set('timeRangeRadio', 1);

        $livewire->assertSeeHTML('wire:submit="downloadExportsCustomTime"')
            ->assertDontSeeHTML('wire:submit="downloadExportsMonthly"');

        $livewire->set('timeRangeRadio', 0);

        $livewire->assertSeeHTML('wire:submit="downloadExportsMonthly"')
            ->assertDontSeeHTML('wire:submit="downloadExportsCustomTime"');
    }

    public function test_it_creates_export_for_month(): void
    {
        Queue::fake();

        $now = now()->clone();
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $count = $pharmacy->refresh()->influenzaVaccinationInvoices->count();

        $this->actingAs($user);

        Livewire::test(ExportSelector::class, ['pharmacy' => $pharmacy])
            ->set('month', $now->month)
            ->set('year', $now->year)
            ->call('downloadExportsMonthly')
            ->assertHasNoErrors();

        $this->assertCount($count + 1, $pharmacy->refresh()->influenzaVaccinationInvoices);

        Queue::assertPushed(GenerateInfluenzaVaccinationInvoice::class);
    }

    public function test_it_creates_export_for_range(): void
    {
        Queue::fake();

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $count = $pharmacy->refresh()->influenzaVaccinationInvoices->count();

        $this->actingAs($user);

        Livewire::test(ExportSelector::class, ['pharmacy' => $pharmacy])
            ->set('export_start', now()->clone()->subYearNoOverflow()->format('d.m.Y'))
            ->set('export_end', now()->clone()->format('d.m.Y'))
            ->call('downloadExportsCustomTime')
            ->assertHasNoErrors();

        $this->assertCount($count + 1, $pharmacy->refresh()->influenzaVaccinationInvoices);

        Queue::assertPushed(GenerateInfluenzaVaccinationInvoice::class);
    }

    public function test_it_gets_month(): void
    {
        $livewire = new ExportSelector;

        $this->assertEmpty($livewire->getMonthRangeProperty());

        $this->assertEmpty($livewire->getMonthRangeProperty());

        $livewire->year = now()->clone()->year;

        $this->assertNotSame(collect(), $livewire->getMonthRangeProperty());
    }
}
