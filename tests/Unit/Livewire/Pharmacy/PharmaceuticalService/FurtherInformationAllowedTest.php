<?php

namespace Tests\Unit\Livewire\Pharmacy\PharmaceuticalService;

use App\Livewire\Pharmacy\PharmaceuticalServices\FurtherInformationAllowed;
use App\PharmaceuticalService;
use Livewire\Livewire;
use Tests\TestCase;

class FurtherInformationAllowedTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    public function test_further_information_allowed_can_be_checked(): void
    {
        $pdl = PharmaceuticalService::factory()->create();

        Livewire::test(FurtherInformationAllowed::class, ['pharmaceuticalService' => $pdl])
            ->set('furtherInformationAllowed', true)
            ->assertSet('furtherInformationAllowed', true);

        $pdl->refresh();

        $this->assertTrue($pdl->further_information_allowed);
    }

    public function test_further_information_allowed_can_be_unchecked(): void
    {
        $pdl = PharmaceuticalService::factory()->create();

        Livewire::test(FurtherInformationAllowed::class, ['pharmaceuticalService' => $pdl])
            ->set('furtherInformationAllowed', false)
            ->assertSet('furtherInformationAllowed', false);

        $pdl->refresh();

        $this->assertFalse($pdl->further_information_allowed);
    }
}
