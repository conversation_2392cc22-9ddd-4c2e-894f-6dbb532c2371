<?php

namespace Tests\Unit\Livewire\Pharmacy\ImportVaccinations;

use App\Association;
use App\Enums\VaccinationImport\VaccineNameEnum;
use App\Livewire\Pharmacy\ImportVaccinations\Create;
use App\Pharmacy;
use App\TelematicsId;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Livewire\Livewire;
use Tests\TestCase;

class CreateTest extends TestCase
{
    use RefreshDatabase;

    private $pharmacy;

    private $owner;

    protected function setUp(): void
    {
        parent::setUp();
        /** @var Pharmacy $pharmacy */
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->update([
            'vaccination_import' => true,
        ]);
        $pharmacy->telematicsId()->create(TelematicsId::factory()->raw());
        $owner->pharmacyProfile->update([
            'association_id' => Association::all()->first()->id,
        ]);

        $this->actingAs($owner);

        $this->pharmacy = $pharmacy;
        $this->owner = $owner;
    }

    /** @test */
    public function it_validates_the_first_name()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'firstName' => 'required',
            ])

            ->set('firstName', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'firstName' => 'regex',
            ])

            ->set('firstName', Str::random(60))
            ->call('submit')
            ->assertHasErrors([
                'firstName' => 'max',
            ])

            ->set('firstName', 'firstName')
            ->call('submit')
            ->assertHasNoErrors('firstNmae');
    }

    /** @test */
    public function it_validates_the_last_name()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'lastName' => 'required',
            ])

            ->set('lastName', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'lastName' => 'regex',
            ])

            ->set('lastName', Str::random(60))
            ->call('submit')
            ->assertHasErrors([
                'lastName' => 'max',
            ])

            ->set('lastName', 'lastName')
            ->call('submit')
            ->assertHasNoErrors('lastName');
    }

    /** @test */
    public function it_validates_the_birthdate()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'birthdate' => 'required',
            ])

            ->set('birthdate', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'birthdate' => 'date',
            ])

            ->set('birthdate', now()->addDay())
            ->call('submit')
            ->assertHasErrors([
                'birthdate' => 'before_or_equal',
            ])

            ->set('birthdate', now()->subYear())
            ->call('submit')
            ->assertHasNoErrors('birthdate');
    }

    /** @test */
    public function it_validates_the_vaccine()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'vaccine' => 'required',
            ])

            ->set('vaccine', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'vaccine' => 'in',
            ])

            ->set('vaccine', VaccineNameEnum::COMIRNATY)
            ->call('submit')
            ->assertHasNoErrors('vaccine');
    }

    /** @test */
    public function it_validates_the_dose_number()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'doseNumber' => 'required',
            ])

            ->set('doseNumber', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'doseNumber' => 'integer',
            ])

            ->set('doseNumber', 3)
            ->call('submit')
            ->assertHasErrors([
                'doseNumber' => 'max',
            ])

            ->set('doseNumber', 0)
            ->call('submit')
            ->assertHasErrors([
                'doseNumber' => 'min',
            ])

            ->set('doseNumber', 2)
            ->call('submit')
            ->assertHasNoErrors('doseNumber');
    }

    /** @test */
    public function it_validates_the_vaccination_date()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->call('submit')
            ->assertHasErrors([
                'vaccinationDate' => 'required',
            ])

            ->set('vaccinationDate', 'abc123')
            ->call('submit')
            ->assertHasErrors([
                'vaccinationDate' => 'date',
            ])

            ->set('vaccinationDate', now()->addYear())
            ->call('submit')
            ->assertHasErrors([
                'vaccinationDate' => 'before_or_equal',
            ])

            ->set('vaccinationDate', now()->setDate(2019, 1, 1))
            ->call('submit')
            ->assertHasErrors([
                'vaccinationDate' => 'after',
            ])

            ->set('vaccinationDate', now()->subDay())
            ->call('submit')
            ->assertHasNoErrors('vaccinationDate');
    }

    /** @test */
    public function it_changes_the_dose_number_dropdown_based_of_vaccine()
    {
        Livewire::test(Create::class, ['pharmacy' => $this->pharmacy])
            ->assertSet('doseOptions', [])
            ->set('vaccine', VaccineNameEnum::COMIRNATY)
            ->call('vaccineChange')
            ->assertSet('doseOptions', [1 => '1/2', 2 => '2/2'])
            ->set('vaccine', VaccineNameEnum::MODERNA)
            ->call('vaccineChange')
            ->assertSet('doseOptions', [1 => '1/2', 2 => '2/2'])
            ->set('vaccine', VaccineNameEnum::VAXZEVRIA)
            ->call('vaccineChange')
            ->assertSet('doseOptions', [1 => '1/2', 2 => '2/2'])
            ->set('vaccine', VaccineNameEnum::JANSSEN)
            ->call('vaccineChange')
            ->assertSet('doseOptions', [1 => '1/1']);
    }
}
