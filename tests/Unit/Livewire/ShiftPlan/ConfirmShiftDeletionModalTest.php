<?php

namespace Tests\Unit\Livewire\ShiftPlan;

use App\Livewire\ShiftPlan\ConfirmShiftDeletionModal;
use App\Shift;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

/** @group shiftplan */
class ConfirmShiftDeletionModalTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        [$user] = $this->createPharmacyUserWithPharmacy();

        $shiftPlan = ShiftPlan::factory()->create([
            'owner_id' => $user->id,
        ]);

        $shiftPlanGroup = ShiftPlanGroup::factory()->create([
            'shift_plan_id' => $shiftPlan->id,
        ]);

        $shiftPlanGroupUser = ShiftPlanGroupUser::factory()->create([
            'shift_plan_group_id' => $shiftPlanGroup->id,
            'user_id' => $user->id,
        ]);

        Shift::factory()->create([
            'shift_plan_id' => $shiftPlan->id,
            'shift_plan_group_user_id' => $shiftPlanGroupUser->id,
        ]);

        $this->actingAs($user);
    }

    public function test_it_sets_the_shift_id_and_name_on_delete_event(): void
    {
        $shift = Shift::first();

        Livewire::test(ConfirmShiftDeletionModal::class)
            ->dispatch('shift.delete', $shift->id)
            ->assertSet('shift.id', $shift->id)
            ->assertSet('shift.name', $shift->name);
    }

    public function test_it_confirms_shift_deletion(): void
    {
        $shift = Shift::first();

        Livewire::test(ConfirmShiftDeletionModal::class, ['shift' => $shift])
            ->call('confirmDeletion')
            ->assertDispatched('shiftplan.refresh')
            ->assertDispatched('change-modal-state');

        $this->assertDatabaseMissing('shifts', ['id' => $shift->id]);
    }

    public function test_it_resets_properties_after_deletion(): void
    {
        $shift = Shift::first();

        Livewire::test(ConfirmShiftDeletionModal::class, ['shift' => $shift])
            ->call('confirmDeletion');

        $this->assertDatabaseMissing('shifts', ['id' => $shift->id]);
    }

    public function test_it_renders_the_component(): void
    {
        $component = Livewire::test(ConfirmShiftDeletionModal::class);

        $component->assertViewIs('livewire.shift-plan.confirm-shift-deletion-modal');
    }
}
