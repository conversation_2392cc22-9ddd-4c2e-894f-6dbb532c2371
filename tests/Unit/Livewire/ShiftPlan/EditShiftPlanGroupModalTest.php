<?php

namespace Tests\Unit\Livewire\ShiftPlan;

use App\Livewire\ShiftPlan\EditShiftPlanGroupModal;
use App\ShiftPlan;
use App\ShiftPlanGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

/** @group shiftplan */
class EditShiftPlanGroupModalTest extends TestCase
{
    use RefreshDatabase;

    private function prepareShiftPlanGroup(): array
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        $shiftPlan = ShiftPlan::factory()->create([
            'owner_id' => $user->id,
        ]);
        $shiftPlanGroup = ShiftPlanGroup::factory()->create([
            'shift_plan_id' => $shiftPlan->id,
        ]);

        return [$user, $shiftPlanGroup];
    }

    public function test_it_sets_correct_variables(): void
    {
        [$user, $shiftPlanGroup] = $this->prepareShiftPlanGroup();
        $this->actingAs($user);

        Livewire::test(EditShiftPlanGroupModal::class)
            ->call('prepareModal', $shiftPlanGroup->id)
            ->assertSet('title', $shiftPlanGroup->name)
            ->assertDispatched('change-modal-state')
            ->assertHasNoErrors();
    }

    public function test_it_updates_shift_plan_group_name(): void
    {
        [$user, $shiftPlanGroup] = $this->prepareShiftPlanGroup();
        $this->actingAs($user);

        $newName = $shiftPlanGroup->name.' Updated';

        $livewire = Livewire::test(EditShiftPlanGroupModal::class)
            ->call('prepareModal', $shiftPlanGroup->id)
            ->set('title', $newName)
            ->call('updateGroup');

        $this->assertDatabaseHas('shift_plan_groups', [
            'name' => $newName,
        ]);

        $livewire->assertSet('name', '');
        $livewire->assertSet('shiftPlanGroup', null);
        $livewire->assertSet('shiftPlan', null);
        $livewire->assertDispatched('shiftplan-group-updated');
    }
}
