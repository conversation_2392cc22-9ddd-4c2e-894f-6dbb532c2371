<?php

namespace Tests\Unit\Livewire\ShiftPlan;

use App\Domains\ShiftPlan\Domain\Actions\ShiftPlan\CreateShiftPlanAction;
use App\Domains\ShiftPlan\Domain\Actions\ShiftPlanGroup\CreateShiftPlanGroupAction;
use App\Domains\ShiftPlan\Domain\Data\ShiftPlanData;
use App\Domains\ShiftPlan\Domain\Data\ShiftPlanGroupData;
use App\Helper\Color;
use App\Livewire\ShiftPlan\AddUserToGroupModal;
use App\ShiftPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

/** @group shiftplan */
class AddUserToGroupModalTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mounts_correctly(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        $this->actingAs($user);

        $shiftPlan = new ShiftPlan([
            'id' => 1,
            'name' => 'Test Shift Plan',
            'owner_id' => $user->id,
            'uuid' => '1234',
        ]);
        $shiftPlan->save();

        Livewire::test(AddUserToGroupModal::class, ['shiftPlan' => $shiftPlan])
            ->assertSet('shiftPlan', $shiftPlan);
    }

    public function test_it_opens_and_filters_users_correctly(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy);

        $otherPharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $this->createPharmacyEmployee($otherPharmacy);

        $this->actingAs($user);

        $shiftPlan = new ShiftPlan([
            'id' => 1,
            'name' => 'Test Shift Plan',
            'owner_id' => $user->id,
            'uuid' => '1234',
        ]);

        $groupId = 1;

        $users = Livewire::test(AddUserToGroupModal::class, ['shiftPlan' => $shiftPlan])
            ->call('open', $groupId)
            ->assertSet('shiftPlanGroupId', $groupId)
            ->get('users');

        $this->assertContains($user->id, $users->pluck('id'));
        $this->assertContains($employee->id, $users->pluck('id'));
    }

    public function test_it_saves_and_dispatches_events_correctly(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy);
        $this->actingAs($user);

        $planVO = ShiftPlanData::from([
            'name' => 'Testplan',
            'owner_id' => $user->id,
        ]);

        $shiftPlan = CreateShiftPlanAction::execute($planVO);

        $groupDto = CreateShiftPlanGroupAction::execute(
            ShiftPlanGroupData::from([
                'name' => 'Meine Gruppe 1',
                'color' => Color::randomHexColor(),
                'shift_plan_id' => $shiftPlan->id,
            ])
        );

        Livewire::test(AddUserToGroupModal::class, ['shiftPlan' => $shiftPlan])
            ->set('shiftPlanGroupId', $groupDto->id)
            ->set('userId', $employee->id)
            ->call('save')
            ->assertDispatched('shiftplan.refresh');
    }
}
