<?php

namespace Tests\Unit\Livewire\CardLink\WizardSteps;

use App\Enums\CardLink\CardLinkPackageEnum;
use App\Livewire\CardLink\OrderCardLinkWizardSteps\ChoosePackageStep;
use Livewire\Livewire;
use Tests\TestCase;

class ChoosePackageStepTest extends TestCase
{
    public function test_it_does_not_submit_because_no_package(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        Livewire::actingAs($user)
            ->test(ChoosePackageStep::class)
            ->call('setPackage', '')
            ->call('submit')
            ->assertHasErrors(['selectedPackage']);
    }

    public function test_it_submits(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->session([
            'card-link' => [
                ChoosePackageStep::class => [
                    'selectedPackage' => CardLinkPackageEnum::cases()[array_rand(CardLinkPackageEnum::cases())]->value,
                ],
            ],
        ]);

        Livewire::actingAs($user)
            ->test(ChoosePackageStep::class)
            ->call('submit')
            ->assertHasNoErrors(['selectedPackage'])
            ->assertSessionHas('card-link.'.ChoosePackageStep::class.'.selectedPackage');
    }
}
