<?php

namespace Tests\Unit\Livewire\CardLink;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PharmacyRoleEnum;
use App\Livewire\CardLink\Success;
use Livewire\Livewire;
use Tests\TestCase;

class SuccessTest extends TestCase
{
    public function test_it_does_not_render_because_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        Livewire::actingAs($user)
            ->test(Success::class)
            ->assertForbidden();
    }

    public function test_it_does_not_render_because_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        Livewire::actingAs($user)
            ->test(Success::class)
            ->assertForbidden();
    }

    public function test_it_does_not_render_because_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        Livewire::actingAs($user)
            ->test(Success::class)
            ->assertForbidden();
    }

    public function test_it_does_not_render_because_not_owner_or_sub_owner(): void
    {
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);

        Livewire::actingAs($employee)
            ->test(Success::class)
            ->assertForbidden();

        Livewire::actingAs($branchManager)
            ->test(Success::class)
            ->assertForbidden();
    }

    public function test_it_renders_with_sub_owner(): void
    {
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);

        Livewire::actingAs($employee)
            ->test(Success::class)
            ->assertOk();
    }

    public function test_it_renders_with_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        Livewire::actingAs($user)
            ->test(Success::class)
            ->assertOk();
    }
}
