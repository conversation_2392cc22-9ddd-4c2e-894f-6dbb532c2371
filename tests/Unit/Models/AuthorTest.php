<?php

namespace Tests\Unit\Models;

use App\Author;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthorTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_generate_a_full_name()
    {
        $author = Author::factory()->create([
            'title' => 'Dr.',
            'first_name' => 'Tim',
            'last_name' => 'Test',
        ]);

        $this->assertSame('Dr. Tim Test', $author->full_name);
    }
}
