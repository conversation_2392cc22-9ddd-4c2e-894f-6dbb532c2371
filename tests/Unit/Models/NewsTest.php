<?php

namespace Tests\Unit\Models;

use App\Author;
use App\Category;
use App\Enums\NewsStatusEnum;
use App\News;
use App\Tag;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NewsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_check_if_it_is_released()
    {
        /** @var News $news */
        $news = News::factory()->unreleased()->create();

        $this->assertFalse($news->isReleased());

        $news = News::factory()->released()->create();

        $this->assertTrue($news->isReleased());

        $news->update(['status' => NewsStatusEnum::DRAFT]);

        $this->assertFalse($news->isReleased());
    }

    /** @test */
    public function it_can_check_if_it_should_display_the_login_wall()
    {
        /** @var News $news */
        $news = News::factory()->unreleased()->create();

        $this->assertFalse($news->shouldDisplayLoginWall());

        $news->update(['with_login_wall' => true]);

        $this->assertTrue($news->shouldDisplayLoginWall());

        $this->actingAs(User::factory()->create());

        $this->assertFalse($news->shouldDisplayLoginWall());
    }

    /** @test */
    public function it_can_have_tags()
    {
        $news = News::factory()
            ->has(Tag::factory()->count(2))
            ->create();

        $this->assertCount(2, $news->tags);
    }

    /** @test */
    public function it_can_have_categories()
    {
        $news = News::factory()
            ->has(Category::factory()->count(2))
            ->create();

        $this->assertCount(2, $news->categories);
    }

    /** @test */
    public function it_can_have_an_author()
    {
        $author = Author::factory()->create();
        $news = News::factory()->count(3)->create(['author_id' => $author->id]);

        $this->assertCount(3, $author->news);
        $this->assertTrue($news->first()->author->is($author));
    }

    /** @test */
    public function it_can_query_only_released_posts()
    {
        $released_news = News::factory()->count(3)->released()->create();
        $draft_news = News::factory()->create(['status' => NewsStatusEnum::DRAFT]);
        $pending_news = News::factory()->create(['status' => NewsStatusEnum::DRAFT]);
        $archive_news = News::factory()->create(['status' => NewsStatusEnum::ARCHIVE]);

        $this->assertCount(3, News::released()->get());
    }
}
