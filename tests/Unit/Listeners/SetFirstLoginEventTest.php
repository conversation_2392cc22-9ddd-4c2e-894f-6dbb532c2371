<?php

namespace Tests\Unit\Listeners;

use App\Listeners\SetFirstLoginEvent;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SetFirstLoginEventTest extends TestCase
{
    use RefreshDatabase;

    public function test_handle()
    {
        $user = $this->createPharmacyUser();
        $listener = new SetFirstLoginEvent;

        $listener->handle(new Login('web', $user, false));

        $this->assertDatabaseHas($user, ['first_login' => $user->first_login]);
        $this->assertNotNull($user->first_login);
    }
}
