<?php

namespace Tests\Unit\Misc;

use App\Enums\PharmacyRoleEnum;
use App\Enums\PharmacyStatusEnum;
use App\Pharmacy;
use App\PharmacyAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PharmacyApprovalTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function a_name_can_be_approved()
    {
        $this->markTestSkipped('Pharmacy approvals are currently disabled');

        $pharmacy = Pharmacy::factory()->create();

        $pharmacy->approvableChanges()->create([
            'attribute' => 'name',
            'type' => 'string',
            'change' => [
                'newValue' => 'ABC',
            ],
        ]);
        $pharmacy->refresh();

        $this->assertNotSame('ABC', $pharmacy->name);

        $pharmacy->approveAttributeChange('name');

        $pharmacy->refresh();

        $this->assertSame('ABC', $pharmacy->name);
    }

    /** @test */
    public function a_image_can_be_approved()
    {
        $this->markTestSkipped('Pharmacy approvals are currently disabled');
        $pharmacy = Pharmacy::factory()->create();

        $pharmacy->approvableChanges()->create([
            'attribute' => 'city',
            'type' => 'string',
            'change' => [
                'newValue' => 'ABC',
            ],
        ]);

        Storage::fake('local');
        Storage::fake('approval');

        Storage::disk('local')->putFileAs($pharmacy->getFilesDirectory(), UploadedFile::fake()->image('photo1.jpg'), 'photo1.jpg');

        $pharmacy->pharmacyImages()->create([
            'path' => $pharmacy->getFilesDirectory().'photo1.jpg',
            'is_logo' => true,
            'is_validated' => true,
        ]);

        Storage::disk('approval')->putFileAs('', UploadedFile::fake()->image('photo1.jpg'), 'photo12.jpg');

        $pharmacy->approvableChanges()->create([
            'attribute' => 'logo',
            'type' => 'image',
            'change' => [
                'path' => 'photo12.jpg',
            ],
        ]);

        Storage::disk('local')->assertExists($pharmacy->getFilesDirectory().'photo1.jpg');
        Storage::disk('approval')->assertExists('/photo12.jpg');

        Storage::disk('approval')->putFileAs('', UploadedFile::fake()->image('photo1.jpg'), 'photo15.jpg');

        $pharmacy->approvableChanges()->create([
            'attribute' => 'logo',
            'type' => 'image',
            'change' => [
                'path' => 'photo15.jpg',
            ],
        ]);

        Storage::disk('approval')->assertExists('/photo15.jpg');
        Storage::disk('approval')->assertMissing('/photo12.jpg');

        $pharmacy->approveAttributeChange('logo');

        Storage::disk('local')->assertMissing($pharmacy->getFilesDirectory().'photo1.jpg');

        Storage::disk('approval')->assertMissing('/photo15.jpg');

        Storage::disk('local')->assertExists($pharmacy->logo->path);
    }

    /** @test */
    public function an_unverified_pharmacy_can_edit_without_approval()
    {
        $this->markTestSkipped('Pharmacy approvals are currently disabled');

        /** @var Pharmacy $pharmacy */
        $pharmacy = Pharmacy::factory()->create(['verification_status' => PharmacyStatusEnum::PENDING]);
        $pharmacyAddress = PharmacyAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);
        $user = $this->createPharmacyUser();
        $pharmacy->assignUser($user, PharmacyRoleEnum::OWNER);
        $this->acceptTermsForPharmacy($pharmacy);

        $request = array_merge($pharmacy->toArray(), $pharmacyAddress->toArray());
        $request['city'] = 'ABC';
        $request['courier_service_radius'] = '5,2';
        $request['goods_management_system'] = $request['goods_management_system_id'];
        $request['accounting_center'] = $request['accounting_center_id'];
        $request['telematics_id'] = '3-17.2.**********.10.372';
        $request['billing_address_id'] = 1;

        $this
            ->actingAs($user)
            ->put(route('pharmacies.update', $pharmacy), $request)
            ->assertRedirect(route('pharmacies.overview', [$pharmacy]));

        $pharmacy->refresh();

        $this->assertSame('ABC', $pharmacy->city);
        $this->assertCount(0, $pharmacy->approvableChanges);
    }
}
