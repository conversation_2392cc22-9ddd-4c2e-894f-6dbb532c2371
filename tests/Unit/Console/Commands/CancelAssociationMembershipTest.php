<?php

namespace Tests\Unit\Console\Commands;

use App\Association;
use App\Enums\Settings\PharmacySettingTypes;
use App\Mail\CancelAssociationMembershipUserInfoMail;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class CancelAssociationMembershipTest extends TestCase
{
    use RefreshDatabase;

    public function test_the_association_membership_get_canceled(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $association = Association::factory()->create();

        $user->pharmacyProfile->update([
            'association_id' => $association->id,
        ]);

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);

        $payload = new CreateAssociationMembershipChangePayload(
            user: $user,
            changeAt: now(),
            old: $association,
        );
        $process = new CreateAssociationMembershipChange;
        $success = (bool) $process->run($payload);

        Mail::assertQueued(CancelAssociationMembershipUserInfoMail::class);

        $this->assertTrue($success);

        $this->travelTo(now()->endOfMonth());

        $user->refresh();

        $associationMembershipChange = $user->currentAssociationMembershipChange;

        $this->assertNull($associationMembershipChange->change_done_at);
        $this->assertTrue((bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value);
        $this->assertTrue((bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value);

        $this->artisan('user:change-association-membership');

        $associationMembershipChange->refresh();
        $pharmacy->refresh();
        $user->refresh();

        $this->assertNotNull($associationMembershipChange->change_done_at);
        $this->assertNull($user->pharmacyProfile->association_id);
        $this->assertFalse((bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value);
        $this->assertFalse((bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value);
        $this->assertCount(0, $pharmacy->activeSubscriptions);
    }
}
