<?php

namespace Tests\Unit\Console\Commands;

use App\Actions\AnonymizeVaccinationAction;
use App\CovidVaccination;
use App\Enums\Vaccinate\VaccinationStatus;
use App\InfluenzaVaccination;
use App\Vaccination;
use App\VaccinationPatient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class HandleOldNonAnonymizedVaccinationsCommandTest extends TestCase
{
    use RefreshDatabase;

    public function test_pseudonymization_and_anonymization(): void
    {
        $this->markTestSkipped('This test is skipped because RKI DIM api is disabled');

        Http::fake([
            config('services.rki-dim-api.url').'/auth/realms/DIM/protocol/openid-connect/token' => Http::response([
                'access_token' => 'your_mocked_access_token',
                'expires_in' => 3600, // Example expiration time (in seconds)
            ]),
            config('services.rki-dim-api.url').'/api/v1/pseudonymization' => Http::response([
                'hash-1' => 'hash-1',
                'hash-2' => 'hash-2',
                'hash-3' => 'hash-3',
                'psn' => 'psn',
            ]),
        ]);

        $vaccination = Vaccination::factory()->create(['status' => VaccinationStatus::FINISHED]);
        $vaccinationPatient = VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccination->id,
            'first_name' => 'something',
        ]);
        CovidVaccination::factory()->create([
            'vaccination_id' => $vaccination->id,
        ]);

        $anonymizedVaccination = Vaccination::factory()->create(['status' => VaccinationStatus::FINISHED]);
        $anonymizedVaccinationPatient = VaccinationPatient::factory()->create([
            'vaccination_id' => $anonymizedVaccination->id,
            ...AnonymizeVaccinationAction::$anonymizedData,
        ]);
        InfluenzaVaccination::factory()->create([
            'vaccination_id' => $anonymizedVaccination->id,
        ]);

        $vaccinationNotFinished = Vaccination::factory()->create(['status' => VaccinationStatus::DRAFT]);
        $vaccinationPatientNotFinished = VaccinationPatient::factory()->create([
            'vaccination_id' => $vaccinationNotFinished->id,
            'first_name' => 'something',
        ]);
        CovidVaccination::factory()->create([
            'vaccination_id' => $vaccinationNotFinished->id,
        ]);

        $this->artisan('vaccinations:handle-old-non-anonymized 2000-01-01 2050-01-01')
            ->expectsConfirmation('Do you wish to continue?', 'yes')
            ->assertExitCode(0);

        $this->assertTrue($vaccinationPatient->refresh()->isAnonymized());
        $this->assertTrue($anonymizedVaccinationPatient->refresh()->isAnonymized());
        $this->assertFalse($vaccinationPatientNotFinished->refresh()->isAnonymized());
    }
}
