<?php

namespace Tests\Unit\Console\Commands;

use App\Subscription;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateSubscriptionToPharmacyLevelTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_converts_a_single_subscription_with_single_pharmacy()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $user->pharmacyProfile()->update([
            'association_id' => null,
        ]);

        $subscription = Subscription::create([
            'subscribable_type' => $user::class,
            'subscribable_id' => $user->id,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
            'plan' => 'extern_base',
        ]);

        $subscription->subscriptionOrders()->createMany([
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-03-01'),
                'ended_at' => Carbon::parse('2020-03-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-04-01'),
                'ended_at' => Carbon::parse('2020-04-30'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-05-01'),
                'ended_at' => Carbon::parse('2020-05-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
        ]);

        $this->artisan('subscription:update-to-pharmacy-level');

        $this->assertDatabaseCount('subscriptions_old', 1);
        $this->assertDatabaseCount('subscription_orders', 3);
        $this->assertDatabaseHas('subscriptions_old', [
            'id' => $subscription->id,
            'subscribable_id' => $pharmacy->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
        ]);
    }

    public function test_it_converts_a_single_subscription_with_single_pharmacy_that_is_already_canceled()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $user->pharmacyProfile()->update([
            'association_id' => null,
        ]);

        $subscription = Subscription::create([
            'subscribable_type' => $user::class,
            'subscribable_id' => $user->id,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
            'plan' => 'extern_base',
            'ends_at' => Carbon::parse('2020-05-31'),
        ]);

        $subscription2 = Subscription::create([
            'subscribable_type' => $user::class,
            'subscribable_id' => $user->id,
            'started_at' => Carbon::parse('2020-07-01'),
            'cycle_started_at' => Carbon::parse('2020-07-01'),
            'cycle_ends_at' => Carbon::parse('2020-07-31'),
            'plan' => 'extern_base',
        ]);

        $subscription->subscriptionOrders()->createMany([
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'subscription_id' => $subscription->id,
                'started_at' => Carbon::parse('2020-03-01'),
                'ended_at' => Carbon::parse('2020-03-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'subscription_id' => $subscription->id,
                'started_at' => Carbon::parse('2020-04-01'),
                'ended_at' => Carbon::parse('2020-04-30'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'subscription_id' => $subscription->id,
                'started_at' => Carbon::parse('2020-05-01'),
                'ended_at' => Carbon::parse('2020-05-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
        ]);

        $subscription2->subscriptionOrders()->createMany([
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-07-01'),
                'ended_at' => Carbon::parse('2020-07-31'),
                'subscription_id' => $subscription2->id,
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
        ]);

        $this->artisan('subscription:update-to-pharmacy-level');

        $this->assertDatabaseCount('subscriptions_old', 2);
        $this->assertDatabaseCount('subscription_orders', 4);
        $this->assertDatabaseHas('subscriptions_old', [
            'id' => $subscription->id,
            'subscribable_id' => $pharmacy->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
            'ends_at' => Carbon::parse('2020-05-31'),
        ]);
        $this->assertDatabaseHas('subscriptions_old', [
            'id' => $subscription2->id,
            'subscribable_id' => $pharmacy->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-07-01'),
            'cycle_started_at' => Carbon::parse('2020-07-01'),
            'cycle_ends_at' => Carbon::parse('2020-07-31'),
            'ends_at' => null,
        ]);
    }

    public function test_it_converts_a_multiple_subscriptions_with_multiple_pharmacies_that_is_already_canceled()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($user);

        $user->pharmacyProfile()->update([
            'association_id' => null,
        ]);

        $subscription = Subscription::create([
            'subscribable_type' => $user::class,
            'subscribable_id' => $user->id,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
            'plan' => 'extern_base',
            'ends_at' => Carbon::parse('2020-05-31'),
        ]);

        $subscription2 = Subscription::create([
            'subscribable_type' => $user::class,
            'subscribable_id' => $user->id,
            'started_at' => Carbon::parse('2020-07-01'),
            'cycle_started_at' => Carbon::parse('2020-07-01'),
            'cycle_ends_at' => Carbon::parse('2020-07-31'),
            'plan' => 'extern_base',
        ]);

        $subscription->subscriptionOrders()->createMany([
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-03-01'),
                'ended_at' => Carbon::parse('2020-03-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-04-01'),
                'ended_at' => Carbon::parse('2020-04-30'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy2->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-04-01'),
                'ended_at' => Carbon::parse('2020-04-30'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-05-01'),
                'ended_at' => Carbon::parse('2020-05-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
        ]);

        $subscription2->subscriptionOrders()->createMany([
            [
                'orderable_id' => $pharmacy->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-07-01'),
                'ended_at' => Carbon::parse('2020-07-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
            [
                'orderable_id' => $pharmacy2->id,
                'orderable_type' => $pharmacy::class,
                'started_at' => Carbon::parse('2020-07-01'),
                'ended_at' => Carbon::parse('2020-07-31'),
                'plan' => 'wl_base',
                'total_price' => 50,
            ],
        ]);

        $this->artisan('subscription:update-to-pharmacy-level');

        $this->assertDatabaseCount('subscriptions_old', 4);
        $this->assertDatabaseCount('subscription_orders', 6);
        $this->assertDatabaseHas('subscriptions_old', [
            'id' => $subscription->id,
            'subscribable_id' => $pharmacy->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-03-01'),
            'cycle_started_at' => Carbon::parse('2020-05-01'),
            'cycle_ends_at' => Carbon::parse('2020-05-31'),
            'ends_at' => Carbon::parse('2020-05-31'),
        ]);
        $this->assertDatabaseHas('subscriptions_old', [
            'subscribable_id' => $pharmacy2->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-04-01'),
            'cycle_started_at' => Carbon::parse('2020-04-01'),
            'cycle_ends_at' => Carbon::parse('2020-04-30'),
            'ends_at' => Carbon::parse('2020-04-30'),
        ]);
        $this->assertDatabaseHas('subscriptions_old', [
            'id' => $subscription2->id,
            'subscribable_id' => $pharmacy->id,
            'subscribable_type' => $pharmacy::class,
            'started_at' => Carbon::parse('2020-07-01'),
            'cycle_started_at' => Carbon::parse('2020-07-01'),
            'cycle_ends_at' => Carbon::parse('2020-07-31'),
        ]);

        $this->assertDatabaseHas('subscription_orders', [
            'orderable_id' => $pharmacy2->id,
            'started_at' => Carbon::parse('2020-04-01'),
            'ended_at' => Carbon::parse('2020-04-30'),
            'plan' => 'wl_base',
            'subscription_id' => 3,
        ]);

        $this->assertDatabaseHas('subscription_orders', [
            'orderable_id' => $pharmacy->id,
            'started_at' => Carbon::parse('2020-04-01'),
            'ended_at' => Carbon::parse('2020-04-30'),
            'plan' => 'wl_base',
            'subscription_id' => 1,
        ]);

        $this->assertDatabaseHas('subscription_orders', [
            'orderable_id' => $pharmacy->id,
            'started_at' => Carbon::parse('2020-05-01'),
            'ended_at' => Carbon::parse('2020-05-31'),
            'plan' => 'wl_base',
            'subscription_id' => 1,
        ]);

        $this->assertDatabaseHas('subscription_orders', [
            'orderable_id' => $pharmacy->id,
            'started_at' => Carbon::parse('2020-07-01'),
            'ended_at' => Carbon::parse('2020-07-31'),
            'plan' => 'wl_base',
            'subscription_id' => 2,
        ]);

        $this->assertDatabaseHas('subscription_orders', [
            'orderable_id' => $pharmacy2->id,
            'started_at' => Carbon::parse('2020-07-01'),
            'ended_at' => Carbon::parse('2020-07-31'),
            'plan' => 'wl_base',
            'subscription_id' => 4,
        ]);
    }
}
