<?php

namespace Tests\Unit\Console\Commands;

use App\Enums\Settings\PharmacySettingTypes;
use App\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SetAllTermsOfServiceToNotAcceptedTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function test_deletes_all_terms_of_service_entries_without_creating_new_ones(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->generalSettings()->forceDelete();

        [$user1, $pharmacy1] = $this->createPharmacyUserWithPharmacy();

        [$user2, $pharmacy2] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, false);
        $pharmacy2->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, false);

        $this->assertSame(
            1,
            Setting::query()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->where('value', PharmacySettingTypes::TERMS_OF_USE_ACCEPTED)
                ->count()
        );
        $this->assertSame(
            1,
            Setting::query()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->where('value', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED)
                ->count()
        );

        $this->assertSame(
            2,
            Setting::withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->where('value', PharmacySettingTypes::TERMS_OF_USE_ACCEPTED)
                ->count()
        );
        $this->assertSame(
            2,
            Setting::withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->where('value', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED)
                ->count()
        );

        $this->assertSame(
            3,
            Setting::withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->count()
        );
        $this->assertSame(
            3,
            Setting::withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->count()
        );

        $this->artisan('pharmacy:set-all-terms-of-service-to-not-accepted')
            ->assertExitCode(0);
        $this->assertSame(0, Setting::count());
    }
}
