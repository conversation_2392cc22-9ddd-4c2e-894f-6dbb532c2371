<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\RecancelKimAddress;
use App\Enums\KimAddressStatus;
use App\Jobs\KimAddress\TransmitKimAddressCancellation;
use App\KimAddress;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class RecancelKimAddressTest extends TestCase
{
    public function test_recancel_kim_addresses_does_not_run_when_kim_address_is_reserved(): void
    {
        Queue::fake();

        $kimAddress = KimAddress::factory()->create(['status' => KimAddressStatus::RESERVED->value]);

        Artisan::call(RecancelKimAddress::class);

        Queue::assertNothingPushed();

        $this->assertDatabaseHas('kim_addresses', [
            'email' => $kimAddress->email,
            'status' => $kimAddress->status,
        ]);
    }

    public function test_recancel_kim_addresses_does_not_run_when_kim_address_is_ordered(): void
    {
        Queue::fake();

        KimAddress::factory()->create(['status' => KimAddressStatus::ORDERED->value]);

        Artisan::call(RecancelKimAddress::class);

        Queue::assertNothingPushed();
    }

    public function test_recancel_kim_addresses_does_run_when_kim_address_is_cancelled(): void
    {
        Queue::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        KimAddress::factory()->for($pharmacy)->create(['status' => KimAddressStatus::CANCELED->value]);

        Artisan::call(RecancelKimAddress::class);

        Queue::assertPushed(TransmitKimAddressCancellation::class);
    }
}
