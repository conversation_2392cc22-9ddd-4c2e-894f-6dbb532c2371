<?php

namespace Tests\Unit\Http\Middleware;

use App\Helper\PharmacySessionHelper;
use App\Http\Integrations\Ia\PartnerApi\Requests\GetWebComponentAuthTokenRequest;
use App\Http\Middleware\IaTokenAuthMiddleware;
use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Route;
use Saloon\Http\Faking\MockClient;
use Saloon\Http\Faking\MockResponse;
use Tests\helper\IaTestCase;
use Tests\TestCase;

class IaTokenAuthMiddlewareTest extends TestCase
{
    use IaTestCase, RefreshDatabase, WithFaker;

    public function setDummyRoute(): void
    {
        Route::get('/dummy-test-route', static fn () => 'nice')
            ->middleware([IaTokenAuthMiddleware::class]);
    }

    public function test_middleware_requests_token_if_not_present(): void
    {
        $this->setDummyRoute();
        $pharmacy = $this->setupWorld(
            isOwner: false,
            hasAccessRights: true,
            integrationEnabled: true,
            isPreflightUser: true
        );

        PharmacySessionHelper::set($pharmacy);

        $this->travelToAndClearCache($this->preflightPhase);

        $this->get('/dummy-test-route')
            ->assertStatus(200);

        $this->travelToAndClearCache($this->released);

        $this->get('/dummy-test-route')
            ->assertStatus(200);
    }

    public function test_middleware_requests_token_if_not_present_for_preflight_user(): void
    {
        $this->setDummyRoute();
        $pharmacy = $this->setupWorld(
            isOwner: false,
            hasAccessRights: true,
            integrationEnabled: true,
            isPreflightUser: false
        );

        PharmacySessionHelper::set($pharmacy);

        $this->travelToAndClearCache($this->preflightPhase);

        $this->get('/dummy-test-route')
            ->assertStatus(403);

        $this->travelToAndClearCache($this->released);

        $this->get('/dummy-test-route')
            ->assertStatus(200);
    }

    public function test_the_middleware_sets_zpa_customer_status_correctly_to_true(): void
    {
        $this->setDummyRoute();

        MockClient::destroyGlobal();

        MockClient::global([
            GetWebComponentAuthTokenRequest::class => MockResponse::make(
                body: [
                    'token' => '2dcaa82cae3e9ed91e34b2b7ff118e05b7cd19aff55b62a8ead09eebf0c9fe28',
                    'zpa_customer' => true,
                ]
            ),
        ]);

        $pharmacy = $this->setupWorld(
            isOwner: false,
            hasAccessRights: true,
            integrationEnabled: true,
            isPreflightUser: false
        );

        PharmacySessionHelper::set($pharmacy);

        $this->travelToAndClearCache($this->released);

        $this->get('/dummy-test-route')
            ->assertStatus(200);

        /** @var IaIntegration $integration */
        $integration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;

        $this->assertTrue($integration->isZpaCustomer);
    }

    public function test_the_middleware_sets_zpa_customer_status_correctly_to_false(): void
    {
        $this->setDummyRoute();

        MockClient::destroyGlobal();

        MockClient::global([
            GetWebComponentAuthTokenRequest::class => MockResponse::make(
                body: [
                    'token' => '2dcaa82cae3e9ed91e34b2b7ff118e05b7cd19aff55b62a8ead09eebf0c9fe28',
                    'zpa_customer' => false,
                ]
            ),
        ]);

        $pharmacy = $this->setupWorld(
            isOwner: false,
            hasAccessRights: true,
            integrationEnabled: true,
            isPreflightUser: false
        );

        PharmacySessionHelper::set($pharmacy);

        $this->travelToAndClearCache($this->released);

        $this->get('/dummy-test-route')
            ->assertStatus(200);

        /** @var IaIntegration $integration */
        $integration = $pharmacy->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;

        $this->assertFalse($integration->isZpaCustomer);
    }

    public function test_redirect_to_dashboard_on_network_error(): void
    {
        $this->setDummyRoute();

        MockClient::destroyGlobal();

        MockClient::global([
            GetWebComponentAuthTokenRequest::class => MockResponse::make(
                status: 500
            ),
        ]);

        $pharmacy = $this->setupWorld(
            isOwner: false,
            hasAccessRights: true,
            integrationEnabled: true,
            isPreflightUser: false
        );

        PharmacySessionHelper::set($pharmacy);

        $this->travelToAndClearCache($this->released);

        $this->get('/dummy-test-route')
            ->assertRedirect(route('dashboard'));
    }
}
