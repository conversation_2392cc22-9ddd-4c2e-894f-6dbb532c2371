<?php

namespace Tests\Unit\Http\Middleware;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Http\Middleware\CanViewApomailAdministration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class CanViewApomailAdministrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Route::get('/dummy-test-route', static fn () => 'nice')
            ->middleware([CanViewApomailAdministration::class])
            ->name('dummy-test-route');
    }

    public function test_association_user_cannot_view_apomail_administration(): void
    {
        [$user] = $this->createAssociationUser();

        $this->actingAs($user);

        $this->get('/dummy-test-route')
            ->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function test_user_without_pharmacy_cannot_view_apomail_administration(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $this->get('/dummy-test-route')
            ->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function test_pharmacy_needs_to_be_subscribed(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user);

        $this->get('/dummy-test-route')
            ->assertStatus(Response::HTTP_FORBIDDEN);

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->get('/dummy-test-route')
            ->assertStatus(Response::HTTP_OK);
    }
}
