<?php

namespace Tests\Unit\Http\Controller\Api;

use App\Misc\PassportClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Client;
use <PERSON><PERSON>\Passport\Token;
use Tests\helper\ApiHelper;
use Tests\TestCase;

class ClientAuthenticationTest extends TestCase
{
    use <PERSON>pi<PERSON><PERSON>per, RefreshDatabase;

    public array $scopes = ['languages', 'pharmacies-index'];

    public Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = PassportClient::factory()->create([
            'allowed_scopes' => json_encode($this->scopes),
        ]);
    }

    /** @test */
    public function it_needs_correct_credentials()
    {
        $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => 'abc',
            'client_secret' => $this->client->secret,
        ])->assertStatus(401);

        $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->client->id,
            'client_secret' => 'abc',
        ])->assertStatus(401);

        $this->post('/oauth/token', [
            'grant_type' => 'password',
            'client_id' => $this->client->id,
            'client_secret' => $this->client->secret,
        ])->assertStatus(401);

        $this->assertDatabaseCount((new Token)->getTable(), 0);
    }

    /** @test */
    public function it_gets_all_scopes_the_client_is_allowed()
    {
        $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->client->id,
            'client_secret' => $this->client->secret,
        ])->assertStatus(200); // Token Lifetime 1 Hour

        $token = Token::first();

        $this->assertNotNull($token);
        $this->assertSame($this->scopes, $token->scopes);
    }

    /** @test */
    public function it_cant_requests_scopes_it_is_not_allowed_to_have()
    {
        $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->client->id,
            'client_secret' => $this->client->secret,
            'scopes' => [
                'languages',
                'pharmacies-show',
            ],
        ])->assertStatus(200);

        $token = Token::first();

        $this->assertFalse(in_array('pharmacies-show', $token->scopes), 'The token has scopes it is not allowed to have');
    }
}
