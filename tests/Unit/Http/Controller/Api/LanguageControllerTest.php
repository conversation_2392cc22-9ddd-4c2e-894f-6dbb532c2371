<?php

namespace Tests\Unit\Http\Controller\Api;

use App\Language;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\ApiHelper;
use Tests\helper\ResourceTestHelper;
use Tests\TestCase;

class LanguageControllerTest extends TestCase
{
    use ApiHelper, RefreshDatabase, ResourceTestHelper;

    /** @test */
    public function it_can_fetch_all_languages()
    {
        $count = Language::count();

        $this
            ->json('GET', route('api.languages.index'))
            ->assertUnauthorized();

        $this->actingAsClientWithScopes(['languages']);

        $this
            ->json('GET', route('api.languages.index'))
            ->assertJson(['meta' => ['per_page' => 15]])
            ->assertResourceHasCount($count);

        $this
            ->json('GET', route('api.languages.index'), [
                'per_page' => (config('api.pagination.max') + 50),
            ])
            ->assertJson(['meta' => ['per_page' => config('api.pagination.max')]])
            ->assertResourceHasCount($count);

        $this->actingAsClientWithScopes(['accounting-centers']);

        $this
            ->json('GET', route('api.languages.index'))
            ->assertStatus(403);
    }

    /** @test */
    public function it_can_get_one_language()
    {
        $language = Language::first();

        $this
            ->json('GET', route('api.languages.show', $language))
            ->assertUnauthorized();

        $this->actingAsClientWithScopes(['languages']);

        $this
            ->json('GET', route('api.languages.show', $language))
            ->assertStatus(200);

        $this->actingAsClientWithScopes(['accounting-centers']);

        $this
            ->json('GET', route('api.languages.show', $language))
            ->assertStatus(403);
    }
}
