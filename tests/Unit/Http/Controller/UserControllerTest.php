<?php

namespace Tests\Unit\Http\Controller;

use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Mail\UserChangeEmailMail;
use Database\Factories\UserFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use OpenIDConnectClient\AccessToken;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    private const ID_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.0Q_NCJzfbBzhcwXkcDkwNfjEr3pvENC7zPkmK-IDEGQ';

    private const DEFAULT_ARGUMENTS = [
        'access_token' => 'some access token',
        'resource_owner_id' => 'some resource_owner_id',
        'refresh_token' => 'some refresh_token',
        'expires_in' => 123,
        'id_token' => self::ID_TOKEN,
        'random_key_123' => 'some random value',
    ];

    public function test_a_user_can_update_his_account_but_email_needs_verification(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user);

        $updates = [
            'salutation' => SalutationEnum::MR,
            'title' => 'Dr.',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '**********',
            'email' => '<EMAIL>',
            'email_confirmation' => '<EMAIL>',
        ];

        $this->put(route('users.update'), $updates);

        $this->assertDatabaseHas('users', Arr::except($updates, ['email', 'email_confirmation']));
        $this->assertNotSame($user->fresh()->email, $updates['email']);
    }

    public function test_a_company_user_can_update_his_account_but_email_needs_verification(): void
    {
        $user = $this->createCompanyUser();
        $pharmacy = $this->createPharmacy();
        $pharmacy->assignUser($user, PharmacyRoleEnum::OWNER);

        $this->actingAs($user);

        $updates = [
            'name' => 'Changed Name OHG',
            'salutation' => SalutationEnum::MS,
            'title' => 'Prof.',
            'first_name' => 'Joan',
            'last_name' => 'Doe',
            'phone' => '**********',
            'email' => '<EMAIL>',
            'email_confirmation' => '<EMAIL>',
        ];

        $this->put(route('users.updateCompany'), $updates);

        $this->assertDatabaseHas('users', Arr::except($updates, ['email', 'email_confirmation', 'name']));
        $this->assertNotSame($user->fresh()->email, $updates['email']);
        $this->assertDatabaseHas('company_users', ['name' => $updates['name']]);
    }

    public function test_a_user_can_request_to_change_his_email_address(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $request = $user->toArray();

        Mail::fake();

        $request['email'] = '<EMAIL>';
        $request['email_confirmation'] = '<EMAIL>';
        $request['association_id'] = 'no';

        $this->actingAs($user)->get(route('users.edit'))->assertDontSee('E-Mail erneut senden');

        $this->actingAs($user)->put(route('users.update'), $request)->assertSessionDoesntHaveErrors();

        $this->assertNotSame('<EMAIL>', $user->fresh()->email);

        $actionUrl = '';
        Mail::assertSent(UserChangeEmailMail::class, function ($mail) use (&$actionUrl) {
            $actionUrl = $mail->actionUrl;

            return $mail->hasTo('<EMAIL>');
        });

        $this->actingAs($user)->get(route('users.edit'))->assertSee('E-Mail erneut senden');

        $this->secondUser();

        $this->actingAs($user)->get($actionUrl)->assertStatus(200);

        $this->assertSame('<EMAIL>', $user->fresh()->email);
    }

    private function secondUser(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        [$defaultUser] = $this->createPharmacyUserWithPharmacy();

        $request = $user->toArray();

        // E-Mail is as change requested from another user
        $request['email'] = '<EMAIL>';
        $request['email_confirmation'] = '<EMAIL>';
        $request['association_id'] = 'no';
        $request['password'] = UserFactory::PASSWORD;

        $this->actingAs($user)->put(route('users.update'), $request)->assertSessionHasErrors(['email']);

        // email is used by another user
        $request['email'] = $defaultUser->email;
        $request['email_confirmation'] = $defaultUser->email;
        $request['password'] = UserFactory::PASSWORD;

        $this->actingAs($user)->put(route('users.update'), $request)->assertSessionHasNoErrors();
    }

    public function test_a_user_confirmed_or_declined_the_changed_idp_email_address_usage(): void
    {
        $token = new AccessToken(self::DEFAULT_ARGUMENTS);

        [$user] = $this->createPharmacyUserWithPharmacy();

        $request = [
            'yes' => true,
        ];

        $this->actingAs($user)
            ->withSession(['oidc-auth.access_token' => $token])
            ->post(route('users.check-for-idp-email-change.set-address'), $request)
            ->assertRedirectToRoute('dashboard');

        $user->fresh();

        $this->assertSame('<EMAIL>', $user->email);
        $this->assertSame('<EMAIL>', $user->idp_email);

        [$user] = $this->createPharmacyUserWithPharmacy();

        $request = [
            'no' => true,
        ];

        $this->actingAs($user)
            ->withSession(['oidc-auth.access_token' => $token])
            ->post(route('users.check-for-idp-email-change.set-address'), $request)
            ->assertRedirectToRoute('dashboard');

        $user->fresh();

        $this->assertNotSame('<EMAIL>', $user->email);
        $this->assertSame('<EMAIL>', $user->idp_email);

    }
}
