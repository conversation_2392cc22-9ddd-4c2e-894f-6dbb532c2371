<?php

namespace Tests\Unit\Http\Controller;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Integrations\NGDAIntegration;
use App\Livewire\Ngda\WizardSteps\AccessDeniedStep;
use App\Livewire\Ngda\WizardSteps\ConnectWithPharmacyStep;
use App\Livewire\Ngda\WizardSteps\FailedStep;
use App\Livewire\Ngda\WizardSteps\ForeignAccountStep;
use App\Livewire\Ngda\WizardSteps\NotLegitimizedStep;
use App\Livewire\Ngda\WizardSteps\PublicFailedStep;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class NgdaCallbackControllerTest extends TestCase
{
    public function test_ngda_callback_successful(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'bla keks urn-ngda-services-nhub-w lol rofl',
                    'realm_access' => [
                        'roles' => ['unknown', 'urn-ngda-roles-status-legitimized', 'bla'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => ConnectWithPharmacyStep::kebab(),
            ]);
    }

    public function test_ngda_callback_missing_roles(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'bla keks urn-ngda-services-nhub-w lol rofl',
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => NotLegitimizedStep::kebab(),
            ]);
    }

    public function test_not_legitimized(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'bla keks urn-ngda-services-nhub-w lol rofl',
                    'realm_access' => [
                        'roles' => ['unknown'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => NotLegitimizedStep::kebab(),
            ]);
    }

    public function test_legitimized_non_public_pharmacy(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'lol rofl',
                    'realm_access' => [
                        'roles' => ['urn-ngda-roles-status-legitimized'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => PublicFailedStep::kebab(),
            ]);
    }

    public function test_empty_roles(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'bla keks urn-ngda-services-nhub-w lol rofl',
                    'realm_access' => [
                        'roles' => [],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => NotLegitimizedStep::kebab(),
            ]);
    }

    public function test_unknown_roles_and_unknown_scopes(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'bla keks lol rofl',
                    'realm_access' => [
                        'roles' => ['unknown', 'blakeks'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => FailedStep::kebab(),
            ]);
    }

    public function test_no_scopes(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'realm_access' => [
                        'roles' => ['urn-ngda-roles-status-legitimized'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => PublicFailedStep::kebab(),
            ]);
    }

    public function test_scopes_empty(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => '',
                    'realm_access' => [
                        'roles' => ['urn-ngda-roles-status-legitimized'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => PublicFailedStep::kebab(),
            ]);
    }

    public function test_ngda_callback_validation_error(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => null,
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => FailedStep::kebab(),
            ]);
    }

    public function test_json_response_cannot_be_decoded(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => 'bla.bla.bla',
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => FailedStep::kebab(),
            ]);
    }

    public function test_foreign_account(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'access_token' => JWT::encode([
                    'scope' => 'urn-ngda-services-nhub-w',
                    'realm_access' => [
                        'roles' => ['urn-ngda-roles-status-legitimized'],
                    ],
                    'id' => 'APO123',
                    'name' => 'Kronenapotheke',
                ], Str::random(), 'HS256'),
                'refresh_token' => JWT::encode([], Str::random(), 'HS256'),
                'refresh_expires_in' => time() + 20,
                'id_token' => Str::random(),
            ]),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        [$otherUser, $otherPharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($otherPharmacy, BaseStripeProduct::make());

        $otherPharmacy->setIntegration(
            new NGDAIntegration(
                id: 'APO123',
                accessToken: Str::random(),
                refreshToken: Str::random(),
                refreshAt: now()->addWeek(),
                acceptedNnf: true,
            )
        );

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => ForeignAccountStep::kebab(),
            ]);
    }

    public function test_access_denied(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        [$otherUser, $otherPharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($otherPharmacy, BaseStripeProduct::make());

        $otherPharmacy->setIntegration(
            new NGDAIntegration(
                id: 'APO123',
                accessToken: Str::random(),
                refreshToken: Str::random(),
                refreshAt: now()->addWeek(),
                acceptedNnf: true,
            )
        );

        $url = sprintf('%s?error=access_denied', route('pharmacies.ngda-callback', $pharmacy));

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => AccessDeniedStep::kebab(),
            ]);
    }

    public function test_invalid_scope_account(): void
    {
        Http::fake([
            config('services.ngda.token_url') => Http::response([
                'error' => 'invalid_scope',
                'error_description' => 'Client no longer has requested consent from user',
            ], 400),
        ]);

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $url = sprintf('%s?code=%s', route('pharmacies.ngda-callback', $pharmacy), Str::random());

        $this->actingAs($user)
            ->get($url)
            ->assertRedirectToRoute('pharmacies.edit', [
                'pharmacy' => $pharmacy,
                'open' => true,
                'step' => AccessDeniedStep::kebab(),
            ]);
    }
}
