<?php

namespace Tests\Unit\Http\Controller;

use App\Enums\SessionEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DocSpaceGroupControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_the_request_to_index_route_is_successful()
    {
        [$owner, $pharmacy] = $this->createOwnerForSDR(true, true, true);

        session()->push(SessionEnum::GUIDED_CREATION_ALREADY_SKIPPED->value, $pharmacy->id);

        $this->get(route('sdr.groups', ['pharmacy' => $pharmacy]))
            ->assertStatus(200)
            ->assertViewIs('pharmacy.docspace.groups.index');
    }
}
