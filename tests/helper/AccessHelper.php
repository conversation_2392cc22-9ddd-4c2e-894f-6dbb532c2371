<?php

namespace Tests\helper;

use ReflectionClass;

trait AccessHelper
{
    /**
     * @return mixed
     */
    public static function getProtectedProperty(object $object, string $property)
    {
        $reflection = new ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);

        return $property->getValue($object);
    }

    /**
     * @param  mixed  $value
     */
    public static function setProtectedProperty(object $object, string $property, $value): void
    {
        $reflection = new ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);
        $property->setValue($object, $value);
    }

    /**
     * @return mixed
     */
    public static function callProtectedMethod(object $object, string $method, array $args = [])
    {
        $reflection = new ReflectionClass($object);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $args);
    }

    /**
     * @template T of object
     *
     * @param  class-string<T>  $class
     * @return T
     */
    public static function instantiateProtectedClass(string $class, mixed ...$args): object
    {
        $reflection = new ReflectionClass($class);
        $constructor = $reflection->getConstructor();
        $constructor->setAccessible(true);

        /** @var T $instance */
        $instance = $reflection->newInstanceWithoutConstructor();

        $constructor->invoke($instance, ...$args);

        return $instance;
    }
}
