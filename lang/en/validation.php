<?php

return [
    'accepted' => 'The :attribute must be accepted.',
    'active_url' => 'The :attribute is not a valid URL.',
    'after' => 'The :attribute must be a date after :date.',
    'after_or_equal' => 'The :attribute must be a date after or equal to :date.',
    'alpha' => 'The :attribute may only contain letters.',
    'alpha_dash' => 'The :attribute may only contain letters, numbers, dashes and underscores.',
    'alpha_num' => 'The :attribute may only contain letters and numbers.',
    'array' => 'The :attribute must be an array.',
    'before' => 'The :attribute must be a date before :date.',
    'before_or_equal' => 'The :attribute must be a date before or equal to :date.',
    'between' => [
        'numeric' => 'The :attribute must be between :min and :max.',
        'file' => 'The :attribute must be between :min and :max kilobytes.',
        'string' => 'The :attribute must be between :min and :max characters.',
        'array' => 'The :attribute must have between :min and :max items.',
    ],
    'boolean' => 'The :attribute field must be true or false.',
    'confirmed' => 'The :attribute confirmation does not match.',
    'date' => 'The :attribute is not a valid date.',
    'date_equals' => 'The :attribute must be a date equal to :date.',
    'date_format' => 'The :attribute does not match the format :format.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => 'The :attribute must be :digits digits.',
    'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'email' => 'The :attribute must be a valid email address.',
    'ends_with' => 'The :attribute must end with one of the following: :values.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'numeric' => 'The :attribute must be greater than :value.',
        'file' => 'The :attribute must be greater than :value kilobytes.',
        'string' => 'The :attribute must be greater than :value characters.',
        'array' => 'The :attribute must have more than :value items.',
    ],
    'gte' => [
        'numeric' => 'The :attribute must be greater than or equal :value.',
        'file' => 'The :attribute must be greater than or equal :value kilobytes.',
        'string' => 'The :attribute must be greater than or equal :value characters.',
        'array' => 'The :attribute must have :value items or more.',
    ],
    'image' => 'The :attribute must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute must be an integer.',
    'ip' => 'The :attribute must be a valid IP address.',
    'ipv4' => 'The :attribute must be a valid IPv4 address.',
    'ipv6' => 'The :attribute must be a valid IPv6 address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'lt' => [
        'numeric' => 'The :attribute must be less than :value.',
        'file' => 'The :attribute must be less than :value kilobytes.',
        'string' => 'The :attribute must be less than :value characters.',
        'array' => 'The :attribute must have less than :value items.',
    ],
    'lte' => [
        'numeric' => 'The :attribute must be less than or equal :value.',
        'file' => 'The :attribute must be less than or equal :value kilobytes.',
        'string' => 'The :attribute must be less than or equal :value characters.',
        'array' => 'The :attribute must not have more than :value items.',
    ],
    'max' => [
        'numeric' => 'The :attribute may not be greater than :max.',
        'file' => 'The :attribute may not be greater than :max kilobytes.',
        'string' => 'The :attribute may not be greater than :max characters.',
        'array' => 'The :attribute may not have more than :max items.',
    ],
    'mimes' => 'The :attribute must be a file of type: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'numeric' => 'The :attribute must be at least :min.',
        'file' => 'The :attribute must be at least :min kilobytes.',
        'string' => 'The :attribute must be at least :min characters.',
        'array' => 'The :attribute must have at least :min items.',
    ],
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute format is invalid.',
    'numeric' => 'The :attribute must be a number.',
    'password' => 'The password is incorrect.',
    'present' => 'The :attribute field must be present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'numeric' => 'The :attribute must be :size.',
        'file' => 'The :attribute must be :size kilobytes.',
        'string' => 'The :attribute must be :size characters.',
        'array' => 'The :attribute must contain :size items.',
    ],
    'starts_with' => 'The :attribute must start with one of the following: :values.',
    'string' => 'The :attribute must be a string.',
    'timezone' => 'The :attribute must be a valid zone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'url' => 'The :attribute format is invalid.',
    'uuid' => 'The :attribute must be a valid UUID.',
    'invalid_format' => 'The :attribute format is invalid.',
    'tid_card_type_for_human' => 'Diese Telematik-ID ist nur für Personen gültig. Bitte geben Sie die Telematik-ID Ihrer Institutionskarte (SMC-B) ein.',
    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'accounting_center_id' => 'Accounting center',
        'accountingCenter' => 'Accounting center',
        'address' => 'Address',
        'age' => 'Age',
        'association' => 'Association',
        'association_news_active' => 'Verbandsnews erhalten',
        'businessHours' => 'Business hours',
        'city' => 'City',
        'code' => 'Registration code',
        'company_name' => 'OHG name',
        'content' => 'Content',
        'corona_rapid_test' => 'Corona rapid tests',
        'country' => 'Country',
        'courier_service_radius' => 'Courier service radius',
        'current_status' => 'Current status',
        'custom_accounting_center' => 'Custom accounting center',
        'custom_goods_management_system' => 'Custom goods management system',
        'date' => 'Date',
        'date_of_birth' => 'Date of birth',
        'datetime' => 'Date & time',
        'day' => 'Day',
        'delivery' => 'Courier service',
        'description' => 'Description',
        'device_condition' => 'Condition of the device',
        'di' => 'Div.',
        'doseCount' => 'Total dose count',
        'doseNumber' => 'Dose number',
        'email' => 'E-Mail address',
        'email_confirmation' => 'Confirm E-Mail address',
        'ending' => 'Ending',
        'excerpt' => 'Excerpt',
        'fax' => 'Fax',
        'first_name' => 'First name',
        'firstName' => 'First name',
        'focusAreas' => 'Focus areas',
        'gender' => 'Gender',
        'goods_management_system_id' => 'Goods management system',
        'goodsManagementSystem' => 'Goods management system',
        'hour' => 'Hour',
        'house_number' => 'House number',
        'in_association' => 'Are you member of a association?',
        'inhalation' => 'Inhalation',
        'institute_id' => 'Institute ID',
        'is_company' => 'flag whether it is a general partnership',
        'last_name' => 'Last name',
        'lastName' => 'Last name',
        'login_email' => 'E-Mail for login',
        'minute' => 'Minute',
        'mobile' => 'Mobile',
        'month' => 'Month',
        'mr' => 'Mr.',
        'ms' => 'Ms.',
        'n_id' => 'Apo-number of N-Ident registration',
        'name' => 'Name',
        'name_affix' => 'name affix (title)',
        'nearParkingSpace' => 'Near parking space',
        'new_password' => 'New password',
        'new_password_confirmation' => 'Confirm new password',
        'notifications_email' => 'E-Mail for notifications',
        'no' => 'No',
        'old_password' => 'Old password',
        'optional_address_line' => 'Address addition',
        'password' => 'Password',
        'password_confirmation' => 'Confirm password',
        'patient_has_illnesses' => 'Do you have the following diseases?',
        'pharmacies' => 'Apotheken',
        'pharmacy_id' => 'Pharmacy number',
        'pharmacy_name' => 'Pharmacy name',
        'phone' => 'Phone',
        'postcode' => 'Postcode',
        'preparation' => 'Preparation',
        'public_transport_trains' => 'Closest station metro, s-train or train',
        'public_transport_tram' => 'Closest station tram',
        'public_transport_bus' => 'Closest station bus',
        'recommended_solutions' => 'Recommended solution',
        'recoveredCertificate' => 'Recovered vaccination',
        'remember_me' => 'Remember me',
        'second' => 'Second',
        'sex' => 'Sex',
        'size' => 'Size',
        'spokenLanguages' => 'Spoken languages',
        'status' => 'Status',
        'street' => 'Street',
        'telematics_id' => 'Telematics-ID',
        'time' => 'Time',
        'title' => 'Title',
        'type' => 'Special Location',
        'username' => 'Username',
        'vaccination_import' => 'Vaccination import',
        'vaccination_import_city' => 'Vaccination certificate: City',
        'vaccination_import_postcode' => 'Vaccination certificate: Postcode',
        'vaccination_import_street' => 'Vaccination certificate: Street',
        'vaccination_import_house_number' => 'Vaccination certificate: House number',
        'vaccination_import_optional_address_line' => 'Vaccination certificate: Optional Address Line',
        'vaccinationDate' => 'Date of vaccination',
        'vaccine' => 'Vaccine',
        'vaccineHolder' => 'Authorization holder',
        'vaccineTarget' => 'Pathogen',
        'vaccineType' => 'Vaccine type',
        'verified' => 'Verified',
        'website' => 'Website',
        'year' => 'Year',
        'yes' => 'Yes',
        'your_personal_email' => 'E-Mail address for notifications',
        'your_personal_phone' => 'Your personal phone',
        'pharmacyInformation.*.fax' => 'Fax',
        'pharmacyInformation.*.accounting_center' => 'Billing Center',
        'pharmacyInformation.*.ik_number' => 'IK-Number',
        'pharmacyInformation.*.telematik_id' => 'Telematics-ID',
    ],
];
