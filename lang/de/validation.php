<?php

$excelValidationAttributes = [];
foreach (App\Excel\Imports\AssociationMemberImport::COLUMNS as $key => $value) {
    $excelValidationAttributes['*.'.$key] = $value;
}

return [
    'accepted' => ':attribute muss akzeptiert werden.',
    'active_url' => ':attribute ist keine gültige Internet-Adresse.',
    'after' => ':attribute muss ein Datum nach dem :date sein.',
    'after_or_equal' => ':attribute muss ein Datum nach dem :date oder gleich dem :date sein.',
    'alpha' => ':attribute darf nur aus Buchstaben bestehen.',
    'alpha_dash' => ':attribute darf nur aus Buchstaben, Zahlen, Binde- und Unterstrichen bestehen.',
    'alpha_num' => ':attribute darf nur aus Buchstaben und Zahlen bestehen.',
    'array' => ':attribute muss ein Array sein.',
    'before' => ':attribute muss ein Datum vor dem :date sein.',
    'before_or_equal' => ':attribute muss ein Datum vor oder gleich dem :date sein.',
    'between' => [
        'numeric' => ':attribute muss zwischen :min & :max liegen.',
        'file' => ':attribute muss zwischen :min & :max Kilobytes groß sein.',
        'string' => ':attribute muss zwischen :min & :max Zeichen lang sein.',
        'array' => ':attribute muss zwischen :min & :max Elemente haben.',
    ],
    'boolean' => ":attribute muss entweder 'true' oder 'false' sein.",
    'confirmed' => ':attribute stimmt nicht mit der Bestätigung überein.',
    'date' => ':attribute muss ein gültiges Datum sein.',
    'date_equals' => ':attribute muss ein Datum gleich :date sein.',
    'date_format' => ':attribute entspricht nicht dem gültigen Format für :format.',
    'different' => ':attribute und :other müssen sich unterscheiden.',
    'digits' => ':attribute muss :digits Stellen haben.',
    'digits_between' => ':attribute muss zwischen :min und :max Stellen haben.',
    'dimensions' => ':attribute hat ungültige Bildabmessungen.',
    'distinct' => ':attribute beinhaltet einen bereits vorhandenen Wert.',
    'email' => ':attribute muss eine gültige E-Mail-Adresse sein.',
    'ends_with' => ':attribute muss eine der folgenden Endungen aufweisen: :values',
    'exists' => 'Der gewählte Wert für :attribute ist ungültig.',
    'incorrect_owner' => 'Der ausgewählte Nutzer gehört nicht zum aktiven Inhaber.',
    'file' => ':attribute muss eine Datei sein.',
    'filled' => ':attribute muss ausgefüllt sein.',
    'gt' => [
        'numeric' => ':attribute muss größer als :value sein.',
        'file' => ':attribute muss größer als :value Kilobytes sein.',
        'string' => ':attribute muss länger als :value Zeichen sein.',
        'array' => ':attribute muss mehr als :value Elemente haben.',
    ],
    'gte' => [
        'numeric' => ':attribute muss größer oder gleich :value sein.',
        'file' => ':attribute muss größer oder gleich :value Kilobytes sein.',
        'string' => ':attribute muss mindestens :value Zeichen lang sein.',
        'array' => ':attribute muss mindestens :value Elemente haben.',
    ],
    'hex_color' => ':attribute muss folgenden Format entsprechen #000000.',
    'image' => ':attribute muss ein Bild sein.',
    'in' => 'Der gewählte Wert für :attribute ist ungültig.',
    'in_array' => 'Der gewählte Wert für :attribute kommt nicht in :other vor.',
    'integer' => ':attribute muss eine ganze Zahl sein.',
    'ip' => ':attribute muss eine gültige IP-Adresse sein.',
    'ipv4' => ':attribute muss eine gültige IPv4-Adresse sein.',
    'ipv6' => ':attribute muss eine gültige IPv6-Adresse sein.',
    'json' => ':attribute muss ein gültiger JSON-String sein.',
    'lt' => [
        'numeric' => ':attribute muss kleiner als :value sein.',
        'file' => ':attribute muss kleiner als :value Kilobytes sein.',
        'string' => ':attribute muss kürzer als :value Zeichen sein.',
        'array' => ':attribute muss weniger als :value Elemente haben.',
    ],
    'lte' => [
        'numeric' => ':attribute muss kleiner oder gleich :value sein.',
        'file' => ':attribute muss kleiner oder gleich :value Kilobytes sein.',
        'string' => ':attribute darf maximal :value Zeichen lang sein.',
        'array' => ':attribute darf maximal :value Elemente haben.',
    ],
    'max' => [
        'numeric' => ':attribute darf maximal :max sein.',
        'file' => ':attribute darf maximal :max Kilobytes groß sein.',
        'string' => ':attribute darf maximal :max Zeichen haben.',
        'array' => ':attribute darf maximal :max Elemente haben.',
    ],
    'mimes' => ':attribute muss den Dateityp :values haben.',
    'mimetypes' => ':attribute muss den Dateityp :values haben.',
    'min' => [
        'numeric' => ':attribute muss mindestens :min sein.',
        'file' => ':attribute muss mindestens :min Kilobytes groß sein.',
        'string' => ':attribute muss mindestens :min Zeichen lang sein.',
        'array' => ':attribute muss mindestens :min Elemente haben.',
    ],
    'not_in' => 'Der gewählte Wert für :attribute ist ungültig.',
    'not_regex' => ':attribute hat ein ungültiges Format.',
    'numeric' => ':attribute muss eine Zahl sein.',
    'password' => 'Das Passwort ist falsch.',
    'password.mixed' => 'Das Passwort muss Groß-, Kleinbuchstaben, Sonderzeichen und Zahlen enthalten.',
    'old_password' => 'Das aktuelle Passwort ist falsch.',
    'present' => ':attribute muss vorhanden sein.',
    'regex' => ':attribute Format ist ungültig.',
    'required' => ':attribute muss ausgefüllt werden.',
    'required_if' => ':attribute muss ausgefüllt werden, wenn :other den Wert :value hat.',
    'required_unless' => ':attribute muss ausgefüllt werden, wenn :other nicht den Wert :values hat.',
    'required_with' => ':attribute muss ausgefüllt werden, wenn :values ausgefüllt wurde.',
    'required_with_all' => ':attribute muss ausgefüllt werden, wenn :values ausgefüllt wurde.',
    'required_without' => ':attribute muss ausgefüllt werden, wenn :values nicht ausgefüllt wurde.',
    'required_without_all' => ':attribute muss ausgefüllt werden, wenn keines der Felder :values ausgefüllt wurde.',
    'same' => ':attribute und :other müssen übereinstimmen.',
    'size' => [
        'numeric' => ':attribute muss gleich :size sein.',
        'file' => ':attribute muss :size Kilobyte groß sein.',
        'string' => ':attribute muss :size Zeichen lang sein.',
        'array' => ':attribute muss genau :size Elemente haben.',
    ],
    'starts_with' => ':attribute muss mit einem der folgenden Anfänge aufweisen: :values',
    'string' => ':attribute muss ein String sein.',
    'timezone' => ':attribute muss eine gültige Zeitzone sein.',
    'unique' => ':attribute ist bereits vergeben.',
    'uploaded' => ':attribute konnte nicht hochgeladen werden.',
    'url' => ':attribute entspricht nicht den formalen Anforderungen.',
    'uuid' => ':attribute muss ein UUID sein.',
    'invalid_format' => ':attribute hat ein ungültiges Format.',
    'tid_card_type_for_human' => 'Diese Telematik-ID ist nur für Personen gültig. Bitte geben Sie die Telematik-ID Ihrer Institutionskarte (SMC-B) ein.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
        'facebook' => [
            'regex' => 'Facebook: Bitte tragen Sie die vollständige Webseite im Format https://de-de.facebook.com/Apotheke_Am_Markt ein.',
        ],
        'instagram' => [
            'regex' => 'Instagram: Bitte tragen Sie die vollständige Webseite im Format https://www.instagram.com/Apotheke_Am_Markt ein.',
        ],
    ],

    'custom_roles' => [
        'courier-service-presense' => [
            'enabled' => 'Botendienst Radius muss ausgefüllt sein.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => array_merge([
        'selectedUsers' => 'Benutzer',
        'attr.groups' => 'Gruppe',

        'yes' => 'Ja',
        'no' => 'Nein',
        'name' => 'Name',
        'username' => 'Benutzername',
        'email' => 'E-Mail-Adresse',
        'email_confirmation' => 'E-Mail-Adresse bestätigen',
        'login_email' => 'E-Mail für Login',
        'notifications_email' => 'E-Mail für Benachrichtigungen',
        'salutation' => 'Gewünschte Anrede',
        'first_name' => 'Vorname',
        'last_name' => 'Nachname',
        'password' => 'Passwort',
        'old_password' => 'Aktuelles Passwort',
        'password_confirmation' => 'Passwort bestätigen',
        'remember_me' => 'Angemeldet bleiben',
        'city' => 'Ort',
        'contactPerson' => 'Ansprechpartner',
        'country' => 'Land',
        'address' => 'Adresse',
        'phone' => 'Telefonnummer',
        'mobile' => 'Handynummer',
        'age' => 'Alter',
        'sex' => 'Geschlecht',
        'gender' => 'Geschlecht',
        'day' => 'Tag',
        'month' => 'Monat',
        'year' => 'Jahr',
        'hour' => 'Stunde',
        'minute' => 'Minute',
        'second' => 'Sekunde',
        'title' => 'Titel',
        'content' => 'Inhalt',
        'description' => 'Beschreibung',
        'excerpt' => 'Auszug',
        'date' => 'Datum',
        'time' => 'Uhrzeit',
        'size' => 'Größe',
        'date_of_birth' => 'Geburtsdatum',
        'postcode' => 'Postleitzahl',
        'street' => 'Straße',
        'house_number' => 'Hausnummer',
        'houseNumber' => 'Hausnummer',
        'optional_address_line' => 'Adresszusatz',
        'fax' => 'Fax',
        'website' => 'Website',
        'status' => 'Status',
        'current_status' => 'Aktueller Status',
        'verified' => 'Verifiziert',
        'association' => 'Hauptverband',
        'in_association' => 'Sind Sie Mitglied in einem Verband?',
        'pharmacy_id' => 'Apothekennummer',
        'pharmacy_name' => 'Name der Apotheke',
        'pharmacyName' => 'Name der Apotheke',
        'your_personal_email' => 'E-Mail-Adresse für Benachrichtigungen',
        'your_personal_phone' => 'Ihre persönliche Telefonnummer',
        'delivery' => 'Botendienst',
        'focusAreas' => 'Weitere Leistungen',
        'businessHours' => 'Öffnungszeiten',
        'spokenLanguages' => 'Gesprochene Sprachen',
        'nearParkingSpace' => 'Parkmöglichkeit an der Apotheke',
        'type' => 'Besondere Lage',
        'goodsManagementSystem' => 'Warenwirtschaftssystem',
        'accountingCenter' => 'Abrechnungszentrum',
        'mr' => 'Herr',
        'ms' => 'Frau',
        'di' => 'Divers',
        'courier_service_radius' => 'Botendienst Radius',
        'institute_id' => 'Institutionskennzeichen',
        'commercial_register' => 'Handelsregister',
        'facebook' => 'Facebook',
        'instagram' => 'Instagram',
        'n_id' => 'Aponummer der N-Ident Registrierung',
        'telematics_id' => 'Telematik-ID',
        'telematicsId' => 'Telematik-ID',
        'goods_management_system_id' => 'Warenwirtschaftssystem',
        'accounting_center_id' => 'Abrechnungszentrum',
        'public_transport_trains' => 'Haltestelle U-Bahn, S-Bahn oder Zug',
        'public_transport_tram' => 'Haltestelle Straßenbahn',
        'public_transport_bus' => 'Haltestelle Bus',
        'new_password' => 'Neues Passwort',
        'new_password_confirmation' => 'Neues Passwort bestätigen',
        'data_protection' => 'Datenschutzbestimmung',
        'data_protection_text' => 'Ich akzeptiere die Datenschutzbestimmungen und Nutzungsbedingungen',
        'custom_goods_management_system' => 'Benutzerdefiniertes Warenwirtschaftssystem',
        'custom_accounting_center' => 'Benutzerdefiniertes Abrechnungszentrum',
        'privacy_person_name' => 'Name des Datenschützers',
        'insurance_number' => 'Versichertennummer',
        'birthdate' => 'Geburtsdatum',
        'patient_has_illness' => 'Akute Erkrankung',
        'patient_has_illness_vaccination_possible' => 'Impfbarkeit trotz akuter Erkrankung',
        'patient_has_allergy' => 'Allergie',
        'patient_has_allergy_vaccination_possible' => 'Impfbarkeit trotz Allergie',
        'patient_had_reaction' => 'Allergische Reaktionen',
        'patient_had_reaction_vaccination_possible' => 'Impfbarkeit trotz allergischer Reaktion',
        'patient_has_operation' => 'Operation',
        'patient_has_operation_vaccination_possible' => 'Impfbarkeit trotz Operation',
        'patient_takes_marcumar' => 'Blutgerinnung',
        'patient_is_pregnant' => 'Schwangerschaft',
        'group_stiko_health' => 'STIKO gesundheitliche Indikation',
        'group_stiko_job' => 'STIKO berufliche Indikation',
        'questions' => 'Weiteren Fragen',
        'acceptance' => 'Einverstanden',
        'noAcceptance' => 'Nicht einverstanden',
        'notes' => 'Anmerkungen',
        'poll_vaccinated_before.*' => 'vorherige Influenza Impfung',
        'poll_where_found_out.*' => 'vom Impfangebot der Apotheke erfahren',
        'poll_had_alternative' => 'zum Arzt oder zum Gesundheitsamt gegangen',
        'poll_why_pharmacy.*' => 'warum in der Apotheke impfen lassen',
        'poll_rate_information' => 'Informationsbewertung',
        'poll_rate_satisfaction' => 'Zufriedenheit',
        'poll_rate_do_again' => 'nochmal in der Apotheke impfen lassen',
        'poll_rate_others_too' => 'auch gegen andere Erkrankungen in der Apotheke impfen lassen',
        'comments' => 'Kommentar/Verbesserungsvorschläge',
        'poll_why_pharmacy_other' => 'Sonstiges',
        'poll_where_found_out_other' => 'Sonstiges',
        'severe_vaccination_reactions.*' => 'schwerwiegende Impfreaktion',
        'emergency_measures' => 'eingeleitete Notfallmaßnahmen',
        'belated_reactions.*' => 'schwerwiegende Impfreaktion',
        'pharmacies' => 'Apotheken',
        'corona_rapid_test' => 'Corona-Schnelltests',
        'corona_rapid_test_booking_url' => 'Link zur Terminbuchung',
        'slug' => 'URL Kürzel',
        'text' => 'Text',
        'release_date' => 'Verlöffentlichungsdatum',
        'shipping_pharmacy_name' => 'Name des Versandhandels',
        'shipping_pharmacy_website' => 'Website des Versandhandels',
        'shipping_pharmacy_enabled' => 'Inländischer Versandhandel',
        'firstName' => 'Vorname',
        'lastName' => 'Nachname',
        'vaccine' => 'Impfstoff',
        'vaccineTarget' => 'Erreger',
        'vaccineType' => 'Impfstofftyp',
        'vaccineHolder' => 'Zulassungsinhaber',
        'doseNumber' => 'Nummer der Dosis',
        'doseCount' => 'Gesamtzahl Dosen',
        'vaccinationDate' => 'Datum der Impfung',
        'vaccination_import' => 'COVID-19-Zertifikat',
        'vaccination_import_city' => 'Impfzertifikat: Ort',
        'vaccination_import_postcode' => 'Impfzertifikat: Postleitzahl',
        'vaccination_import_street' => 'Impfzertifikat: Straße',
        'vaccination_import_house_number' => 'Impfzertifikat: Hausnummer',
        'vaccination_import_optional_address_line' => 'Impfzertifikat: Adresszusatz',
        'code' => 'Registrierungscode',
        'recoveredCertificate' => 'Genesenen-Impfung',
        'subject' => 'Betreff',
        'reason' => 'Anliegen',
        'dataProtection' => 'Datenschutz',
        'home_visit' => 'Hausbesuch',
        'home_visit_type' => 'Hausbesuchsart',
        'vaccination_type' => 'Impfserie',
        'pharmaceutical_id' => 'Impfstoff',
        'batch_number' => 'Chargennummer',
        'export_start' => 'Start',
        'export_end' => 'Ende',
        'start' => 'Start',
        'end' => 'Ende',
        'calendar_email' => 'E-Mail-Adresse für Terminbuchungen',
        'acceptNewPayments' => 'Die Nutzungsbedingung',
        'tosCheckboxAccepted' => 'Die Nutzungsbedingung',
        'tosDeclineCheckboxAccepted' => 'Die Bedingung',
        'paymentCheckboxAccepted' => 'Die Bedingung',
        'avvCheckboxAccepted' => 'Die Bedingung',
        'avvDeclineCheckboxAccepted' => 'Die Bedingung',
        'downloaded' => 'Die Bedingung',
        'datetime' => 'Datum & Uhrzeit',
        'recommended_solutions' => 'Empfohlene Maßnahme',
        'device_condition' => 'Zustand des Gerätes',
        'preparation' => 'Vorbereitung',
        'inhalation' => 'Inhalation',
        'ending' => 'Beenden',
        'patient_has_illnesses' => 'Haben Sie folgende Erkrankungen?',
        'company_name' => 'Name der OHG',
        'companyName' => 'Name der OHG',
        'is_company' => 'Das Flag, ob es sich um eine OHG handelt',
        'name_affix' => 'Der Namenszusatz (Titel)',
        'consentForm' => 'Die Einverständniserklärung',
        'postal_code' => 'Postleitzahl',
        'company' => 'Firmenname',
        'state' => 'Bundesland',
        'apo_mail_email' => 'ApoMail Adresse',
        'alternative_email' => 'Alternative E-Mail-Adresse',
        'user_id' => 'Nutzer',
        'attr' => [
            'name' => 'Name',
            'description' => 'Beschreibung',
            'hard_quota' => 'maximaler Speicherplatz',
        ],
        'pharmaceutical' => 'Arzneimittel (Wirkstoffe)',
        'inhalation_system' => 'Inhalationssystem',
        'inhalation_system_other_text' => 'Sonstiges Inhalationssystem',
        'others' => 'Sonstiges',
        'given_name' => 'Vorname',
        'family_name' => 'Nachname',
        'email_verified' => 'Wiederholung der E-Mail-Adresse',
        'phone_number_verified' => 'Wiederholung der Telefonnummer',
        'phone_number' => 'Telefonnummer',
        'groups' => 'Gruppen',
        'password_hash' => 'Passwortverschlüsselung',
        'accept_terms_and_conditions_required' => 'Akzeptanz der Nutzungsbedingungen',
        'change_password_required' => 'Nötige Änderung des Passworts',
        'uuid' => 'UUID',
        'operatingLicense' => 'Kopie der Betriebserlaubnis',
        'activityCertificate' => 'Aktivitätsnachweis',
        'associationId' => 'Verbandsmitgliedschaft',
        'associationProof' => 'Verbandsmitgliedschaft-Nachweis',
        'health_insurance_company_id' => 'Krankenversicherung',
        'break_duration' => 'Pause',
        'color' => 'Farbe',
        'weekly_working_hours' => 'Wochenstunden',
        'group_user_id' => 'Nutzer',
        'groups.*.name' => 'Gruppenname',
        'selectedUsers.*.user_id' => 'Mitarbeiter',
        'selectedUsers.*.groups' => 'Gruppe',
        'billingAddress' => 'Rechnungsadresse',
        'pharmacyInformation.*.fax' => 'Fax',
        'pharmacyInformation.*.accounting_center' => 'Abrechnungszentrum',
        'pharmacyInformation.*.ik_number' => 'IK-Nummer',
        'pharmacyInformation.*.telematik_id' => 'Telematik-ID',
        'ik_number' => 'IK-Nummer',

    ], $excelValidationAttributes),

];
