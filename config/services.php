<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'ibm' => [
        'vaccination-center' => [
            'private-key-password' => env('IBM_VACCINATION_CENTER_PRIVATE_KEY_PASSWORD'),
            'uuid' => env('IBM_VACCINATION_CENTER_UUID', 'DAVDEMO'),
            'use-pharmacy-uuid' => env('IBM_VACCINATION_CENTER_USE_PHARMACY_UUID', false),
            'url' => env('IBM_VACCINATION_CENTER_URL', 'https://api.certify.demo.ubirch.com/api/certify/v2/issue'),
            'throttle' => env('IBM_VACCINATION_THROTTLE', 0),
            'timeout' => env('IBM_VACCINATION_TIMEOUT', 5),
        ],
    ],

    'vaccination-portal' => [
        'url' => env('DAV_VACCINATION_PORTAL_URL', 'http://dav.impfnachweis.info'),
    ],

    'mein-apothekenportal' => [
        'domain' => env('MEIN_APOTHEKENPORTAL_DOMAIN', 'https://www.mein-apothekenportal.de'),
        'client-id' => env('MEIN_APOTHEKENPORTAL_CLIENT_ID'),
        'client-secret' => env('MEIN_APOTHEKENPORTAL_CLIENT_SECRET'),
        'cache-duration' => env('MEIN_APOTHEKENPORTAL_CACHE_DURATION', 18000),
        'distance' => env('MEIN_APOTHEKENPORTAL_distance', 30),
    ],

    'notifications-api' => [
        'endpoint' => [
            'url' => env('NOTIFICATIONS_API_ENDPOINT_URL'),
            'token' => env('NOTIFICATIONS_API_ENDPOINT_TOKEN'),
        ],
        'self' => [
            'token' => env('NOTIFICATIONS_API_TOKEN'),
        ],
    ],

    'rki-dim-api' => [
        'url' => env('RKI_DIM_URL'),
        'certificate-password' => env('RKI_DIM_CERTIFICATE_PASSWORD'),
        'client-id' => env('RKI_DIM_CLIENT_ID'),
        'client-secret' => env('RKI_DIM_CLIENT_SECRET'),
        'username' => env('RKI_DIM_USERNAME'),
        'password' => env('RKI_DIM_PASSWORD'),
    ],

    'apomondo' => [
        'url' => env('APOMONDO_URL'),
        'patient_url' => env('APOMONDO_PATIENT_URL'),
        'token' => env('APOMONDO_KEY'),
        'audience' => env('APOMONDO_JWT_AUDIENCE', 'staging-api.apomondo.online'),
        'public_key_path' => env('JWT_APOMONDO_PUBLIC_KEY_PATH', 'apomondo_pharmacy_public_key.pem'),
        'private_key_path' => env('APOMONDO_PRIVATE_KEY_PATH', 'apomondo_pharmacy_private_key.pem'),
        'pharmacies_issuer' => env('PHARMACIES_JWT_ISSUER', 'token-service'),
        'pharmacies_audience' => env('PHARMACIES_JWT_AUDIENCE', 'pharmacies'),
        'pharmacies_private_key_path' => env('JWT_PHARMACIES_TOKEN_PRIVATE_KEY', 'pharmacies_token_private_key.pem'),
        'pharmacies_public_key_path' => env('JWT_PHARMACIES_TOKEN_PLUBLIC_KEY', 'pharmacies_token_public_key.pem'),
    ],

    'mailcoach' => [
        'enabled' => env('MAILCOACH_ENABLED', false),
        'url' => env('MAILCOACH_URL', 'host.docker.internal/mailcoach/api'),
        'token' => env('MAILCOACH_TOKEN', '**********'),
        'list_id' => env('MAILCOACH_LIST_ID', '1'),
    ],

    'horizon' => [
        'token' => env('HORIZON_TOKEN'),
    ],

    'token-service' => [
        'url' => env('SERVICES_TOKEN_SERVICE_URL', ''),
        'import-token' => env('SERVICES_TOKEN_SERVICE_IMPORT_TOKEN'),
    ],

    'apomail' => [
        'url' => env('APOMAIL_URL'),
        'active' => (bool) env('APOMAIL_ACTIVE', true),
        'certificate_name' => env('APOMAIL_CERTIFICATE_NAME'),
        'admin_login_expiring_minutes' => env('APOMAIL_ADMIN_LOGIN_COOKIE_EXPIRING_MINUTES'),
        'admin' => [
            'username' => env('APOMAIL_ADMIN_USERNAME'),
            'password' => env('APOMAIL_ADMIN_PASSWORD'),
        ],
    ],

    'paul-ehrlich-institut' => [
        'bottleneck-url' => env(
            'PAUL_EHRLICH_INSTITUT_DELIVERY_BOTTLENECK_URL',
            'https://www.pei.de/DE/arzneimittel/impfstoffe/lieferengpaesse/lieferengpaesse-node.html?cms_tabcounter=0'
        ),
    ],

    'ngda' => [
        'url' => env('NGDA_IDP_URL'),
        'authorization_url' => env('NGDA_IDP_AUTHORIZE_URL'),
        'token_url' => env('NGDA_IDP_TOKEN_URL'),
        'client_id' => env('NGDA_IDP_CLIENT_ID'),
        'client_secret' => env('NGDA_IDP_CLIENT_SECRET'),
        'recipient_id' => env('NGDA_RECIPIENT_ID'),
        'logout_url' => env('NGDA_IDP_LOGOUT_URL'),
    ],

    'nnf' => [
        'url' => env('NNF_API_URL'),
    ],

    'consent' => [
        'url' => env('CONSENT_SERVICE_URL'),
        'use' => env('USE_CONSENT_SERVICE', true),
    ],

    'ia-update-pharmacy' => [
        'env' => env('IA_UPDATE_PHARMACY_DATA_API_ENV', 'dev'),
        'dev' => [
            'url' => env('IA_UPDATE_PHARMACY_DATA_API_URL_DEV'),
            'key' => env('IA_UPDATE_PHARMACY_DATA_API_URL_DEV_KEY'),
        ],
        'qa' => [
            'url' => env('IA_UPDATE_PHARMACY_DATA_API_URL_QA'),
            'key' => env('IA_UPDATE_PHARMACY_DATA_API_URL_QA_KEY'),
        ],
        'prod' => [
            'url' => env('IA_UPDATE_PHARMACY_DATA_API_URL_PRODUCTION'),
            'key' => env('IA_UPDATE_PHARMACY_DATA_API_URL_PRODUCTION_KEY'),
        ],
    ],
    'ia-partner' => [
        'env' => env('IA_PARTNER_API_ENV'),
        'key' => env('IA_PARTNER_API_KEY'),
        'dev' => [
            'url' => env('IA_PARTNER_API_DEV_URL'),
        ],
        'qa' => [
            'url' => env('IA_PARTNER_API_QA_URL'),
        ],
        'prod' => [
            'url' => env('IA_PARTNER_API_PRODUCTION_URL'),
        ],
    ],
    'ia-web-components' => [
        'env' => env('IA_WEB_COMPONENTS_ENV', 'qa'),
    ],

    'subscription-service' => [
        'url' => env('SUBSCRIPTION_SERVICE_URL'),
        'token' => env('SUBSCRIPTION_SERVICE_TOKEN'),
    ],

    'card-link-service' => [
        'url' => env('CARD_LINK_SERVICE_URL'),
        'client-id' => env('CARD_LINK_SERVICE_CLIENT_ID'),
        'client-secret' => env('CARD_LINK_SERVICE_CLIENT_SECRET'),
        'client-id-vendor' => env('CARD_LINK_SERVICE_CLIENT_ID_VENDOR'),
        'client-secret-vendor' => env('CARD_LINK_SERVICE_CLIENT_SECRET_VENDOR'),

        'apoguide' => [
            'channel-id' => env('CARD_LINK_APOGUIDE_CHANNEL_ID'),
        ],
    ],

    'ti-gateway' => [
        'url' => env('TI_GATEWAY_URL', 'https://ehealth.akquinet.de/gedisa-ti-gateway'),
    ],

    'retax' => [
        'url' => env('RETAX_URL', 'https://staging.retax.gedisa.de'),
        'audience' => env('RETAX_AUDIENCE', 'retax'),
    ],

    'jwt' => [
        'private_key_path' => env('JWT_PHARMACIES_TOKEN_PRIVATE_KEY', 'pharmacies_token_private_key.pem'),
    ],
];
