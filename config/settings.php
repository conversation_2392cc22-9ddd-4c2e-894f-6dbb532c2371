<?php

use App\Settings\NovaCasts\DateTimeNovaCast;

return [

    /*
     * Each settings class used in your application must be registered, you can
     * put them (manually) here.
     */
    'settings' => [
        \App\Settings\ApoMailSettings::class,
        \App\Settings\AppSettings::class,
        \App\Settings\ChatSettings::class,
        \App\Settings\KimSettings::class,
        \App\Settings\PharmaceuticalServiceSettings::class,
        \App\Settings\RkiSettings::class,
        \App\Settings\NgdaSettings::class,
        \App\Settings\IaSettings::class,
        \App\Settings\CardLinkSettings::class,
        \App\Settings\ShiftPlanSettings::class,
        \App\Settings\RiseSettings::class,
        \App\Settings\SubscriptionSettings::class,
        \App\Settings\TermsOfServiceSettings::class,
        \App\Settings\ApoGuideSettings::class,
        \App\Settings\ApomondoSettings::class,
        \App\Settings\MailcoachSettings::class,
        \App\Settings\NovaSqlQuerySettings::class,
        \App\Domains\Subscription\Application\Settings\TaxStripeSetting::class,
        \App\Domains\Subscription\Application\Settings\TemplateStripeSetting::class,
        \App\Settings\TiGatewaySettings::class,
        \App\Settings\PlausibleSettings::class,
        \App\Settings\RetaxSettings::class,
        \App\Settings\JwtSettings::class,

        /**
         * Subscription Product Settings
         */
        \App\Domains\Subscription\Application\Settings\Products\BaseProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\CalendarProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\CardLinkProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\IAProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\KimProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\ShiftPlanProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\TimProductSetting::class,
        \App\Domains\Subscription\Application\Settings\Products\RetaxProductSetting::class,

        /**
         * Discount Settings
         */
        \App\Domains\Subscription\Application\Settings\Discounts\CardLinkPartnerPharmacyDiscountSetting::class,
        \App\Domains\Subscription\Application\Settings\Discounts\BaseSubscriptionAssociationPaidDiscountSetting::class,
    ],

    /*
     * The path where the settings classes will be created.
     */
    'setting_class_path' => app_path('Settings'),

    /*
     * In these directories settings migrations will be stored and ran when migrating. A settings
     * migration created via the make:settings-migration command will be stored in the first path or
     * a custom defined path when running the command.
     */
    'migrations_paths' => [
        database_path('settings'),
    ],

    /*
     * When no repository was set for a settings class the following repository
     * will be used for loading and saving settings.
     */
    'default_repository' => 'database',

    /*
     * Settings will be stored and loaded from these repositories.
     */
    'repositories' => [
        'database' => [
            'type' => Spatie\LaravelSettings\SettingsRepositories\DatabaseSettingsRepository::class,
            'model' => null,
            'table' => 'system_settings',
            'connection' => null,
        ],
        'redis' => [
            'type' => Spatie\LaravelSettings\SettingsRepositories\RedisSettingsRepository::class,
            'connection' => null,
            'prefix' => null,
        ],
    ],

    /*
     * The contents of settings classes can be cached through your application,
     * settings will be stored within a provided Laravel store and can have an
     * additional prefix.
     */
    'cache' => [
        'enabled' => env('SETTINGS_CACHE_ENABLED', false),
        'store' => null,
        'prefix' => null,
        'ttl' => null,
    ],

    /*
     * These global casts will be automatically used whenever a property within
     * your settings class isn't a default PHP type.
     */
    'global_casts' => [
        DateTimeInterface::class => Spatie\LaravelSettings\SettingsCasts\DateTimeInterfaceCast::class,
        DateTimeZone::class => Spatie\LaravelSettings\SettingsCasts\DateTimeZoneCast::class,
        //        Spatie\DataTransferObject\DataTransferObject::class => Spatie\LaravelSettings\SettingsCasts\DtoCast::class,
        //        Spatie\LaravelData\Data::class => Spatie\LaravelSettings\SettingsCasts\DataCast::class,
    ],

    'nova_fields' => [
        DateTimeInterface::class => DateTimeNovaCast::class,
    ],

    /*
     * The package will look for settings in these paths and automatically
     * register them.
     */
    'auto_discover_settings' => [
        // auto discover must be switched off and all setting classes must be defined above so that they can be resolved by Nova
    ],

    /*
     * Automatically discovered settings classes can be cached, so they don't
     * need to be searched each time the application boots up.
     */
    'discovered_settings_cache_path' => base_path('bootstrap/cache'),
];
