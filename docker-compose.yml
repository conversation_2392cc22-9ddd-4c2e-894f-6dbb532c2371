# For more information: https://laravel.com/docs/sail
services:
    mein-apothekenportal.test:
        build:
            context: ./vendor/laravel/sail/runtimes/8.3
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
                NODE_VERSION: '22'
        image: sail-8.3/app
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            PHP_IDE_CONFIG: "serverName=mein-apothekenportal.test"
        volumes:
            - '.:/var/www/html'
            - './999-sail-overrides.ini:/etc/php/8.3/cli/conf.d/999-sail-overrides.ini'
        networks:
            - sail
        depends_on:
            - mysql
            - redis
        extra_hosts:
          - "host.docker.internal:host-gateway"
    mysql:
        image: 'mysql:8.0-oracle'
        # platform added
        platform: linux/amd64
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
        volumes:
            - 'sailmysql:/var/lib/mysql'
        networks:
            - sail
        healthcheck:
          test: ["CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
          retries: 3
          timeout: 5s
    mailhog:
        image: 'mailhog/mailhog:latest'
        ports:
            - '${FORWARD_MAILHOG_PORT:-1025}:1025'
            - '${FORWARD_MAILHOG_DASHBOARD_PORT:-8025}:8025'
        networks:
            - sail
    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sailredis:/data'
        networks:
            - sail
        healthcheck:
          test: ["CMD", "redis-cli", "ping"]
          retries: 3
          timeout: 5s
    minio:
      image: 'minio/minio:latest'
      ports:
        - '${FORWARD_MINIO_PORT:-9000}:9000'
        - '${FORWARD_MINIO_CONSOLE_PORT:-8900}:8900'
      environment:
        MINIO_ROOT_USER: 'sail'
        MINIO_ROOT_PASSWORD: 'password'
      volumes:
        - 'sailminio:/data/minio'
      networks:
        - sail
      command: minio server /data/minio --console-address ":8900"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
        retries: 3
        timeout: 5s
#    mysql-keycloak:
#      image: 'mysql:8.0'
#      ports:
#        - 3308:3306
#      environment:
#        MYSQL_ROOT_PASSWORD: 'keycloak'
#        MYSQL_DATABASE: 'keycloak'
#        MYSQL_USER: 'keycloak'
#        MYSQL_PASSWORD: 'keycloak'
#        MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
#      volumes:
#        - 'sailmysql-keycloak:/var/lib/mysql'
#      networks:
#        - sail
#      healthcheck:
#        test: [ "CMD", "mysqladmin", "ping", "-p keycloak" ]
#        retries: 3
#        timeout: 5s
#    keycloak:
#      image: keycloak/keycloak:20.0
#      environment:
#        DB_VENDOR: '${DB_CONNECTION}'
#        DB_ADDR: 'mysql-keycloak'
#        DB_USERNAME: 'keycloak'
#        DB_PASSWORD: 'keycloak'
#        KEYCLOAK_ADMIN: '${KEYCLOAK_ADMIN}'
#        KEYCLOAK_ADMIN_PASSWORD: '${KEYCLOAK_ADMIN_PASSWORD}'
#      ports:
#        - 8080:8080
#      volumes:
#        - './keycloak/conf:/opt/keycloak/conf'
#        - './keycloak/realms:/opt/keycloak/data/h2'
#        - './keycloak/themes:/opt/keycloak/themes'
#        - './keycloak/realm-export.json:/opt/keycloak/data/import/realm-export.json'
#      command:
#        - start-dev --import-realm --features=declarative-user-profile
#      networks:
#        - sail
#      depends_on:
#        - mysql-keycloak
#    chrome:
#      image: selenium/standalone-chrome:latest
#      platform: linux/amd64
#      restart: always
#      hostname: chrome
#      networks:
#        - sail
#      privileged: true
#      shm_size: 2g
#      ports:
#        - 4444:4444
#        - 5900:5900
#        - 7900:7900
#      environment:
#        - SE_NODE_OVERRIDE_MAX_SESSIONS=true
#        - SE_NODE_MAX_SESSIONS=100
#      volumes:
#        - './${SELENIUM_FAILURES_SCREENSHOTS_PATH}:/var/www/html/${SELENIUM_FAILURES_SCREENSHOTS_PATH}'
#        - './${SELENIUM_FAILURES_VIDEOS_PATH}:/var/www/html/{SELENIUM_FAILURES_VIDEOS_PATH}'

networks:
    sail:
        driver: bridge
volumes:
    sailmysql:
        driver: local
    sailredis:
        driver: local
    sailminio:
        driver: local
    sailkeycloak:
      driver: local
    sailmysql-keycloak:
      driver: local
